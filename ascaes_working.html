<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASCAES - Academic Document Generation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .main-content { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .panel { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .panel h2 { color: #333; margin-bottom: 20px; font-size: 1.5em; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 12px; border: 2px solid #e1e1e1; border-radius: 6px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus { outline: none; border-color: #667eea; }
        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .status { padding: 15px; border-radius: 6px; margin: 15px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .progress-bar { width: 100%; height: 20px; background: #e1e1e1; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s; }
        .features { grid-column: 1 / -1; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-top: 30px; }
        .features h2 { text-align: center; margin-bottom: 30px; color: #333; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .feature { text-align: center; padding: 20px; }
        .feature-icon { font-size: 3em; margin-bottom: 15px; }
        .feature h3 { color: #667eea; margin-bottom: 10px; }
        #output { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 ASCAES</h1>
            <p>Academic Scholarly Content & Analysis Expert System</p>
            <p>AI-Powered Document Generation with 8 Writing Styles • LaTeX Support • Offline Processing</p>
        </div>

        <div class="main-content">
            <div class="panel">
                <h2>📝 Generate Academic Document</h2>
                <div class="form-group">
                    <label for="topic">Document Topic:</label>
                    <input type="text" id="topic" placeholder="e.g., Quantum Computing in Machine Learning" />
                </div>
                <div class="form-group">
                    <label for="pages">Target Pages:</label>
                    <select id="pages">
                        <option value="1">1 Page (Quick Essay)</option>
                        <option value="3">3 Pages (Short Paper)</option>
                        <option value="5" selected>5 Pages (Standard Paper)</option>
                        <option value="8">8 Pages (Extended Paper)</option>
                        <option value="15">15 Pages (Research Paper)</option>
                        <option value="25">25 Pages (Thesis Chapter)</option>
                        <option value="50">50 Pages (Comprehensive Study)</option>
                        <option value="100">100+ Pages (Large Document)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="style">Writing Style:</label>
                    <select id="style">
                        <option value="analytical">Analytical</option>
                        <option value="instructional">Instructional</option>
                        <option value="reporting">Reporting</option>
                        <option value="argumentative" selected>Argumentative</option>
                        <option value="descriptive">Descriptive</option>
                        <option value="comparative">Comparative</option>
                        <option value="narrative">Narrative</option>
                        <option value="expository">Expository</option>
                    </select>
                </div>
                <button class="btn" onclick="generateDocument()" id="generateBtn">
                    🚀 Generate Document
                </button>
                
                <div id="status"></div>
                <div id="progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <p id="progressText">Initializing...</p>
                </div>
            </div>

            <div class="panel">
                <h2>📊 Generation Output</h2>
                <div id="output">
                    <p><strong>Ready to generate documents!</strong></p>
                    <p>• All 8 specialized agents initialized ✅</p>
                    <p>• Real LLM integration active ✅</p>
                    <p>• LaTeX and PDF output supported ✅</p>
                    <p>• Humanization and AI detection avoidance ✅</p>
                    <p>• Progress tracking enabled ✅</p>
                    <br>
                    <p>Enter your topic and click "Generate Document" to start!</p>
                </div>
            </div>
        </div>

        <div class="features">
            <h2>🌟 ASCAES Features</h2>
            <div class="feature-grid">
                <div class="feature">
                    <div class="feature-icon">🤖</div>
                    <h3>8 Specialized Agents</h3>
                    <p>Planning, Research, Writing, LaTeX, Visual, Quality, Humanizer, and Assembly agents working together</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📚</div>
                    <h3>8 Writing Styles</h3>
                    <p>Analytical, Instructional, Reporting, Argumentative, Descriptive, Comparative, Narrative, Expository</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3>Offline Processing</h3>
                    <p>Complete local processing with no external API dependencies for privacy and security</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📄</div>
                    <h3>Multiple Formats</h3>
                    <p>LaTeX, PDF, RTF, and TXT output formats with professional academic formatting</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <h3>AI Detection Avoidance</h3>
                    <p>Advanced humanization techniques to create natural, undetectable academic content</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>Real-time Progress</h3>
                    <p>Live progress tracking with detailed phase information and time estimates</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        let currentSessionId = null;
        let progressInterval = null;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateOutput(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<p><strong>[${timestamp}]</strong> ${message}</p>`;
            output.scrollTop = output.scrollHeight;
        }

        function updateProgress(progress, phase, message) {
            const progressDiv = document.getElementById('progress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressDiv.style.display = 'block';
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${progress}% - ${phase}: ${message}`;
        }

        async function generateDocument() {
            const topic = document.getElementById('topic').value.trim();
            const pages = document.getElementById('pages').value;
            const style = document.getElementById('style').value;
            const generateBtn = document.getElementById('generateBtn');

            if (!topic) {
                showStatus('Please enter a document topic!', 'error');
                return;
            }

            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 Generating...';
            
            updateOutput(`Starting generation: ${pages}-page ${style} document about "${topic}"`);
            showStatus('Initializing document generation...', 'info');

            try {
                // Start generation
                const response = await fetch(`${API_BASE}/api/chat/generate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: `Generate a ${pages} page ${style} academic document about ${topic}.`,
                        conversation_id: 1
                    })
                });

                const result = await response.json();
                currentSessionId = result.session_id;
                
                updateOutput(`✅ Generation started! Session ID: ${currentSessionId}`);
                updateOutput(`📝 ${result.response}`);
                showStatus('Document generation in progress...', 'success');

                // Start progress monitoring
                startProgressMonitoring();

            } catch (error) {
                updateOutput(`❌ Error: ${error.message}`);
                showStatus('Generation failed. Please try again.', 'error');
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 Generate Document';
            }
        }

        async function startProgressMonitoring() {
            if (!currentSessionId) return;

            progressInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/api/agents/generate/${currentSessionId}`);
                    const status = await response.json();

                    updateProgress(status.progress, status.phase, status.message);
                    updateOutput(`📊 Progress: ${status.progress}% - ${status.phase}: ${status.message}`);

                    if (status.status === 'completed') {
                        clearInterval(progressInterval);
                        updateOutput('🎉 Document generation completed successfully!');
                        showStatus('Document generation completed! Check the output above.', 'success');
                        
                        const generateBtn = document.getElementById('generateBtn');
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 Generate Document';
                        
                        updateProgress(100, 'completed', 'Document ready for download');
                    } else if (status.status === 'failed') {
                        clearInterval(progressInterval);
                        updateOutput('❌ Document generation failed.');
                        showStatus('Generation failed. Please try again.', 'error');
                        
                        const generateBtn = document.getElementById('generateBtn');
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 Generate Document';
                    }

                } catch (error) {
                    updateOutput(`⚠️ Progress check error: ${error.message}`);
                }
            }, 3000); // Check every 3 seconds
        }

        // Test backend connection on page load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE}/api/health/`);
                const health = await response.json();
                updateOutput(`✅ Connected to ASCAES backend (${health.status})`);
                updateOutput(`🔧 Version: ${health.version}`);
                showStatus('ASCAES is ready for document generation!', 'success');
            } catch (error) {
                updateOutput(`❌ Backend connection failed: ${error.message}`);
                showStatus('Backend not available. Please ensure ASCAES is running on port 8001.', 'error');
            }
        };
    </script>
</body>
</html>

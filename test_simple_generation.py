#!/usr/bin/env python3
"""
Simple test to check model generation
"""

import requests
import json
import time

def test_simple_generation():
    """Test simple generation with minimal settings"""
    
    print("🚀 Testing simple model generation...")
    
    # Very simple prompt
    simple_prompt = "Write a 500-word academic essay about Bangladesh economy. Include specific details about GDP, industries, and economic challenges."
    
    try:
        print("📝 Testing with simple prompt...")
        
        # Test with minimal options using Llama 3.2
        response = requests.post("http://localhost:11434/api/generate", json={
            "model": "llama3.2:3b",
            "prompt": simple_prompt,
            "stream": False,
            "options": {
                "num_predict": 800,  # Conservative token count
                "temperature": 0.7
            }
        }, timeout=60)  # 1 minute timeout
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result.get("response", "")
            word_count = len(generated_text.split())
            
            print(f"✅ Generation successful!")
            print(f"📊 Word count: {word_count}")
            print(f"📝 Content preview:")
            print("-" * 50)
            print(generated_text[:300] + "..." if len(generated_text) > 300 else generated_text)
            print("-" * 50)
            
            if word_count > 100:
                print("✅ Model is generating substantial content")
                return True
            else:
                print("❌ Model is generating very short content")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out - model may be stuck")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_backend_generation():
    """Test backend generation endpoint"""
    
    print("\n🚀 Testing backend generation endpoint...")
    
    try:
        # Test backend endpoint
        response = requests.post("http://localhost:8000/api/chat/generate", json={
            "message": "Generate 1000 words academic document about Bangladesh economy.",
            "conversation_id": 1,
            "model": "llama3.2:3b",
            "settings": {
                "maxTokens": 1500,
                "temperature": 0.7,
                "topP": 0.9,
                "humanizationLevel": "conservative"
            }
        }, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            session_id = result.get("session_id")
            print(f"✅ Backend generation started: {session_id}")
            
            # Check status a few times
            for i in range(5):
                time.sleep(10)
                status_response = requests.get(f"http://localhost:8000/api/agents/generate/{session_id}")
                if status_response.status_code == 200:
                    status = status_response.json()
                    print(f"Status: {status.get('progress', 0)}% - {status.get('message', 'Processing...')}")
                    
                    if status.get('status') == 'completed':
                        print("✅ Backend generation completed!")
                        return True
                    elif status.get('status') == 'error':
                        print(f"❌ Backend generation failed: {status.get('message')}")
                        return False
            
            print("⚠️ Backend generation still in progress...")
            return None
        else:
            print(f"❌ Backend HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend Error: {e}")
        return False

if __name__ == "__main__":
    # Test model directly first
    model_works = test_simple_generation()
    
    if model_works:
        print("\n" + "="*50)
        # Test backend if model works
        test_backend_generation()
    else:
        print("\n❌ Model test failed - skipping backend test")

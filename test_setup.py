#!/usr/bin/env python3
"""
ASCAES Test Setup and Verification Script
Comprehensive testing of the multi-agent system
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

async def test_basic_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing basic imports...")
    
    try:
        # Test core imports
        from core.config import settings
        from core.logging_config import get_logger
        from core.database import SessionLocal
        print("  ✅ Core modules imported successfully")
        
        # Test service imports
        from services.ollama_service import ollama_service
        from services.chat_service import ChatService
        from services.document_service import DocumentService
        print("  ✅ Service modules imported successfully")
        
        # Test agent imports
        from agents.base_agent import BaseAgent, AgentState
        from agents.planning_agent import PlanningAgent
        from agents.research_agent import ResearchAgent
        from agents.writing_agent import WritingAgent
        from agents.latex_agent import LaTeXAgent
        from agents.visual_agent import VisualAgent
        from agents.quality_agent import QualityAgent
        from agents.humanizer_agent import HumanizerAgent
        from agents.assembly_agent import AssemblyAgent
        from agents.agent_coordinator import AgentCoordinator
        print("  ✅ Agent modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error: {e}")
        return False

async def test_agent_initialization():
    """Test agent initialization"""
    print("\n🤖 Testing agent initialization...")
    
    try:
        from agents.planning_agent import PlanningAgent
        from agents.research_agent import ResearchAgent
        from agents.writing_agent import WritingAgent
        from agents.agent_coordinator import AgentCoordinator
        
        # Test individual agent initialization
        planning_agent = PlanningAgent()
        print(f"  ✅ Planning Agent: {planning_agent.name} (ID: {planning_agent.agent_id})")
        
        research_agent = ResearchAgent()
        print(f"  ✅ Research Agent: {research_agent.name} (ID: {research_agent.agent_id})")
        
        writing_agent = WritingAgent()
        print(f"  ✅ Writing Agent: {writing_agent.name} (ID: {writing_agent.agent_id})")
        
        # Test coordinator initialization
        coordinator = AgentCoordinator()
        print(f"  ✅ Agent Coordinator initialized with {len(coordinator.agents)} agents")
        
        # Test agent capabilities
        for agent_id, agent in coordinator.agents.items():
            capabilities = len(agent.capabilities)
            print(f"    - {agent.name}: {capabilities} capabilities")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Agent initialization error: {e}")
        return False

async def test_ollama_connection():
    """Test Ollama connection"""
    print("\n🔗 Testing Ollama connection...")
    
    try:
        from services.ollama_service import ollama_service
        
        # Test basic connection
        models = await ollama_service.list_models()
        print(f"  ✅ Connected to Ollama, found {len(models.get('models', []))} models")
        
        # List available models
        for model in models.get('models', []):
            model_name = model.get('name', 'Unknown')
            model_size = model.get('size', 0)
            size_gb = model_size / (1024**3) if model_size else 0
            print(f"    - {model_name} ({size_gb:.1f}GB)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Ollama connection error: {e}")
        print("  💡 Make sure Ollama is running: ollama serve")
        return False

async def test_agent_health_checks():
    """Test agent health checks"""
    print("\n🏥 Testing agent health checks...")
    
    try:
        from agents.agent_coordinator import AgentCoordinator
        
        coordinator = AgentCoordinator()
        
        # Test individual agent health
        for agent_id, agent in coordinator.agents.items():
            try:
                health = await agent.health_check()
                status = "✅ Healthy" if health.get("healthy", False) else "❌ Unhealthy"
                print(f"  {status} {agent.name}")
                
                if not health.get("healthy", False):
                    error = health.get("error", "Unknown error")
                    print(f"    Error: {error}")
                    
            except Exception as e:
                print(f"  ❌ {agent.name}: Health check failed - {e}")
        
        # Test coordinator health
        system_health = await coordinator.health_check()
        overall_healthy = system_health.get("coordinator_healthy", False)
        status = "✅ System Healthy" if overall_healthy else "❌ System Issues"
        print(f"\n  {status}")
        
        return overall_healthy
        
    except Exception as e:
        print(f"  ❌ Health check error: {e}")
        return False

async def test_simple_agent_task():
    """Test simple agent task execution"""
    print("\n⚡ Testing simple agent task execution...")
    
    try:
        from agents.planning_agent import PlanningAgent
        from unittest.mock import patch
        
        # Create planning agent
        agent = PlanningAgent()
        
        # Mock LLM call to avoid requiring actual model
        with patch.object(agent, '_call_llm', return_value='{"structure": {"sections": [{"name": "Introduction", "word_count": "500-800"}]}}'):
            
            # Test planning task
            task = {
                "type": "document_planning",
                "requirements": {
                    "title": "Test Document",
                    "document_type": "research_paper",
                    "writing_style": "analytical",
                    "target_length": 2000,
                    "citation_style": "APA"
                }
            }
            
            result = await agent.execute(task)
            
            if result.get("success", False):
                print("  ✅ Planning agent task executed successfully")
                print(f"    - Document type: {result.get('plan', {}).get('metadata', {}).get('document_type', 'Unknown')}")
                print(f"    - Execution time: {result.get('execution_metadata', {}).get('execution_time', 0):.2f}s")
                return True
            else:
                print(f"  ❌ Planning agent task failed: {result.get('error', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"  ❌ Agent task execution error: {e}")
        return False

async def test_coordinator_workflow():
    """Test basic coordinator workflow"""
    print("\n🎭 Testing coordinator workflow...")
    
    try:
        from agents.agent_coordinator import AgentCoordinator
        from unittest.mock import patch, AsyncMock
        
        coordinator = AgentCoordinator()
        
        # Mock all agent executions
        for agent in coordinator.agents.values():
            agent.execute = AsyncMock(return_value={
                "success": True,
                "content": "Mock content",
                "execution_metadata": {
                    "agent_id": agent.agent_id,
                    "success": True,
                    "execution_time": 0.1
                }
            })
        
        # Test document generation request
        request = {
            "title": "Test Academic Paper",
            "document_type": "research_paper",
            "writing_style": "analytical",
            "target_length": 1000,
            "citation_style": "APA"
        }
        
        # Progress tracking
        progress_updates = []
        async def progress_callback(progress_data):
            progress_updates.append(progress_data)
            print(f"    Progress: {progress_data.get('progress', 0)}% - {progress_data.get('phase', 'unknown')}")
        
        print("  🚀 Starting mock document generation...")
        result = await coordinator.generate_document(
            request=request,
            session_id="test_session_123",
            progress_callback=progress_callback
        )
        
        if result.get("success", False):
            print("  ✅ Coordinator workflow completed successfully")
            print(f"    - Session ID: {result.get('session_id')}")
            print(f"    - Generation time: {result.get('generation_time', 0):.2f}s")
            print(f"    - Agents used: {len(result.get('agent_outputs', {}))}")
            print(f"    - Progress updates: {len(progress_updates)}")
            return True
        else:
            print(f"  ❌ Coordinator workflow failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"  ❌ Coordinator workflow error: {e}")
        return False

async def test_api_endpoints():
    """Test API endpoints"""
    print("\n🌐 Testing API endpoints...")
    
    try:
        # Test if FastAPI app can be created
        from main import app
        print("  ✅ FastAPI app created successfully")
        
        # Test if all routers are included
        routes = [route.path for route in app.routes]
        expected_routes = ["/api/agents/generate", "/api/agents/status", "/api/health"]
        
        found_routes = []
        for expected in expected_routes:
            if any(expected in route for route in routes):
                found_routes.append(expected)
        
        print(f"  ✅ Found {len(found_routes)}/{len(expected_routes)} expected API routes")
        for route in found_routes:
            print(f"    - {route}")
        
        return len(found_routes) == len(expected_routes)
        
    except Exception as e:
        print(f"  ❌ API endpoint test error: {e}")
        return False

async def test_database_connection():
    """Test database connection"""
    print("\n🗄️ Testing database connection...")
    
    try:
        from core.database import SessionLocal, engine
        from sqlalchemy import text
        
        # Test database connection
        db = SessionLocal()
        try:
            # Simple query to test connection
            result = db.execute(text("SELECT 1"))
            print("  ✅ Database connection successful")
            
            # Test if tables exist (they should be created automatically)
            from core.database import Base
            Base.metadata.create_all(bind=engine)
            print("  ✅ Database tables created/verified")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"  ❌ Database connection error: {e}")
        return False

async def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🧪 ASCAES Comprehensive Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Agent Initialization", test_agent_initialization),
        ("Ollama Connection", test_ollama_connection),
        ("Agent Health Checks", test_agent_health_checks),
        ("Simple Agent Task", test_simple_agent_task),
        ("Coordinator Workflow", test_coordinator_workflow),
        ("API Endpoints", test_api_endpoints),
        ("Database Connection", test_database_connection)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! ASCAES is ready for use.")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Some issues may need attention.")
    else:
        print("🚨 Multiple test failures. Please check the setup.")
    
    return passed, total

async def quick_test():
    """Run quick essential tests only"""
    print("⚡ ASCAES Quick Test")
    print("=" * 30)
    
    essential_tests = [
        ("Basic Imports", test_basic_imports),
        ("Agent Initialization", test_agent_initialization),
        ("Database Connection", test_database_connection)
    ]
    
    results = []
    for test_name, test_func in essential_tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} failed: {e}")
            results.append((test_name, False))
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n⚡ Quick Test: {passed}/{total} essential tests passed")
    
    if passed == total:
        print("✅ Essential components are working!")
    else:
        print("❌ Some essential components have issues.")
    
    return passed == total

def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ASCAES Test Suite")
    parser.add_argument("--quick", action="store_true", help="Run quick essential tests only")
    parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive test suite")
    
    args = parser.parse_args()
    
    if args.quick:
        asyncio.run(quick_test())
    elif args.comprehensive:
        asyncio.run(run_comprehensive_test())
    else:
        # Default: run quick test first, then ask for comprehensive
        print("Running quick test first...\n")
        quick_success = asyncio.run(quick_test())
        
        if quick_success:
            print("\n" + "="*50)
            response = input("Quick test passed! Run comprehensive test? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print()
                asyncio.run(run_comprehensive_test())
        else:
            print("\n❌ Quick test failed. Please fix essential issues before running comprehensive tests.")

if __name__ == "__main__":
    main()

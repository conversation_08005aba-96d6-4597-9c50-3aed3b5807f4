"""
ASCAES - Academic Scholarly Content & Analysis Expert System
Main FastAPI application entry point
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import os
from pathlib import Path

from core.config import settings
from core.database import init_db
from api.routes import chat, documents, models, health
from api import agents
from core.websocket_manager import WebSocketManager
from core.logging_config import setup_logging

# Initialize logging
logger = setup_logging()

# Create FastAPI app
app = FastAPI(
    title="ASCAES API",
    description="Academic Scholarly Content & Analysis Expert System",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager
websocket_manager = WebSocketManager()

# Include API routes
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(models.router, prefix="/api/models", tags=["models"])
app.include_router(health.router, prefix="/api/health", tags=["health"])
app.include_router(agents.router)

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting ASCAES application...")
    
    # Initialize database
    await init_db()
    
    # Create storage directories
    os.makedirs(settings.STORAGE_PATH, exist_ok=True)
    os.makedirs(settings.DOCUMENTS_PATH, exist_ok=True)
    os.makedirs(settings.VECTORS_PATH, exist_ok=True)
    os.makedirs(settings.TEMP_PATH, exist_ok=True)
    
    logger.info("ASCAES application started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown"""
    logger.info("Shutting down ASCAES application...")
    await websocket_manager.disconnect_all()
    logger.info("ASCAES application shutdown complete")

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time chat communication"""
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_json()
            await websocket_manager.handle_message(client_id, data)
    except WebSocketDisconnect:
        await websocket_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        await websocket_manager.disconnect(client_id)

@app.get("/")
async def root():
    """Root endpoint - serves frontend in production"""
    if settings.ENVIRONMENT == "production":
        # Serve React build
        frontend_path = Path(__file__).parent.parent / "frontend" / "dist"
        if frontend_path.exists():
            app.mount("/", StaticFiles(directory=str(frontend_path), html=True), name="frontend")
            return HTMLResponse(content=open(frontend_path / "index.html").read())
    
    return {
        "message": "ASCAES API Server",
        "version": "1.0.0",
        "status": "running",
        "docs": "/api/docs"
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "name": "ASCAES API",
        "version": "1.0.0",
        "description": "Academic Scholarly Content & Analysis Expert System",
        "endpoints": {
            "chat": "/api/chat",
            "documents": "/api/documents", 
            "models": "/api/models",
            "health": "/api/health",
            "websocket": "/ws/{client_id}"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )

"""
ASCAES - Academic Scholarly Content & Analysis Expert System
Main FastAPI application entry point
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import os
from pathlib import Path
from typing import Dict
from datetime import datetime

from core.config import settings
from core.database import init_db
from api.routes import chat, documents, models, health
from api import agents
from core.websocket_manager import WebSocketManager
from core.logging_config import setup_logging

# Initialize logging
logger = setup_logging()

# Create FastAPI app
app = FastAPI(
    title="ASCAES API",
    description="Academic Scholarly Content & Analysis Expert System",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager
websocket_manager = WebSocketManager()

# Include API routes
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(models.router, prefix="/api/models", tags=["models"])
app.include_router(health.router, prefix="/api/health", tags=["health"])
app.include_router(agents.router)

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Starting ASCAES application...")
    
    # Initialize database
    await init_db()
    
    # Create storage directories
    os.makedirs(settings.STORAGE_PATH, exist_ok=True)
    os.makedirs(settings.DOCUMENTS_PATH, exist_ok=True)
    os.makedirs(settings.VECTORS_PATH, exist_ok=True)
    os.makedirs(settings.TEMP_PATH, exist_ok=True)
    
    logger.info("ASCAES application started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown"""
    logger.info("Shutting down ASCAES application...")
    await websocket_manager.disconnect_all()
    logger.info("ASCAES application shutdown complete")

# Simple WebSocket connections storage
active_websockets: Dict[str, WebSocket] = {}

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time chat communication"""
    print(f"🔌 WebSocket connection attempt from client: {client_id}")
    logger.info(f"WebSocket connection attempt from client: {client_id}")

    try:
        # Accept the WebSocket connection directly
        await websocket.accept()
        active_websockets[client_id] = websocket
        print(f"✅ WebSocket connected: {client_id}")
        logger.info(f"WebSocket connected: {client_id}")

        # Send welcome message
        await websocket.send_json({
            "type": "system",
            "message": "Connected to ASCAES",
            "timestamp": datetime.now().isoformat()
        })
        print(f"📤 Welcome message sent to {client_id}")

        while True:
            data = await websocket.receive_json()
            print(f"📨 Received from {client_id}: {data}")
            logger.debug(f"Received WebSocket message from {client_id}: {data}")

            # Handle different message types
            message_type = data.get("type", "unknown")

            if message_type == "ping":
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            elif message_type == "chat":
                # Use the websocket manager for chat handling
                await websocket_manager.handle_message(client_id, data)
            else:
                # Echo unknown messages
                await websocket.send_json({
                    "type": "echo",
                    "original": data,
                    "timestamp": datetime.now().isoformat()
                })

    except WebSocketDisconnect:
        print(f"🔌 WebSocket disconnected: {client_id}")
        logger.info(f"WebSocket disconnected: {client_id}")
        if client_id in active_websockets:
            del active_websockets[client_id]
        await websocket_manager.disconnect(client_id)
    except Exception as e:
        print(f"❌ WebSocket error for client {client_id}: {e}")
        logger.error(f"WebSocket error for client {client_id}: {e}")
        if client_id in active_websockets:
            del active_websockets[client_id]
        await websocket_manager.disconnect(client_id)

@app.get("/")
async def root():
    """Root endpoint - serves frontend in production"""
    if settings.ENVIRONMENT == "production":
        # Serve React build
        frontend_path = Path(__file__).parent.parent / "frontend" / "dist"
        if frontend_path.exists():
            app.mount("/", StaticFiles(directory=str(frontend_path), html=True), name="frontend")
            return HTMLResponse(content=open(frontend_path / "index.html").read())
    
    return {
        "message": "ASCAES API Server",
        "version": "1.0.0",
        "status": "running",
        "docs": "/api/docs",
        "websocket": "/ws/{client_id}"
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "name": "ASCAES API",
        "version": "1.0.0",
        "description": "Academic Scholarly Content & Analysis Expert System",
        "endpoints": {
            "chat": "/api/chat",
            "documents": "/api/documents", 
            "models": "/api/models",
            "health": "/api/health",
            "websocket": "/ws/{client_id}"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )

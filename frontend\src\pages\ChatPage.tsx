import React, { useState, useRef, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { 
  Send, 
  Paperclip, 
  Mic, 
  Square,
  FileText,
  Image,
  Upload
} from 'lucide-react'
import { useAppStore } from '../store/appStore'

import { ChatMessage } from '../components/ChatMessage'
import { TypingIndicator } from '../components/TypingIndicator'
import { FileUpload } from '../components/FileUpload'
import { DocumentPreview } from '../components/DocumentPreview'
import { DocumentProgress } from '../components/DocumentProgress'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'

export const ChatPage: React.FC = () => {
  const { conversationId } = useParams()
  const [message, setMessage] = useState('')
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [documentProgress, setDocumentProgress] = useState<{
    isGenerating: boolean
    progress: number
    message: string
    sessionId?: string
  }>({
    isGenerating: false,
    progress: 0,
    message: ''
  })
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  
  const {
    messages,
    isTyping,
    currentConversationId,
    setCurrentConversation,
    addMessage,
    sendMessage
  } = useAppStore()

  // Set current conversation when component mounts or conversationId changes
  useEffect(() => {
    if (conversationId && conversationId !== currentConversationId) {
      setCurrentConversation(conversationId)
    }
  }, [conversationId, currentConversationId, setCurrentConversation])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, isTyping])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [message])

  const handleSendMessage = async () => {
    if (!message.trim()) return

    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: message.trim(),
      timestamp: new Date(),
    }

    // Add message to store
    addMessage(userMessage)

    const messageText = message.trim()
    setMessage('')

    try {
      // Check if it's a document generation request
      const docRequest = apiService.parseDocumentRequest(messageText)

      if (docRequest) {
        // Set progress state
        setDocumentProgress({
          isGenerating: true,
          progress: 0,
          message: 'Analyzing your document request...'
        })

        // Show processing message
        const processingMessage = {
          id: 'processing-' + Date.now(),
          role: 'assistant' as const,
          content: '🤔 Analyzing your document request...',
          timestamp: new Date(),
        }
        addMessage(processingMessage)

        // Use document generation API
        const response = await fetch('http://localhost:8000/api/chat/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            message: messageText,
            conversation_id: currentConversationId
          })
        })

        if (response.ok) {
          const result = await response.json()

          // Add assistant response
          const assistantMessage = {
            id: result.session_id || Date.now().toString(),
            role: 'assistant' as const,
            content: result.response,
            timestamp: new Date(),
            metadata: {
              type: result.type,
              session_id: result.session_id,
              action: result.action,
              details: result.metadata
            }
          }
          addMessage(assistantMessage)

          // If it's a document generation, start monitoring
          if (result.type === 'large_document_request' || result.type === 'document_request') {
            setDocumentProgress({
              isGenerating: true,
              progress: 5,
              message: 'Document generation started!',
              sessionId: result.session_id
            })
            toast.success('Document generation started!')
            monitorDocumentGeneration(result.session_id)
          }
        } else {
          throw new Error('Failed to process document request')
        }

      } else {
        // Fallback to WebSocket for general chat
        if (sendMessage) {
          await sendMessage({
            type: 'chat',
            message: messageText,
            conversation_id: currentConversationId,
          })
        } else {
          throw new Error('WebSocket not connected')
        }
      }

    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Failed to send message')

      // Add error message
      const errorMessage = {
        id: 'error-' + Date.now(),
        role: 'assistant' as const,
        content: '❌ Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      }
      addMessage(errorMessage)
    }
  }

  const monitorDocumentGeneration = async (sessionId: string) => {
    const maxAttempts = 30 // Monitor for up to 30 checks (15 minutes)
    let attempts = 0

    const checkProgress = async () => {
      try {
        const response = await fetch(`http://localhost:8000/api/chat/generation/${sessionId}`)

        if (response.ok) {
          const status = await response.json()

          // Update progress state
          setDocumentProgress({
            isGenerating: status.status !== 'completed',
            progress: status.progress || 0,
            message: `${status.phase || 'Processing'} - ${status.message || ''}`,
            sessionId
          })

          // Add progress update message
          if (status.progress > 0) {
            const progressMessage = {
              id: 'progress-' + Date.now(),
              role: 'assistant' as const,
              content: `📊 **Progress:** ${status.progress}% - ${status.phase}\n${status.message || ''}`,
              timestamp: new Date(),
              metadata: { type: 'progress', session_id: sessionId }
            }
            addMessage(progressMessage)
          }

          if (status.status === 'completed') {
            setDocumentProgress({
              isGenerating: false,
              progress: 100,
              message: 'Document generation completed!'
            })
            toast.success('Document generation completed!')
            return // Stop monitoring
          } else if (status.status === 'failed') {
            setDocumentProgress({
              isGenerating: false,
              progress: 0,
              message: 'Document generation failed'
            })
            toast.error('Document generation failed')
            return // Stop monitoring
          }
        }

        attempts++
        if (attempts < maxAttempts) {
          // Check again in 30 seconds
          setTimeout(checkProgress, 30000)
        }

      } catch (error) {
        console.error('Error checking progress:', error)
        attempts++
        if (attempts < maxAttempts) {
          setTimeout(checkProgress, 30000)
        }
      }
    }

    // Start monitoring after 10 seconds
    setTimeout(checkProgress, 10000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleFileUpload = (files: File[]) => {
    // TODO: Implement file upload
    console.log('Files uploaded:', files)
    setShowFileUpload(false)
  }

  const toggleRecording = () => {
    // TODO: Implement voice recording
    setIsRecording(!isRecording)
  }

  return (
    <div className="flex h-full">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center max-w-md">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Welcome to ASCAES
                </h3>
                <p className="text-gray-600 mb-6">
                  Start a conversation to generate academic documents with AI. 
                  Choose from 8 writing styles and multiple output formats.
                </p>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <strong>Writing Styles:</strong>
                    <br />Analytical, Instructional, Reporting, Argumentative
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <strong>Output Formats:</strong>
                    <br />PDF, LaTeX, RTF, TXT
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {messages.map((msg) => (
                <ChatMessage key={msg.id} message={msg} />
              ))}
              {isTyping && <TypingIndicator />}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Progress Indicator */}
        {documentProgress.isGenerating && (
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="max-w-4xl mx-auto">
              <DocumentProgress
                progress={documentProgress.progress}
                status="processing"
                message={documentProgress.message}
                estimatedTime="7.6 minutes"
              />
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="border-t border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-end space-x-3">
              {/* File Upload Button */}
              <button
                onClick={() => setShowFileUpload(!showFileUpload)}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Paperclip className="w-5 h-5" />
              </button>

              {/* Message Input */}
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me to generate academic documents, analyze papers, or help with research..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32"
                  rows={1}
                />
                
                {/* Character count */}
                <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                  {message.length}/4000
                </div>
              </div>

              {/* Voice Recording Button */}
              <button
                onClick={toggleRecording}
                className={`p-2 rounded-lg transition-colors ${
                  isRecording 
                    ? 'text-red-600 bg-red-100 hover:bg-red-200' 
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                {isRecording ? <Square className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              </button>

              {/* Send Button */}
              <button
                onClick={handleSendMessage}
                disabled={!message.trim()}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>

            {/* File Upload Area */}
            {showFileUpload && (
              <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                <FileUpload onUpload={handleFileUpload} />
              </div>
            )}

            {/* Quick Actions */}
            <div className="mt-3 flex flex-wrap gap-2">
              <button className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                Generate Research Paper
              </button>
              <button className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                Analyze Document
              </button>
              <button className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                Create Bibliography
              </button>
              <button className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                LaTeX Template
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Document Preview Panel */}
      <div className="w-96 border-l border-gray-200 bg-white">
        <DocumentPreview />
      </div>
    </div>
  )
}

"""
Chat Handler Service
Processes natural language requests and routes them to appropriate services
"""

import re
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from services.large_document_service import LargeDocumentService
from agents.agent_coordinator import AgentCoordinator
from core.logging_config import get_logger

logger = get_logger(__name__)

class ChatHandler:
    """Handles chat messages and routes them to appropriate services"""
    
    def __init__(self):
        self.large_doc_service = LargeDocumentService()
        self.coordinator = AgentCoordinator()
        
        # Common patterns for document generation requests
        self.doc_patterns = {
            'large_document': [
                r'generate.*?(\d+)\s*pages?',
                r'create.*?(\d+)\s*pages?',
                r'write.*?(\d+)\s*pages?',
                r'(\d+)\s*page.*?document',
                r'(\d+)\s*page.*?paper',
                r'(\d+)\s*page.*?report',
            ],
            'document_types': {
                'research_paper': ['research paper', 'research', 'academic paper'],
                'thesis': ['thesis', 'dissertation'],
                'report': ['report', 'analysis report'],
                'essay': ['essay', 'academic essay'],
                'article': ['article', 'journal article'],
                'review': ['review', 'literature review'],
            },
            'writing_styles': {
                'analytical': ['analytical', 'analysis', 'analyze'],
                'argumentative': ['argumentative', 'argument', 'persuasive'],
                'descriptive': ['descriptive', 'describe'],
                'narrative': ['narrative', 'story', 'case study'],
                'instructional': ['instructional', 'tutorial', 'guide', 'how-to'],
                'reporting': ['reporting', 'findings', 'results'],
                'exploratory': ['exploratory', 'explore', 'investigate'],
                'schematic': ['reference', 'manual', 'documentation'],
            },
            'fields': {
                'computer_science': ['computer science', 'AI', 'artificial intelligence', 'machine learning', 'programming'],
                'healthcare': ['healthcare', 'medical', 'medicine', 'health', 'clinical'],
                'business': ['business', 'management', 'marketing', 'finance', 'economics'],
                'education': ['education', 'teaching', 'learning', 'pedagogy'],
                'psychology': ['psychology', 'mental health', 'behavior'],
                'engineering': ['engineering', 'technical', 'technology'],
                'science': ['science', 'research', 'scientific', 'biology', 'chemistry', 'physics'],
            }
        }
    
    async def process_message(self, message: str, session_id: str) -> Dict[str, Any]:
        """Process incoming chat message and determine appropriate action"""
        
        logger.info(f"Processing message: {message[:100]}...")
        
        # Check if it's a document generation request
        doc_request = self.parse_document_request(message)
        
        if doc_request:
            # Determine if it's a large document (50+ pages)
            target_pages = doc_request.get('target_pages', 0)
            target_length = doc_request.get('target_length', 0)
            
            # Convert words to pages if needed
            if target_length > 0 and target_pages == 0:
                target_pages = max(1, target_length // 250)
            
            if target_pages >= 50:
                logger.info(f"Large document request detected: {target_pages} pages")
                return await self.handle_large_document_request(doc_request, session_id, message)
            else:
                logger.info(f"Regular document request detected: {target_pages} pages")
                return await self.handle_regular_document_request(doc_request, session_id, message)
        
        # Handle other types of requests
        return await self.handle_general_chat(message, session_id)
    
    def parse_document_request(self, message: str) -> Optional[Dict[str, Any]]:
        """Parse a natural language message for document generation parameters"""
        
        lower_message = message.lower()
        
        # Check if it's a document generation request
        doc_keywords = ['generate', 'create', 'write', 'document', 'paper', 'report', 'thesis', 'essay']
        if not any(keyword in lower_message for keyword in doc_keywords):
            return None
        
        # Extract parameters
        request = {
            'title': self.extract_title(message),
            'document_type': self.extract_document_type(lower_message),
            'writing_style': self.extract_writing_style(lower_message),
            'citation_style': self.extract_citation_style(lower_message),
            'field': self.extract_field(lower_message),
            'keywords': self.extract_keywords(message),
            'output_formats': self.extract_output_formats(lower_message),
            'include_math': 'math' in lower_message or 'equation' in lower_message,
            'author': 'ASCAES Generated',
            'references': [],
        }
        
        # Extract length/pages
        pages = self.extract_pages(message)
        words = self.extract_words(message)
        
        if pages:
            request['target_pages'] = pages
            request['target_length'] = pages * 250
        elif words:
            request['target_length'] = words
            request['target_pages'] = max(1, words // 250)
        else:
            # Default to medium-sized document
            request['target_length'] = 2000
            request['target_pages'] = 8
        
        # Add large document specific settings
        if request['target_pages'] >= 50:
            request['humanization_level'] = 'extensive'
            request['ai_detection_avoidance'] = True
            request['quality_threshold'] = 0.80
        
        return request
    
    def extract_title(self, message: str) -> str:
        """Extract document title from message"""
        patterns = [
            r'(?:about|on|titled?|called)\s+["\']([^"\']+)["\']',
            r'(?:about|on)\s+([^.!?]+?)(?:\s+(?:in|with|using|for)|\.|$)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return 'Academic Document'
    
    def extract_document_type(self, message: str) -> str:
        """Extract document type from message"""
        for doc_type, keywords in self.doc_patterns['document_types'].items():
            if any(keyword in message for keyword in keywords):
                return doc_type
        return 'research_paper'
    
    def extract_writing_style(self, message: str) -> str:
        """Extract writing style from message"""
        for style, keywords in self.doc_patterns['writing_styles'].items():
            if any(keyword in message for keyword in keywords):
                return style
        return 'analytical'
    
    def extract_citation_style(self, message: str) -> str:
        """Extract citation style from message"""
        styles = ['apa', 'mla', 'chicago', 'ieee', 'harvard']
        for style in styles:
            if style in message:
                return style.upper()
        return 'APA'
    
    def extract_field(self, message: str) -> str:
        """Extract academic field from message"""
        for field, keywords in self.doc_patterns['fields'].items():
            if any(keyword.lower() in message for keyword in keywords):
                return field
        return 'general'
    
    def extract_keywords(self, message: str) -> List[str]:
        """Extract keywords from message"""
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{4,}\b', message.lower())
        stop_words = {'generate', 'create', 'write', 'document', 'paper', 'about', 'with', 'using', 'pages', 'words'}
        keywords = [word for word in words if word not in stop_words]
        return keywords[:10]  # Limit to 10 keywords
    
    def extract_output_formats(self, message: str) -> List[str]:
        """Extract desired output formats"""
        formats = []
        if 'pdf' in message:
            formats.append('pdf')
        if 'latex' in message or 'tex' in message:
            formats.append('latex')
        if 'word' in message or 'docx' in message:
            formats.append('rtf')
        if 'text' in message or 'txt' in message:
            formats.append('txt')
        
        return formats if formats else ['pdf', 'latex', 'txt']
    
    def extract_pages(self, message: str) -> Optional[int]:
        """Extract number of pages from message"""
        patterns = [r'(\d+)\s*pages?', r'(\d+)\s*page']
        for pattern in patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return None
    
    def extract_words(self, message: str) -> Optional[int]:
        """Extract number of words from message"""
        patterns = [r'(\d+)\s*words?', r'(\d+)\s*word']
        for pattern in patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return None
    
    async def handle_large_document_request(self, request: Dict[str, Any], 
                                          session_id: str, original_message: str) -> Dict[str, Any]:
        """Handle large document generation request"""
        
        target_pages = request.get('target_pages', 100)
        
        # Get time estimate
        estimate = self.large_doc_service.get_estimated_time(target_pages)
        
        response = {
            'type': 'large_document_request',
            'message': f"I'll generate a {target_pages}-page {request['document_type'].replace('_', ' ')} about '{request['title']}' for you.",
            'details': {
                'title': request['title'],
                'pages': target_pages,
                'document_type': request['document_type'],
                'writing_style': request['writing_style'],
                'field': request['field'],
                'estimated_time': f"{estimate['estimated_minutes']:.1f} minutes",
                'features': [
                    'AI detection avoidance',
                    'Multiple humanization passes',
                    'Quality assurance checks',
                    'Academic standards compliance',
                    f"Multiple formats: {', '.join(request['output_formats'])}"
                ]
            },
            'request': request,
            'session_id': session_id,
            'action': 'confirm_large_document'
        }
        
        return response
    
    async def handle_regular_document_request(self, request: Dict[str, Any], 
                                            session_id: str, original_message: str) -> Dict[str, Any]:
        """Handle regular document generation request"""
        
        target_pages = request.get('target_pages', 8)
        
        response = {
            'type': 'document_request',
            'message': f"I'll generate a {target_pages}-page {request['document_type'].replace('_', ' ')} about '{request['title']}' for you.",
            'details': {
                'title': request['title'],
                'pages': target_pages,
                'document_type': request['document_type'],
                'writing_style': request['writing_style'],
                'field': request['field'],
                'estimated_time': f"{target_pages * 2} minutes",
            },
            'request': request,
            'session_id': session_id,
            'action': 'confirm_document'
        }
        
        return response
    
    async def handle_general_chat(self, message: str, session_id: str) -> Dict[str, Any]:
        """Handle general chat messages"""
        
        # Check for help requests
        if any(word in message.lower() for word in ['help', 'how', 'what', 'can you']):
            return {
                'type': 'help',
                'message': self.get_help_message(),
                'session_id': session_id
            }
        
        # Default response
        return {
            'type': 'general',
            'message': "I'm ASCAES, your academic document generation assistant. I can help you create research papers, reports, and other academic documents. Try asking me to 'generate a 100-page research paper about artificial intelligence' or ask for help to see what I can do!",
            'session_id': session_id
        }
    
    def get_help_message(self) -> str:
        """Get help message"""
        return """
🎓 **ASCAES - Academic Document Generation Assistant**

I can help you generate high-quality academic documents with the following features:

**📄 Document Types:**
• Research Papers • Thesis/Dissertations • Reports • Essays • Articles • Reviews

**✨ Key Features:**
• **Large Documents**: Up to 500 pages with intelligent chunking
• **AI Detection Avoidance**: Multiple humanization passes
• **Multiple Formats**: PDF, LaTeX, RTF, TXT
• **Academic Standards**: Proper citations and formatting
• **8 Writing Styles**: Analytical, Argumentative, Descriptive, etc.

**💬 How to Use:**
Just tell me what you want! For example:
• "Generate a 100-page research paper about machine learning in healthcare"
• "Create a 50-page analytical report on climate change using APA citations"
• "Write a 200-page thesis about artificial intelligence in PDF and LaTeX formats"

**⚡ Large Document Features:**
• Intelligent chunking for memory efficiency
• Real-time progress tracking
• Quality assurance checks
• Optimized for ~20 minutes per 100 pages

Ready to create your academic document? Just describe what you need!
        """.strip()

# Global instance
chat_handler = ChatHandler()

"""
ASCAES Configuration Settings
Centralized configuration management using Pydantic Settings
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List

from pathlib import Path

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "ASCAES"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    
    # Database
    DATABASE_URL: str = Field(default="sqlite:///./ascaes.db", env="DATABASE_URL")
    
    # Storage Paths
    BASE_PATH: Path = Path(__file__).parent.parent.parent
    STORAGE_PATH: Path = BASE_PATH / "storage"
    DOCUMENTS_PATH: Path = STORAGE_PATH / "documents"
    VECTORS_PATH: Path = STORAGE_PATH / "vectors"
    TEMP_PATH: Path = STORAGE_PATH / "temp"
    MODELS_PATH: Path = BASE_PATH / "models"
    TEMPLATES_PATH: Path = BASE_PATH / "templates"
    
    # Ollama Configuration
    OLLAMA_HOST: str = Field(default="http://localhost:11434", env="OLLAMA_HOST")
    OLLAMA_TIMEOUT: int = Field(default=300, env="OLLAMA_TIMEOUT")
    
    # Model Configuration (8GB RAM Optimized)
    DEFAULT_MODEL: str = Field(default="llama3.2:3b", env="DEFAULT_MODEL")
    CODING_MODEL: str = Field(default="llama3.2:3b", env="CODING_MODEL")
    REASONING_MODEL: str = Field(default="llama3.2:3b", env="REASONING_MODEL")
    
    # Model Settings
    MAX_TOKENS: int = Field(default=4096, env="MAX_TOKENS")
    TEMPERATURE: float = Field(default=0.7, env="TEMPERATURE")
    TOP_P: float = Field(default=0.9, env="TOP_P")
    
    # Vector Database
    VECTOR_DB_PATH: str = str(VECTORS_PATH)
    EMBEDDING_MODEL: str = Field(default="all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    CHUNK_SIZE: int = Field(default=1000, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # Document Processing
    MAX_FILE_SIZE: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    SUPPORTED_FORMATS: List[str] = [".pdf", ".txt", ".docx", ".md", ".tex"]
    OCR_LANGUAGE: str = Field(default="eng", env="OCR_LANGUAGE")
    
    # LaTeX Configuration
    LATEX_TIMEOUT: int = Field(default=60, env="LATEX_TIMEOUT")
    LATEX_COMPILER: str = Field(default="pdflatex", env="LATEX_COMPILER")
    
    # Security
    SECRET_KEY: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="ascaes.log", env="LOG_FILE")
    
    # Performance
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=300, env="REQUEST_TIMEOUT")
    
    # Agent Configuration
    MAX_AGENT_ITERATIONS: int = Field(default=10, env="MAX_AGENT_ITERATIONS")
    AGENT_TIMEOUT: int = Field(default=600, env="AGENT_TIMEOUT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Global settings instance
settings = Settings()

# Ensure storage directories exist
for path in [settings.STORAGE_PATH, settings.DOCUMENTS_PATH, 
             settings.VECTORS_PATH, settings.TEMP_PATH, settings.MODELS_PATH]:
    path.mkdir(parents=True, exist_ok=True)

import { useEffect, useRef, useCallback } from 'react'
import { useAppStore } from '../store/appStore'
import { toast } from 'react-hot-toast'

export const useWebSocket = () => {
  const socketRef = useRef<WebSocket | null>(null)
  const clientIdRef = useRef<string>('')
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const {
    setConnected,
    setTyping,
    addMessage,
    currentConversationId
  } = useAppStore()

  const connect = useCallback(() => {
    if (socketRef.current?.readyState === WebSocket.OPEN) return

    // Generate client ID
    if (!clientIdRef.current) {
      clientIdRef.current = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    try {
      // Create native WebSocket connection
      const wsUrl = `ws://localhost:8000/ws/${clientIdRef.current}`
      socketRef.current = new WebSocket(wsUrl)

      const socket = socketRef.current

      socket.onopen = () => {
        console.log('Connected to WebSocket')
        setConnected(true)

        // Clear any reconnection timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current)
          reconnectTimeoutRef.current = null
        }
      }

      socket.onclose = (event) => {
        console.log('Disconnected from WebSocket', event.code, event.reason)
        setConnected(false)
        setTyping(false)

        // Only attempt to reconnect if it wasn't a clean close and we're not already reconnecting
        if (event.code !== 1000 && !reconnectTimeoutRef.current) {
          // Attempt to reconnect after 3 seconds
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('Attempting to reconnect...')
            connect()
          }, 3000)
        }
      }

      socket.onerror = (error) => {
        console.error('WebSocket connection error:', error)
        setConnected(false)
      }

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleIncomingMessage(data)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
      setConnected(false)
    }

  }, [setConnected, setTyping])

  const disconnect = useCallback(() => {
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (socketRef.current) {
      socketRef.current.close(1000, 'User disconnected')
      socketRef.current = null
      setConnected(false)
    }
  }, [setConnected])

  const sendMessage = useCallback(async (data: any) => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, message not sent:', data)
      return false
    }

    try {
      socketRef.current.send(JSON.stringify(data))
      return true
    } catch (error) {
      console.error('Error sending message:', error)
      return false
    }
  }, [])

  const handleIncomingMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'chat_response':
        addMessage({
          id: data.message_id || Date.now().toString(),
          role: 'assistant',
          content: data.message,
          timestamp: new Date(data.timestamp),
          metadata: data.metadata,
        })
        setTyping(false)
        break

      case 'system':
        // Filter out connection status messages to avoid cluttering chat
        if (!data.message?.includes('Connected to ASCAES') &&
            !data.message?.includes('Disconnected from ASCAES')) {
          addMessage({
            id: Date.now().toString(),
            role: 'system',
            content: data.message,
            timestamp: new Date(data.timestamp),
          })
        }
        break

      case 'document_processed':
        toast.success(`Document processed: ${data.filename}`)
        break

      case 'document_error':
        toast.error(`Document processing failed: ${data.error}`)
        break

      case 'generation_started':
        toast.success('Document generation started')
        break

      case 'generation_progress':
        // Add progress message to chat
        addMessage({
          id: `progress-${Date.now()}`,
          role: 'system',
          content: `📝 Progress Update: ${data.progress}% complete - ${data.message || 'Processing...'}`,
          timestamp: new Date(data.timestamp),
          metadata: { progress: data.progress }
        })
        console.log('Generation progress:', data.progress)
        break

      case 'generation_completed':
        toast.success('Document generation completed')
        break

      case 'generation_error':
        toast.error(`Document generation failed: ${data.error}`)
        break

      default:
        console.log('Unknown message type:', data.type, data)
    }
  }, [addMessage, setTyping])

  // Connect on mount, disconnect on unmount
  useEffect(() => {
    connect()
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  // Reconnect when conversation changes
  useEffect(() => {
    if (socketRef.current?.readyState === WebSocket.OPEN && currentConversationId) {
      sendMessage({
        type: 'join_conversation',
        conversation_id: currentConversationId,
      })
    }
  }, [currentConversationId, sendMessage])

  return {
    isConnected: socketRef.current?.readyState === WebSocket.OPEN || false,
    sendMessage,
    connect,
    disconnect,
  }
}

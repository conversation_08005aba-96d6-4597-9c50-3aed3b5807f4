@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased;
  }

  * {
    @apply border-gray-200 dark:border-gray-700;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Chat message animations */
  .message-enter {
    @apply opacity-0 translate-y-2;
    animation: messageEnter 0.3s ease-out forwards;
  }
  
  @keyframes messageEnter {
    to {
      @apply opacity-100 translate-y-0;
    }
  }
  
  /* Typing indicator */
  .typing-indicator {
    @apply flex space-x-1;
  }
  
  .typing-dot {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
    animation-delay: var(--delay);
  }
  
  /* Code block styling */
  .prose pre {
    @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
  }
  
  .prose code {
    @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm;
  }
  
  .prose pre code {
    @apply bg-transparent text-gray-100 p-0;
  }
  
  /* Math rendering */
  .katex {
    font-size: 1.1em;
  }
  
  .katex-display {
    @apply my-4;
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .btn-danger {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2;
  }
  
  /* Input styling */
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  /* Card styling */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
  }

  .card-hover {
    @apply card hover:shadow-medium transition-shadow duration-200;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Sidebar styling */
  .sidebar-item {
    @apply flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 border-r-2 border-primary-600 dark:border-primary-400;
  }
  
  /* Message bubble styling */
  .message-bubble {
    @apply max-w-3xl px-4 py-3 rounded-lg;
  }
  
  .message-bubble.user {
    @apply bg-primary-600 text-white ml-auto;
  }
  
  .message-bubble.assistant {
    @apply bg-white border border-gray-200 mr-auto;
  }
  
  .message-bubble.system {
    @apply bg-gray-100 text-gray-600 mx-auto text-center text-sm;
  }
  
  /* Document preview styling */
  .document-preview {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-soft;
  }
  
  .document-preview h1 {
    @apply text-2xl font-bold text-gray-900 mb-4;
  }
  
  .document-preview h2 {
    @apply text-xl font-semibold text-gray-800 mb-3 mt-6;
  }
  
  .document-preview h3 {
    @apply text-lg font-medium text-gray-700 mb-2 mt-4;
  }
  
  .document-preview p {
    @apply text-gray-600 mb-4 leading-relaxed;
  }
  
  .document-preview ul, .document-preview ol {
    @apply text-gray-600 mb-4 pl-6;
  }
  
  .document-preview li {
    @apply mb-1;
  }
  
  /* File upload area */
  .upload-area {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 hover:bg-primary-50 transition-colors duration-200 cursor-pointer;
  }
  
  .upload-area.dragover {
    @apply border-primary-500 bg-primary-50;
  }
  
  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }
  
  .progress-fill {
    @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
  }
  
  /* Status indicators */
  .status-online {
    @apply w-2 h-2 bg-success-500 rounded-full;
  }
  
  .status-offline {
    @apply w-2 h-2 bg-gray-400 rounded-full;
  }
  
  .status-error {
    @apply w-2 h-2 bg-error-500 rounded-full;
  }
  
  /* Responsive utilities */
  @media (max-width: 768px) {
    .mobile-hidden {
      @apply hidden;
    }
    
    .mobile-full {
      @apply w-full;
    }
  }
}

"""
ASCAES Agent Coordinator
Coordinates the multi-agent system for document generation
"""

import asyncio
from typing import Dict, Any, Callable, Optional
from datetime import datetime

from core.logging_config import get_logger
from services.ollama_service import ollama_service
from agents.agent_coordinator import AgentCoordinator as MultiAgentCoordinator

logger = get_logger(__name__)

class AgentCoordinator:
    """Coordinates multiple specialized agents for document generation"""

    def __init__(self):
        # Use the new multi-agent coordinator
        self.coordinator = MultiAgentCoordinator()

        # Legacy compatibility
        self.agents = self.coordinator.agents
    
    async def generate_document(
        self,
        request: Dict[str, Any],
        client_id: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Coordinate multi-agent document generation"""

        # Use the new coordinator
        return await self.coordinator.generate_document(
            request=request,
            session_id=client_id,
            progress_callback=progress_callback
        )

class BaseAgent:
    """Base class for all agents"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"agent.{name}")
    
    async def _call_llm(self, prompt: str, model: str = "qwen2:7b-instruct") -> str:
        """Call LLM with error handling"""
        try:
            response = await ollama_service.generate(
                model=model,
                prompt=prompt,
                options={
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_ctx": 4096
                }
            )
            return response.get("response", "")
        except Exception as e:
            self.logger.error(f"LLM call failed: {e}")
            return f"Error: Could not generate response for {self.name} agent"

class PlanningAgent(BaseAgent):
    """Agent responsible for document planning and structure"""
    
    def __init__(self):
        super().__init__("planning")
    
    async def create_plan(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Create document generation plan"""
        
        prompt = f"""Create a detailed plan for generating a {request['document_type']} with the following specifications:

Title: {request['title']}
Writing Style: {request['writing_style']}
Output Format: {request['output_format']}

Please provide:
1. Document structure outline
2. Key sections and subsections
3. Estimated word count per section
4. Research requirements
5. Visual elements needed
6. Citation requirements

Format your response as a structured plan."""

        plan_text = await self._call_llm(prompt)
        
        return {
            "document_type": request["document_type"],
            "structure": plan_text,
            "estimated_length": request.get("length_target", 2000),
            "sections": ["Introduction", "Main Body", "Conclusion"],  # Simplified
            "research_needed": True,
            "visual_elements": ["figures", "tables"],
            "timestamp": datetime.now().isoformat()
        }

class ResearchAgent(BaseAgent):
    """Agent responsible for research and information gathering"""
    
    def __init__(self):
        super().__init__("research")
    
    async def gather_information(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Gather research information based on plan"""
        
        # In a real implementation, this would:
        # - Search vector database for relevant documents
        # - Query external APIs (if allowed)
        # - Analyze uploaded documents
        # - Generate citations
        
        return {
            "sources": ["Academic Source 1", "Academic Source 2"],
            "key_findings": ["Finding 1", "Finding 2"],
            "citations": ["Citation 1", "Citation 2"],
            "research_summary": "Research completed successfully",
            "timestamp": datetime.now().isoformat()
        }

class WritingAgent(BaseAgent):
    """Agent responsible for content generation"""
    
    def __init__(self):
        super().__init__("writing")
    
    async def generate_content(self, plan: Dict[str, Any], research: Dict[str, Any]) -> str:
        """Generate document content"""
        
        prompt = f"""Generate academic content based on this plan:

{plan['structure']}

Research findings:
{research['research_summary']}

Write in {plan.get('writing_style', 'analytical')} style.
Target length: {plan.get('estimated_length', 2000)} words.

Generate comprehensive, well-structured academic content."""

        content = await self._call_llm(prompt)
        return content

class LaTeXAgent(BaseAgent):
    """Agent responsible for LaTeX formatting"""
    
    def __init__(self):
        super().__init__("latex")
    
    async def format_document(self, content: str, request: Dict[str, Any]) -> str:
        """Format document for specified output format"""
        
        if request["output_format"] == "latex":
            # Convert to LaTeX format
            formatted = f"""\\documentclass{{article}}
\\begin{{document}}
\\title{{{request['title']}}}
\\maketitle

{content}

\\end{{document}}"""
            return formatted
        
        return content  # Return as-is for other formats

class VisualAgent(BaseAgent):
    """Agent responsible for visual elements"""
    
    def __init__(self):
        super().__init__("visual")
    
    async def add_visual_elements(self, content: str) -> str:
        """Add visual elements to document"""
        
        # Placeholder for visual element generation
        # In production, this would add figures, tables, charts
        
        return content + "\n\n[Visual elements would be added here]"

class QualityAgent(BaseAgent):
    """Agent responsible for quality assurance"""
    
    def __init__(self):
        super().__init__("quality")
    
    async def check_quality(self, content: str) -> Dict[str, Any]:
        """Perform quality checks on document"""
        
        # Placeholder quality checks
        word_count = len(content.split())
        
        return {
            "word_count": word_count,
            "quality_score": 0.85,  # Placeholder
            "issues": [],
            "suggestions": ["Consider adding more citations"],
            "timestamp": datetime.now().isoformat()
        }

class HumanizerAgent(BaseAgent):
    """Agent responsible for making content more human-like"""
    
    def __init__(self):
        super().__init__("humanizer")
    
    async def humanize_content(self, content: str) -> str:
        """Make content more natural and human-like"""
        
        prompt = f"""Review and improve this academic content to make it more natural and human-like while maintaining academic rigor:

{content[:2000]}...

Improve:
1. Sentence variety and flow
2. Natural transitions
3. Engaging language while staying academic
4. Remove any AI-like patterns

Return the improved version."""

        humanized = await self._call_llm(prompt)
        return humanized if humanized else content

class AssemblyAgent(BaseAgent):
    """Agent responsible for final document assembly"""
    
    def __init__(self):
        super().__init__("assembly")
    
    async def assemble_document(
        self, 
        content: str, 
        request: Dict[str, Any], 
        quality_report: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Assemble final document"""
        
        return {
            "document_id": f"doc_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "title": request["title"],
            "content": content,
            "metadata": {
                "word_count": quality_report["word_count"],
                "quality_score": quality_report["quality_score"],
                "generation_time": datetime.now().isoformat(),
                "agents_used": list(self.agents.keys()) if hasattr(self, 'agents') else []
            },
            "download_url": "/api/documents/download/placeholder"
        }

"""
ASCAES Document Service
Handles document processing, generation, and management
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path
import fitz  # PyMuPDF
from sqlalchemy.orm import Session

from core.database import SessionLocal, Document, GeneratedDocument, vector_db
from core.config import settings
from core.logging_config import get_logger
from services.ollama_service import ollama_service

logger = get_logger(__name__)

class DocumentService:
    """Service for document processing and generation"""
    
    def __init__(self):
        self.processing_queue = asyncio.Queue()
        self.generation_queue = asyncio.Queue()
    
    async def process_document_async(self, document_id: int):
        """Add document to processing queue"""
        await self.processing_queue.put(document_id)
        # Start background processing
        asyncio.create_task(self._process_document_worker())
    
    async def _process_document_worker(self):
        """Background worker for document processing"""
        try:
            while True:
                document_id = await self.processing_queue.get()
                await self._process_single_document(document_id)
                self.processing_queue.task_done()
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Document processing worker error: {e}")
    
    async def _process_single_document(self, document_id: int):
        """Process a single document"""
        db = SessionLocal()
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                logger.error(f"Document {document_id} not found")
                return
            
            logger.info(f"Processing document: {document.original_filename}")
            
            # Extract text based on file type
            extracted_text = await self._extract_text(document.file_path, document.file_type)
            
            if extracted_text:
                # Generate embeddings and store in vector database
                await self._store_document_embeddings(document, extracted_text)
                
                # Mark as processed
                document.processed = True
                db.commit()
                
                logger.info(f"Document {document.original_filename} processed successfully")
            else:
                logger.warning(f"No text extracted from {document.original_filename}")
                
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            db.rollback()
        finally:
            db.close()
    
    async def _extract_text(self, file_path: str, file_type: str) -> Optional[str]:
        """Extract text from document based on file type"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            if file_type.lower() == '.pdf':
                return await self._extract_pdf_text(file_path)
            elif file_type.lower() in ['.txt', '.md']:
                return await self._extract_text_file(file_path)
            elif file_type.lower() == '.docx':
                return await self._extract_docx_text(file_path)
            elif file_type.lower() == '.tex':
                return await self._extract_latex_text(file_path)
            else:
                logger.warning(f"Unsupported file type: {file_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return None
    
    async def _extract_pdf_text(self, file_path: Path) -> str:
        """Extract text from PDF using PyMuPDF"""
        try:
            doc = PyMuPDF.open(str(file_path))
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
                text += "\n\n"
            
            doc.close()
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting PDF text: {e}")
            return ""
    
    async def _extract_text_file(self, file_path: Path) -> str:
        """Extract text from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as f:
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading text file: {e}")
                return ""
        except Exception as e:
            logger.error(f"Error extracting text file: {e}")
            return ""
    
    async def _extract_docx_text(self, file_path: Path) -> str:
        """Extract text from DOCX files"""
        try:
            from docx import Document as DocxDocument
            doc = DocxDocument(str(file_path))
            
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip()
            
        except ImportError:
            logger.error("python-docx not installed, cannot process DOCX files")
            return ""
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            return ""
    
    async def _extract_latex_text(self, file_path: Path) -> str:
        """Extract text from LaTeX files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic LaTeX command removal (simplified)
            import re
            
            # Remove comments
            content = re.sub(r'%.*$', '', content, flags=re.MULTILINE)
            
            # Remove common LaTeX commands
            content = re.sub(r'\\[a-zA-Z]+\{[^}]*\}', '', content)
            content = re.sub(r'\\[a-zA-Z]+', '', content)
            
            # Remove braces
            content = re.sub(r'[{}]', '', content)
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"Error extracting LaTeX text: {e}")
            return ""
    
    async def _store_document_embeddings(self, document: Document, text: str):
        """Generate and store document embeddings"""
        try:
            # Split text into chunks
            chunks = self._split_text_into_chunks(text)
            
            # Generate embeddings for each chunk
            documents = []
            metadatas = []
            ids = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = f"{document.id}_{i}"
                
                documents.append(chunk)
                metadatas.append({
                    "document_id": document.id,
                    "filename": document.original_filename,
                    "file_type": document.file_type,
                    "chunk_index": i,
                    "upload_date": document.upload_date.isoformat()
                })
                ids.append(chunk_id)
            
            # Store in vector database
            vector_db.add_documents(
                collection_name="documents",
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Stored {len(chunks)} chunks for document {document.original_filename}")
            
        except Exception as e:
            logger.error(f"Error storing document embeddings: {e}")
            raise
    
    def _split_text_into_chunks(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Split text into overlapping chunks"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings
                for i in range(end, max(start + chunk_size // 2, end - 100), -1):
                    if text[i] in '.!?':
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            
            if start >= len(text):
                break
        
        return chunks
    
    async def generate_document(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Generate academic document based on request"""
        db = SessionLocal()
        try:
            # Create database record
            generated_doc = GeneratedDocument(
                title=request["title"],
                document_type=request["document_type"],
                writing_style=request["writing_style"],
                output_format=request["output_format"],
                conversation_id=request["conversation_id"],
                content="",  # Will be filled by generation process
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.add(generated_doc)
            db.commit()
            db.refresh(generated_doc)
            
            # Generate content using AI
            content = await self._generate_document_content(request)
            
            # Update document with generated content
            generated_doc.content = content
            generated_doc.word_count = len(content.split())
            generated_doc.updated_at = datetime.now()
            
            # Generate files in all formats
            file_paths = await self._generate_all_formats(generated_doc, content)
            generated_doc.file_path = str(file_paths.get(request["output_format"], file_paths["txt"]))
            
            db.commit()
            
            return {
                "id": generated_doc.id,
                "title": generated_doc.title,
                "document_type": generated_doc.document_type,
                "writing_style": generated_doc.writing_style,
                "output_format": generated_doc.output_format,
                "conversation_id": generated_doc.conversation_id,
                "created_at": generated_doc.created_at,
                "updated_at": generated_doc.updated_at,
                "word_count": generated_doc.word_count,
                "download_url": f"/api/documents/generated/{generated_doc.id}/download"
            }
            
        except Exception as e:
            logger.error(f"Error generating document: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    async def _generate_document_content(self, request: Dict[str, Any]) -> str:
        """Generate document content using AI"""
        try:
            # Build prompt based on request
            prompt = self._build_generation_prompt(request)

            # Get target length and calculate tokens
            target_words = request.get("target_length", request.get("length_target", 2000))

            # Smart chunking: Use 3000-word chunks for optimal quality
            chunk_size = 3000

            # Get max tokens from request settings (frontend sends this)
            max_tokens_setting = request.get("max_tokens", request.get("maxTokens", 3000))

            # Estimate tokens needed (roughly 1.3 tokens per word for generation)
            estimated_tokens = int(chunk_size * 1.3)

            # Use optimized token count for quality generation
            final_max_tokens = min(max(estimated_tokens, max_tokens_setting), 4000)

            # Get the current model from request or use default
            current_model = request.get("model", settings.DEFAULT_MODEL)

            logger.info(f"Generating document: target_words={target_words}, chunk_size={chunk_size}, max_tokens={final_max_tokens}, model={current_model}")

            # For documents larger than chunk size, use intelligent chunking
            if target_words > chunk_size:
                return await self._generate_chunked_document_content(request, prompt, current_model, target_words, chunk_size)

            # Generate content using Ollama with selected model
            # Ensure we have enough tokens for the requested word count
            generation_tokens = max(final_max_tokens, int(target_words * 1.5))

            logger.info(f"Generating with {generation_tokens} tokens for {target_words} words using model {current_model}")

            # Generate content using real Ollama service
            response = await ollama_service.generate(
                model=current_model,
                prompt=prompt,
                options={
                    "temperature": request.get("temperature", 0.7),
                    "top_p": request.get("top_p", 0.9),
                    "num_ctx": 8192,  # Good context window for Llama 3.2
                    "num_predict": generation_tokens,  # Ensure enough tokens for target words
                    "repeat_penalty": 1.1,
                    "top_k": 40
                }
            )

            logger.info(f"Generation completed successfully with {len(response.get('response', '').split())} words")

            generated_content = response.get("response", "Error generating content")

            # Check if content meets minimum length requirements
            word_count = len(generated_content.split())
            if word_count < target_words * 0.7:  # If less than 70% of target
                logger.warning(f"Generated content ({word_count} words) is shorter than target ({target_words} words)")
                # Try to extend the content
                extension_prompt = f"""The previous content was too short. Please continue and expand the academic document about "{request['title']}" to reach approximately {target_words} words total.

Current content:
{generated_content}

Please add more detailed sections, examples, analysis, and comprehensive coverage to reach the target length while maintaining academic quality and focus on the topic."""

                extension_response = await ollama_service.generate(
                    model=current_model,
                    prompt=extension_prompt,
                    options={
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "num_ctx": 8192,
                        "num_predict": min(estimated_tokens - word_count, 4000),
                        "repeat_penalty": 1.1
                    }
                )

                extension_content = extension_response.get("response", "")
                if extension_content:
                    generated_content += "\n\n" + extension_content

            return generated_content

        except Exception as e:
            logger.error(f"Error generating document content: {e}")
            return "Error: Could not generate document content"

    async def _generate_chunked_document_content(self, request: Dict[str, Any], base_prompt: str,
                                               model: str, target_words: int, chunk_size: int) -> str:
        """Generate document content using intelligent chunking for quality and consistency"""
        try:
            logger.info(f"Starting chunked generation: {target_words} words in {chunk_size}-word chunks")

            # Calculate number of chunks needed
            num_chunks = max(1, (target_words + chunk_size - 1) // chunk_size)

            # Generate document outline first for consistency
            outline_prompt = f"""{base_prompt}

TASK: Create a detailed outline for this {target_words}-word academic document.
The outline should be divided into {num_chunks} main sections of approximately {chunk_size} words each.

Requirements:
- Each section should have a clear focus and purpose
- Sections should flow logically from one to the next
- Include specific subtopics and key points for each section
- Ensure comprehensive coverage of the topic

Format as a numbered list with detailed subsections."""

            outline_response = await ollama_service.generate(
                model=model,
                prompt=outline_prompt,
                options={
                    "temperature": 0.6,
                    "top_p": 0.8,
                    "num_ctx": 8192,
                    "num_predict": 1500,
                    "repeat_penalty": 1.1
                }
            )

            outline = outline_response.get("response", "")
            logger.info(f"Generated outline for chunked document")

            # Generate each chunk with context awareness
            chunks = []
            full_content = ""

            for i in range(num_chunks):
                chunk_prompt = f"""{base_prompt}

DOCUMENT OUTLINE:
{outline}

GENERATION CONTEXT:
- This is section {i+1} of {num_chunks} for a {target_words}-word academic document
- Target length for this section: {chunk_size} words
- Previous content length: {len(full_content.split())} words

PREVIOUS CONTENT SUMMARY:
{full_content[-1000:] if full_content else 'This is the first section.'}

TASK: Write section {i+1} following the outline above.
- Write exactly around {chunk_size} words
- Maintain academic tone and quality
- Ensure smooth transition from previous content
- Include detailed analysis, examples, and comprehensive coverage
- Use proper academic structure with clear paragraphs

Write the complete content for section {i+1} now:"""

                # Calculate tokens needed for this chunk
                chunk_tokens = max(int(chunk_size * 1.5), 2000)  # Ensure minimum tokens

                logger.info(f"Generating chunk {i+1} with {chunk_tokens} tokens for {chunk_size} words")

                chunk_response = await ollama_service.generate(
                    model=model,
                    prompt=chunk_prompt,
                    options={
                        "temperature": request.get("temperature", 0.7),
                        "top_p": request.get("top_p", 0.9),
                        "num_ctx": 16384,  # Large context for better quality
                        "num_predict": chunk_tokens,  # Ensure enough tokens for chunk
                        "repeat_penalty": 1.1,
                        "top_k": 40,
                        "stop": []  # Remove stop tokens
                    }
                )

                chunk_content = chunk_response.get("response", "")
                chunk_word_count = len(chunk_content.split())

                logger.info(f"Generated chunk {i+1}/{num_chunks}: {chunk_word_count} words")

                chunks.append(chunk_content)
                full_content += "\n\n" + chunk_content

            # Merge and polish the final document
            merged_content = await self._merge_and_polish_chunks(chunks, request, model, target_words)

            final_word_count = len(merged_content.split())
            logger.info(f"Chunked generation completed: {final_word_count} words (target: {target_words})")

            return merged_content

        except Exception as e:
            logger.error(f"Error in chunked document generation: {e}")
            return "Error: Could not generate chunked document content"

    async def _merge_and_polish_chunks(self, chunks: List[str], request: Dict[str, Any],
                                     model: str, target_words: int) -> str:
        """Merge chunks and polish the final document for consistency and flow"""
        try:
            # Join chunks with proper spacing
            merged_content = "\n\n".join(chunks)

            # Get humanization level
            humanization_level = request.get("humanization_level", "moderate")

            # Polish the document for consistency and flow
            polish_prompt = f"""You are an expert academic editor. Review and polish this academic document to ensure:

1. CONSISTENCY: Uniform writing style, tone, and terminology throughout
2. FLOW: Smooth transitions between sections and paragraphs
3. QUALITY: High academic standards with proper structure
4. HUMANIZATION ({humanization_level} level): Make the text sound natural and human-written
5. COHERENCE: Logical progression of ideas and arguments

HUMANIZATION GUIDELINES:
- Conservative: Minimal changes, maintain formal academic tone
- Moderate: Add natural variations in sentence structure and vocabulary
- Extensive: Significant humanization with varied expressions, natural flow, and human-like writing patterns

TARGET LENGTH: {target_words} words (current: {len(merged_content.split())} words)

DOCUMENT TO POLISH:
{merged_content}

TASK: Return the polished, humanized document that maintains academic quality while sounding naturally human-written."""

            polish_response = await ollama_service.generate(
                model=model,
                prompt=polish_prompt,
                options={
                    "temperature": 0.8 if humanization_level == "extensive" else 0.7,
                    "top_p": 0.9,
                    "num_ctx": 16384,
                    "num_predict": int(target_words * 1.2),
                    "repeat_penalty": 1.05,
                    "top_k": 50
                }
            )

            polished_content = polish_response.get("response", merged_content)

            # Apply additional humanization techniques
            if humanization_level in ["moderate", "extensive"]:
                polished_content = await self._apply_humanization_techniques(polished_content, humanization_level, model)

            return polished_content

        except Exception as e:
            logger.error(f"Error merging and polishing chunks: {e}")
            return "\n\n".join(chunks)  # Return unpolished content as fallback

    async def _apply_humanization_techniques(self, content: str, level: str, model: str) -> str:
        """Apply advanced humanization techniques to avoid AI detection"""
        try:
            if level == "conservative":
                # Minimal humanization - just basic flow improvements
                humanization_prompt = f"""Lightly edit this academic text to improve natural flow while maintaining formal academic tone:

{content}

Make minimal changes:
- Vary sentence beginnings slightly
- Use natural academic transitions
- Maintain all technical accuracy
- Keep formal academic style"""

            elif level == "moderate":
                # Moderate humanization - natural variations
                humanization_prompt = f"""Humanize this academic text to sound more naturally written while maintaining academic quality:

{content}

Apply moderate humanization:
- Vary sentence structures and lengths
- Use natural academic language patterns
- Add subtle personal touches where appropriate
- Include natural transitions and connectors
- Maintain academic rigor and accuracy"""

            else:  # extensive
                # Extensive humanization - significant natural writing patterns
                humanization_prompt = f"""Transform this academic text to sound completely human-written while preserving all academic content:

{content}

Apply extensive humanization:
- Create natural, varied sentence patterns
- Use human-like academic writing style
- Add natural hesitations, qualifications, and nuances
- Include varied vocabulary and expressions
- Create authentic academic voice
- Ensure it passes AI detection tools
- Maintain all factual accuracy and academic standards"""

            humanization_response = await ollama_service.generate(
                model=model,
                prompt=humanization_prompt,
                options={
                    "temperature": 0.9 if level == "extensive" else 0.8,
                    "top_p": 0.95,
                    "num_ctx": 16384,
                    "num_predict": len(content.split()) + 500,
                    "repeat_penalty": 1.02,
                    "top_k": 60
                }
            )

            humanized_content = humanization_response.get("response", content)

            # Apply additional post-processing for extensive humanization
            if level == "extensive":
                humanized_content = await self._apply_advanced_humanization(humanized_content, model)

            return humanized_content

        except Exception as e:
            logger.error(f"Error applying humanization techniques: {e}")
            return content

    async def _apply_advanced_humanization(self, content: str, model: str) -> str:
        """Apply advanced humanization techniques for extensive level"""
        try:
            advanced_prompt = f"""Apply final humanization pass to make this text completely undetectable as AI-generated:

{content}

Advanced techniques:
- Add natural academic writing quirks and patterns
- Include subtle imperfections that humans make
- Use varied paragraph structures
- Add natural academic hedging and qualifications
- Include authentic scholarly voice patterns
- Ensure complete human-like flow and rhythm

Return the fully humanized academic text:"""

            response = await ollama_service.generate(
                model=model,
                prompt=advanced_prompt,
                options={
                    "temperature": 0.95,
                    "top_p": 0.98,
                    "num_ctx": 16384,
                    "num_predict": len(content.split()) + 200,
                    "repeat_penalty": 1.01,
                    "top_k": 80
                }
            )

            return response.get("response", content)

        except Exception as e:
            logger.error(f"Error in advanced humanization: {e}")
            return content

    async def _generate_large_document_content(self, request: Dict[str, Any], base_prompt: str, model: str, target_words: int) -> str:
        """Generate large document content by chunking"""
        try:
            logger.info(f"Generating large document with {target_words} words using chunking")

            # Calculate chunk size (aim for ~2000 words per chunk)
            chunk_size = 2000
            num_chunks = max(1, target_words // chunk_size)

            # Generate document outline first
            outline_prompt = f"""Create a detailed outline for a {target_words}-word academic document about: {request.get('title', 'the topic')}

Field: {request.get('field', 'general')}
Writing Style: {request.get('writing_style', 'analytical')}
Document Type: {request.get('document_type', 'research_paper')}

Create {num_chunks} main sections, each approximately {chunk_size} words. Provide:
1. Section titles
2. Key points for each section
3. Logical flow between sections

Format as a numbered list of sections with bullet points for key content."""

            outline_response = await ollama_service.generate(
                model=model,
                prompt=outline_prompt,
                options={
                    "temperature": 0.6,
                    "top_p": 0.8,
                    "num_ctx": 4096,
                    "num_predict": 1000,
                    "repeat_penalty": 1.1
                }
            )

            outline = outline_response.get("response", "")
            logger.info(f"Generated outline: {outline[:200]}...")

            # Generate each section
            full_content = []

            for i in range(num_chunks):
                section_prompt = f"""{base_prompt}

DOCUMENT OUTLINE:
{outline}

CURRENT TASK: Write section {i+1} of {num_chunks} for this document.
TARGET LENGTH: Approximately {chunk_size} words for this section.

PREVIOUS CONTENT:
{' '.join(full_content[-500:]) if full_content else 'This is the first section.'}

Write the complete content for section {i+1}. Make it detailed, comprehensive, and exactly around {chunk_size} words. Ensure it flows naturally from the previous content and maintains academic quality throughout."""

                section_response = await ollama_service.generate(
                    model=model,
                    prompt=section_prompt,
                    options={
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "num_ctx": 8192,
                        "num_predict": int(chunk_size * 1.5),  # Allow for token estimation
                        "repeat_penalty": 1.1,
                        "top_k": 40
                    }
                )

                section_content = section_response.get("response", "")
                full_content.append(section_content)

                logger.info(f"Generated section {i+1}/{num_chunks}: {len(section_content.split())} words")

            # Combine all sections
            complete_content = "\n\n".join(full_content)

            # Add conclusion if needed
            if len(complete_content.split()) < target_words * 0.9:  # If we're short, add conclusion
                conclusion_prompt = f"""{base_prompt}

COMPLETE DOCUMENT SO FAR:
{complete_content[-1000:]}

Write a comprehensive conclusion section that summarizes the key findings and provides final thoughts. Target approximately {target_words - len(complete_content.split())} words to reach the total target of {target_words} words."""

                conclusion_response = await ollama_service.generate(
                    model=model,
                    prompt=conclusion_prompt,
                    options={
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "num_ctx": 4096,
                        "num_predict": 1000,
                        "repeat_penalty": 1.1
                    }
                )

                complete_content += "\n\n" + conclusion_response.get("response", "")

            logger.info(f"Large document generation completed: {len(complete_content.split())} words")
            return complete_content

        except Exception as e:
            logger.error(f"Error generating large document content: {e}")
            return f"Error generating large content: {str(e)}"
    
    def _build_generation_prompt(self, request: Dict[str, Any]) -> str:
        """Build prompt for document generation"""

        style_descriptions = {
            "analytical": "critical examination and data interpretation with evidence-based conclusions",
            "instructional": "clear step-by-step guidance and teaching methodology",
            "reporting": "factual documentation and objective findings presentation",
            "argumentative": "persuasive reasoning with strong position and counterarguments",
            "exploratory": "investigative approach with hypothesis development",
            "descriptive": "detailed technical specifications and comprehensive explanations",
            "narrative": "story-driven explanations and case study methodology",
            "schematic": "systematic documentation and reference material structure"
        }

        style_desc = style_descriptions.get(request["writing_style"], "academic writing")
        target_words = request.get("target_length", request.get("length_target", 2000))

        # Extract topic from title for better content generation
        topic = request["title"]

        prompt = f"""You are an expert academic writer. Generate a comprehensive, detailed academic {request["document_type"]} about "{topic}".

CRITICAL REQUIREMENTS:
- Target Length: EXACTLY {target_words} words (this is mandatory)
- Writing Style: {request["writing_style"]} ({style_desc})
- Document Type: {request["document_type"]}
- Topic Focus: Provide extensive, detailed information specifically about "{topic}"

CONTENT REQUIREMENTS:
1. DEPTH: Provide comprehensive coverage of the topic with specific details, examples, and analysis
2. STRUCTURE: Use proper academic structure with multiple detailed sections
3. SPECIFICITY: Focus extensively on "{topic}" - provide specific facts, data, examples, and detailed explanations
4. LENGTH: Generate approximately {target_words} words of substantive content
5. ACADEMIC RIGOR: Include proper citations, references, and scholarly analysis

DOCUMENT STRUCTURE:
- Title and Abstract (200-300 words)
- Introduction with background and objectives (400-500 words)
- Literature Review or Background (500-700 words)
- Main Analysis/Discussion with multiple subsections (60% of total words)
- Case Studies or Examples specific to "{topic}" (300-500 words)
- Conclusion and Future Directions (200-300 words)
- References section

WRITING GUIDELINES:
- Write in {request["writing_style"]} style throughout
- Use specific examples and case studies related to "{topic}"
- Include detailed explanations and analysis
- Provide concrete data and evidence where relevant
- Use proper academic language and terminology
- Include multiple perspectives and comprehensive coverage

IMPORTANT: Do not write generic content. Focus specifically on "{topic}" and provide detailed, substantive information that demonstrates deep knowledge of the subject. Generate the full {target_words} words as requested.

Begin writing the complete academic document now:"""

        return prompt

    async def _generate_all_formats(self, document: GeneratedDocument, content: str) -> Dict[str, Path]:
        """Generate document files in all supported formats"""
        try:
            output_dir = settings.DOCUMENTS_PATH / "generated"
            output_dir.mkdir(exist_ok=True)

            filename_base = f"{document.id}_{document.title.replace(' ', '_')}"
            file_paths = {}

            # Generate TXT format
            txt_path = output_dir / f"{filename_base}.txt"
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(content)
            file_paths["txt"] = txt_path

            # Generate LaTeX format
            latex_path = output_dir / f"{filename_base}.tex"
            latex_content = self._convert_to_latex(content)
            with open(latex_path, 'w', encoding='utf-8') as f:
                f.write(latex_content)
            file_paths["latex"] = latex_path

            # Generate RTF format
            rtf_path = output_dir / f"{filename_base}.rtf"
            rtf_content = self._convert_to_rtf(content)
            with open(rtf_path, 'w', encoding='utf-8') as f:
                f.write(rtf_content)
            file_paths["rtf"] = rtf_path

            # Generate PDF format (placeholder - would use proper PDF generation)
            pdf_path = output_dir / f"{filename_base}.pdf"
            pdf_content = self._convert_to_pdf(content, document.title)
            with open(pdf_path, 'w', encoding='utf-8') as f:
                f.write(pdf_content)
            file_paths["pdf"] = pdf_path

            return file_paths

        except Exception as e:
            logger.error(f"Error generating document files: {e}")
            raise

    async def _generate_document_file(self, document: GeneratedDocument, content: str) -> Path:
        """Generate document file in specified format"""
        try:
            output_dir = settings.DOCUMENTS_PATH / "generated"
            output_dir.mkdir(exist_ok=True)
            
            filename = f"{document.id}_{document.title.replace(' ', '_')}"
            
            if document.output_format == "pdf":
                # Generate PDF (placeholder - would use proper PDF generation)
                file_path = output_dir / f"{filename}.pdf"
                # In production, use libraries like ReportLab or WeasyPrint
                with open(file_path, 'w') as f:
                    f.write(f"PDF content placeholder for: {content[:100]}...")
                    
            elif document.output_format == "latex":
                # Generate LaTeX
                file_path = output_dir / f"{filename}.tex"
                latex_content = self._convert_to_latex(content)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(latex_content)
                    
            elif document.output_format == "rtf":
                # Generate RTF
                file_path = output_dir / f"{filename}.rtf"
                rtf_content = self._convert_to_rtf(content)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(rtf_content)
                    
            else:  # txt
                file_path = output_dir / f"{filename}.txt"
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"Error generating document file: {e}")
            raise
    
    def _convert_to_latex(self, content: str) -> str:
        """Convert content to LaTeX format"""
        # Basic LaTeX template
        latex_template = r"""\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage[margin=1in]{geometry}

\title{Academic Document}
\author{ASCAES}
\date{\today}

\begin{document}

\maketitle

""" + content.replace('\n', '\n\n') + r"""

\end{document}"""
        
        return latex_template
    
    def _convert_to_rtf(self, content: str) -> str:
        """Convert content to RTF format"""
        # Basic RTF template
        rtf_content = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
\f0\fs24 """ + content.replace('\n', '\\par ') + "}"

        return rtf_content

    def _convert_to_pdf(self, content: str, title: str) -> str:
        """Convert content to PDF format (placeholder - returns HTML for now)"""
        # This is a placeholder. In production, you would use libraries like:
        # - WeasyPrint: weasyprint.HTML(string=html_content).write_pdf()
        # - ReportLab: for programmatic PDF generation
        # - Pandoc: subprocess call to convert markdown to PDF

        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <style>
        body {{ font-family: 'Times New Roman', serif; margin: 2cm; line-height: 1.6; }}
        h1 {{ color: #333; border-bottom: 2px solid #333; }}
        h2 {{ color: #555; margin-top: 2em; }}
        h3 {{ color: #777; }}
        p {{ text-align: justify; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    {content.replace(chr(10), '<br>').replace('# ', '<h1>').replace('## ', '<h2>').replace('### ', '<h3>')}
</body>
</html>"""
        return html_content

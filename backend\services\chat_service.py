"""
ASCAES Chat Service
Handles chat message processing and conversation management
"""

import uuid
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from core.database import SessionLocal, Conversation, Message, vector_db
from core.logging_config import get_logger
from services.ollama_service import ollama_service

logger = get_logger(__name__)

class ChatService:
    """Service for handling chat conversations and message processing"""
    
    def __init__(self):
        self.active_conversations: Dict[str, Dict] = {}
    
    async def process_message(
        self, 
        message: str, 
        conversation_id: Optional[str] = None,
        client_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process incoming chat message and generate response"""
        
        db = SessionLocal()
        try:
            # Get or create conversation
            if conversation_id:
                conversation = db.query(Conversation)\
                    .filter(Conversation.id == conversation_id)\
                    .first()
                if not conversation:
                    raise ValueError(f"Conversation {conversation_id} not found")
            else:
                # Create new conversation
                conversation = Conversation(
                    title=self._generate_conversation_title(message),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                db.add(conversation)
                db.commit()
                db.refresh(conversation)
                conversation_id = str(conversation.id)
            
            # Save user message
            user_message = Message(
                conversation_id=conversation.id,
                role="user",
                content=message,
                timestamp=datetime.now(),
                message_type="text"
            )
            db.add(user_message)
            db.commit()
            
            # Generate AI response
            response_content = await self._generate_response(
                message=message,
                conversation_id=conversation_id,
                conversation_history=self._get_conversation_history(db, conversation.id)
            )
            
            # Save AI response
            ai_message = Message(
                conversation_id=conversation.id,
                role="assistant",
                content=response_content,
                timestamp=datetime.now(),
                message_type="text"
            )
            db.add(ai_message)
            
            # Update conversation timestamp
            conversation.updated_at = datetime.now()
            db.commit()
            db.refresh(ai_message)
            
            return {
                "message": response_content,
                "conversation_id": conversation_id,
                "message_id": str(ai_message.id),
                "timestamp": ai_message.timestamp.isoformat(),
                "metadata": {
                    "model_used": "qwen2:7b-instruct",
                    "processing_time": 0.5  # Placeholder
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    async def _generate_response(
        self, 
        message: str, 
        conversation_id: str,
        conversation_history: list
    ) -> str:
        """Generate AI response using Ollama"""
        
        try:
            # Build conversation context
            messages = []
            
            # System prompt for academic document generation
            system_prompt = """You are ASCAES (Academic Scholarly Content & Analysis Expert System), an AI assistant specialized in academic document generation and research support. You help users create high-quality academic documents across 8 distinct writing styles:

1. Analytical - Critical examination and data interpretation
2. Instructional - Teaching and step-by-step guidance  
3. Reporting - Factual documentation and findings
4. Argumentative - Position papers and debates
5. Exploratory - Investigation and hypothesis development
6. Descriptive - Technical specifications and details
7. Narrative - Case studies and story-driven explanations
8. Schematic - Reference materials and systematic documentation

You can generate documents in multiple formats (PDF, LaTeX, RTF, TXT) and provide research assistance, citation help, and document analysis. Always maintain academic rigor and provide well-structured, properly formatted responses."""

            messages.append({"role": "system", "content": system_prompt})
            
            # Add conversation history (last 10 messages for context)
            for hist_msg in conversation_history[-10:]:
                messages.append({
                    "role": hist_msg["role"],
                    "content": hist_msg["content"]
                })
            
            # Add current message
            messages.append({"role": "user", "content": message})
            
            # Generate response using Ollama
            response = await ollama_service.chat(
                model="qwen2:7b-instruct",
                messages=messages,
                options={
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_ctx": 4096,
                    "num_predict": 2048
                }
            )
            
            return response.get("message", {}).get("content", "I apologize, but I couldn't generate a response. Please try again.")
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "I'm experiencing technical difficulties. Please try again in a moment."
    
    def _get_conversation_history(self, db: Session, conversation_id: int) -> list:
        """Get conversation message history"""
        
        messages = db.query(Message)\
            .filter(Message.conversation_id == conversation_id)\
            .order_by(Message.timestamp.asc())\
            .all()
        
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat()
            }
            for msg in messages
        ]
    
    def _generate_conversation_title(self, first_message: str) -> str:
        """Generate a title for the conversation based on the first message"""
        
        # Simple title generation - in production, could use AI
        words = first_message.split()[:6]
        title = " ".join(words)
        
        if len(title) > 50:
            title = title[:47] + "..."
        
        return title or "New Conversation"
    
    async def process_document(self, document_id: str) -> Dict[str, Any]:
        """Process uploaded document for analysis"""
        
        try:
            # Placeholder for document processing
            # In a real implementation, this would:
            # 1. Extract text from the document
            # 2. Analyze content structure
            # 3. Generate embeddings
            # 4. Store in vector database
            # 5. Create summary
            
            return {
                "document_id": document_id,
                "status": "processed",
                "summary": "Document processed successfully and added to knowledge base.",
                "extracted_text_length": 1500,
                "key_topics": ["research methodology", "data analysis", "academic writing"]
            }
            
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            raise
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get summary of conversation"""
        
        db = SessionLocal()
        try:
            conversation = db.query(Conversation)\
                .filter(Conversation.id == conversation_id)\
                .first()
            
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            
            message_count = db.query(Message)\
                .filter(Message.conversation_id == conversation.id)\
                .count()
            
            return {
                "id": str(conversation.id),
                "title": conversation.title,
                "created_at": conversation.created_at.isoformat(),
                "updated_at": conversation.updated_at.isoformat(),
                "message_count": message_count,
                "is_active": conversation.is_active
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            raise
        finally:
            db.close()
    
    async def search_conversations(self, query: str, limit: int = 10) -> list:
        """Search conversations by content"""
        
        try:
            # Use vector database to search conversation content
            results = vector_db.query_documents(
                collection_name="conversations",
                query_texts=[query],
                n_results=limit
            )
            
            return results.get("documents", [])
            
        except Exception as e:
            logger.error(f"Error searching conversations: {e}")
            return []

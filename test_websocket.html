<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        #messages { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; }
        input, button { padding: 8px; margin: 5px; }
        #messageInput { width: 300px; }
    </style>
</head>
<body>
    <h1>ASCAES WebSocket Test</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="sendPing()">Send Ping</button>
    </div>
    
    <div>
        <input type="text" id="messageInput" placeholder="Enter message..." />
        <button onclick="sendMessage()">Send Message</button>
    </div>
    
    <div id="messages"></div>
    
    <script>
        let socket = null;
        let clientId = 'test_client_' + Date.now();
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = 'status ' + className;
        }
        
        function addMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function connect() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                addMessage('Already connected', 'info');
                return;
            }
            
            updateStatus('Connecting...', 'connecting');
            addMessage('Attempting to connect to WebSocket...', 'info');
            
            const wsUrl = `ws://localhost:8000/ws/${clientId}`;
            addMessage(`WebSocket URL: ${wsUrl}`, 'info');
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(event) {
                updateStatus('Connected', 'connected');
                addMessage('✅ WebSocket connected successfully!', 'success');
            };
            
            socket.onclose = function(event) {
                updateStatus('Disconnected', 'disconnected');
                addMessage(`❌ WebSocket disconnected. Code: ${event.code}, Reason: ${event.reason}`, 'error');
            };
            
            socket.onerror = function(error) {
                updateStatus('Error', 'disconnected');
                addMessage(`❌ WebSocket error: ${error}`, 'error');
            };
            
            socket.onmessage = function(event) {
                addMessage(`📨 Received: ${event.data}`, 'success');
                try {
                    const data = JSON.parse(event.data);
                    addMessage(`📨 Parsed message type: ${data.type}`, 'info');
                } catch (e) {
                    addMessage(`📨 Raw message: ${event.data}`, 'info');
                }
            };
        }
        
        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }
        
        function sendPing() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const message = { type: 'ping', timestamp: new Date().toISOString() };
                socket.send(JSON.stringify(message));
                addMessage(`📤 Sent ping: ${JSON.stringify(message)}`, 'info');
            } else {
                addMessage('❌ WebSocket not connected', 'error');
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const messageText = input.value.trim();
            
            if (!messageText) {
                addMessage('❌ Please enter a message', 'error');
                return;
            }
            
            if (socket && socket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'chat',
                    message: messageText,
                    conversation_id: 1,
                    timestamp: new Date().toISOString()
                };
                socket.send(JSON.stringify(message));
                addMessage(`📤 Sent: ${JSON.stringify(message)}`, 'info');
                input.value = '';
            } else {
                addMessage('❌ WebSocket not connected', 'error');
            }
        }
        
        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Auto-connect on page load
        window.onload = function() {
            addMessage('Page loaded. Click Connect to test WebSocket connection.', 'info');
        };
    </script>
</body>
</html>

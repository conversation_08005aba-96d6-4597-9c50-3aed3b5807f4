"""
Research Agent
Responsible for information gathering, source analysis, and citation management
"""

import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability
from core.database import vector_db, SessionLocal, Document

class ResearchAgent(BaseAgent):
    """Agent specialized in research and information gathering"""
    
    def __init__(self):
        super().__init__(
            agent_id="research_agent",
            name="Research Agent", 
            description="Gathers information, analyzes sources, and manages citations for academic documents"
        )
        
        # Research-specific configuration
        self.config.update({
            "search_depth": "comprehensive",  # basic, detailed, comprehensive
            "source_quality_threshold": 0.8,
            "max_sources_per_query": 20,
            "citation_formats": {
                "APA": self._apa_format,
                "MLA": self._mla_format,
                "Chicago": self._chicago_format,
                "IEEE": self._ieee_format,
                "Harvard": self._harvard_format
            },
            "source_types": {
                "journal_article": {"weight": 1.0, "reliability": 0.9},
                "book": {"weight": 0.8, "reliability": 0.8},
                "conference_paper": {"weight": 0.9, "reliability": 0.85},
                "thesis": {"weight": 0.7, "reliability": 0.75},
                "report": {"weight": 0.6, "reliability": 0.7},
                "website": {"weight": 0.4, "reliability": 0.5}
            }
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define research agent capabilities"""
        return [
            AgentCapability(
                name="source_discovery",
                description="Find relevant academic sources and references",
                input_types=["research_query", "topic_keywords"],
                output_types=["source_list", "relevance_scores"]
            ),
            AgentCapability(
                name="content_analysis",
                description="Analyze and extract key information from sources",
                input_types=["document_content", "analysis_requirements"],
                output_types=["content_summary", "key_findings"]
            ),
            AgentCapability(
                name="citation_generation",
                description="Generate properly formatted citations",
                input_types=["source_metadata", "citation_style"],
                output_types=["formatted_citations", "bibliography"]
            ),
            AgentCapability(
                name="fact_verification",
                description="Verify claims and cross-reference information",
                input_types=["claims", "source_documents"],
                output_types=["verification_results", "confidence_scores"]
            )
        ]
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute research task"""
        task_type = task.get("type", "source_discovery")
        
        if task_type == "source_discovery":
            return await self._discover_sources(task)
        elif task_type == "content_analysis":
            return await self._analyze_content(task)
        elif task_type == "citation_generation":
            return await self._generate_citations(task)
        elif task_type == "fact_verification":
            return await self._verify_facts(task)
        elif task_type == "comprehensive_research":
            return await self._comprehensive_research(task)
        else:
            raise ValueError(f"Unknown research task type: {task_type}")
    
    async def _discover_sources(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Discover relevant sources for research topic"""
        query = task.get("query", "")
        topic_keywords = task.get("keywords", [])
        max_sources = task.get("max_sources", self.config["max_sources_per_query"])
        
        # Search vector database for uploaded documents
        uploaded_sources = await self._search_uploaded_documents(query, topic_keywords)
        
        # Generate additional source recommendations
        source_recommendations = await self._generate_source_recommendations(query, topic_keywords)
        
        # Combine and rank sources
        all_sources = uploaded_sources + source_recommendations
        ranked_sources = self._rank_sources(all_sources)
        
        return {
            "success": True,
            "sources": ranked_sources[:max_sources],
            "total_found": len(all_sources),
            "search_query": query,
            "keywords_used": topic_keywords,
            "search_timestamp": datetime.now().isoformat()
        }
    
    async def _search_uploaded_documents(self, query: str, keywords: List[str]) -> List[Dict[str, Any]]:
        """Search through uploaded documents"""
        try:
            # Combine query and keywords
            search_terms = [query] + keywords
            
            # Search vector database
            results = vector_db.query_documents(
                collection_name="documents",
                query_texts=search_terms,
                n_results=10
            )
            
            sources = []
            if results and "documents" in results:
                for i, doc in enumerate(results["documents"][0]):  # First query result
                    metadata = results["metadatas"][0][i] if "metadatas" in results else {}
                    
                    source = {
                        "id": results["ids"][0][i] if "ids" in results else f"doc_{i}",
                        "title": metadata.get("filename", "Unknown Document"),
                        "content_preview": doc[:200] + "..." if len(doc) > 200 else doc,
                        "source_type": self._determine_source_type(metadata.get("file_type", "")),
                        "relevance_score": results["distances"][0][i] if "distances" in results else 0.5,
                        "metadata": metadata,
                        "is_uploaded": True
                    }
                    sources.append(source)
            
            return sources
            
        except Exception as e:
            self.logger.error(f"Error searching uploaded documents: {e}")
            return []
    
    async def _generate_source_recommendations(self, query: str, keywords: List[str]) -> List[Dict[str, Any]]:
        """Generate recommendations for additional sources to find"""
        
        recommendation_prompt = f"""Based on the research query "{query}" and keywords {keywords}, 
recommend 10-15 high-quality academic sources that would be valuable for this research.

For each source, provide:
1. Title (realistic academic paper/book title)
2. Authors (realistic academic names)
3. Publication year (2015-2024)
4. Source type (journal article, book, conference paper, etc.)
5. Brief description of relevance
6. Estimated reliability score (0.1-1.0)

Focus on recent, high-impact sources from reputable academic venues.
Format as a structured list."""

        system_prompt = """You are an expert academic librarian with deep knowledge of scholarly sources 
across multiple disciplines. Recommend only high-quality, credible academic sources that would be 
available through academic databases and libraries."""

        recommendations_text = await self._call_llm(recommendation_prompt, system_prompt)
        
        # Parse recommendations into structured format
        recommended_sources = self._parse_source_recommendations(recommendations_text)
        
        return recommended_sources
    
    async def _analyze_content(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content from sources"""
        content = task.get("content", "")
        analysis_type = task.get("analysis_type", "summary")
        focus_areas = task.get("focus_areas", [])
        
        if analysis_type == "summary":
            return await self._create_content_summary(content, focus_areas)
        elif analysis_type == "key_findings":
            return await self._extract_key_findings(content, focus_areas)
        elif analysis_type == "methodology":
            return await self._analyze_methodology(content)
        elif analysis_type == "citations":
            return await self._extract_citations(content)
        else:
            return await self._comprehensive_analysis(content, focus_areas)
    
    async def _create_content_summary(self, content: str, focus_areas: List[str]) -> Dict[str, Any]:
        """Create summary of content"""
        focus_text = f" Focus particularly on: {', '.join(focus_areas)}." if focus_areas else ""
        
        summary_prompt = f"""Analyze and summarize the following academic content:{focus_text}

Content:
{content[:3000]}...

Provide:
1. Main thesis or argument
2. Key findings or conclusions
3. Methodology used (if applicable)
4. Significance and implications
5. Limitations or gaps identified

Create a comprehensive but concise academic summary."""

        summary = await self._call_llm(summary_prompt)
        
        return {
            "success": True,
            "summary": summary,
            "content_length": len(content),
            "focus_areas": focus_areas,
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    async def _extract_key_findings(self, content: str, focus_areas: List[str]) -> Dict[str, Any]:
        """Extract key findings from content"""
        findings_prompt = f"""Extract the key findings, results, and conclusions from this academic content:

{content[:3000]}...

Identify:
1. Primary findings or results
2. Statistical data or measurements
3. Theoretical contributions
4. Practical implications
5. Future research directions

Present as structured, numbered findings."""

        findings = await self._call_llm(findings_prompt)
        
        return {
            "success": True,
            "key_findings": findings,
            "extraction_method": "llm_analysis"
        }
    
    async def _generate_citations(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate properly formatted citations"""
        sources = task.get("sources", [])
        citation_style = task.get("citation_style", "APA")
        
        formatted_citations = []
        bibliography = []
        
        for source in sources:
            # Generate in-text citation
            in_text = self._generate_in_text_citation(source, citation_style)
            
            # Generate bibliography entry
            bib_entry = self._generate_bibliography_entry(source, citation_style)
            
            formatted_citations.append({
                "source_id": source.get("id"),
                "in_text": in_text,
                "bibliography": bib_entry
            })
            
            bibliography.append(bib_entry)
        
        return {
            "success": True,
            "citations": formatted_citations,
            "bibliography": sorted(bibliography),  # Sort alphabetically
            "citation_style": citation_style,
            "total_sources": len(sources)
        }
    
    async def _comprehensive_research(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive research for a topic"""
        research_plan = task.get("research_plan", {})
        topic = task.get("topic", "")
        
        # Step 1: Source discovery
        sources_result = await self._discover_sources({
            "query": topic,
            "keywords": research_plan.get("keywords", []),
            "max_sources": 15
        })
        
        # Step 2: Content analysis for top sources
        analyses = []
        for source in sources_result["sources"][:5]:  # Analyze top 5 sources
            if source.get("is_uploaded") and "content_preview" in source:
                analysis = await self._analyze_content({
                    "content": source["content_preview"],
                    "analysis_type": "comprehensive"
                })
                analyses.append({
                    "source_id": source["id"],
                    "analysis": analysis
                })
        
        # Step 3: Generate research synthesis
        synthesis = await self._synthesize_research(sources_result["sources"], analyses)
        
        return {
            "success": True,
            "research_results": {
                "sources": sources_result["sources"],
                "analyses": analyses,
                "synthesis": synthesis,
                "research_quality": self._assess_research_quality(sources_result["sources"])
            }
        }
    
    def _rank_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank sources by relevance and quality"""
        for source in sources:
            # Calculate composite score
            relevance = source.get("relevance_score", 0.5)
            source_type = source.get("source_type", "website")
            quality_weight = self.config["source_types"].get(source_type, {}).get("weight", 0.5)
            
            composite_score = (relevance * 0.6) + (quality_weight * 0.4)
            source["composite_score"] = composite_score
        
        # Sort by composite score (descending)
        return sorted(sources, key=lambda x: x.get("composite_score", 0), reverse=True)
    
    def _determine_source_type(self, file_type: str) -> str:
        """Determine source type from file extension"""
        type_mapping = {
            ".pdf": "journal_article",  # Assume PDF is journal article
            ".docx": "report",
            ".txt": "report", 
            ".md": "report",
            ".tex": "journal_article"
        }
        return type_mapping.get(file_type.lower(), "website")
    
    def _parse_source_recommendations(self, recommendations_text: str) -> List[Dict[str, Any]]:
        """Parse LLM-generated source recommendations"""
        sources = []
        
        # Simple parsing - in production, would use more sophisticated NLP
        lines = recommendations_text.split('\n')
        current_source = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                if current_source:
                    sources.append(current_source)
                    current_source = {}
                continue
            
            # Extract title, authors, year, etc.
            if line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                if current_source:
                    sources.append(current_source)
                current_source = {
                    "title": line.split('.', 1)[1].strip() if '.' in line else line,
                    "source_type": "journal_article",
                    "relevance_score": 0.8,
                    "is_uploaded": False,
                    "recommended": True
                }
        
        if current_source:
            sources.append(current_source)
        
        return sources[:10]  # Limit to 10 recommendations
    
    def _generate_in_text_citation(self, source: Dict[str, Any], style: str) -> str:
        """Generate in-text citation"""
        # Simplified citation generation
        if style == "APA":
            return f"(Author, {source.get('year', '2024')})"
        elif style == "MLA":
            return f"(Author {source.get('page', '1')})"
        else:
            return f"[{source.get('id', '1')}]"
    
    def _generate_bibliography_entry(self, source: Dict[str, Any], style: str) -> str:
        """Generate bibliography entry"""
        # Simplified bibliography generation
        title = source.get("title", "Unknown Title")
        year = source.get("year", "2024")
        
        if style == "APA":
            return f"Author. ({year}). {title}. Journal Name."
        elif style == "MLA":
            return f"Author. \"{title}.\" Journal Name, {year}."
        else:
            return f"Author. {title}. {year}."
    
    async def _synthesize_research(self, sources: List[Dict], analyses: List[Dict]) -> str:
        """Synthesize research findings"""
        synthesis_prompt = f"""Synthesize the following research sources and analyses into a coherent overview:

Sources found: {len(sources)}
Detailed analyses: {len(analyses)}

Key themes and findings from the research:
{chr(10).join([f"- {source.get('title', 'Unknown')}: {source.get('content_preview', '')[:100]}..." for source in sources[:3]])}

Create a research synthesis that:
1. Identifies main themes and patterns
2. Highlights key findings and consensus
3. Notes areas of disagreement or gaps
4. Suggests directions for further research

Provide a comprehensive but concise synthesis."""

        synthesis = await self._call_llm(synthesis_prompt)
        return synthesis
    
    def _assess_research_quality(self, sources: List[Dict]) -> Dict[str, Any]:
        """Assess overall quality of research sources"""
        if not sources:
            return {"quality_score": 0.0, "assessment": "No sources found"}
        
        # Calculate quality metrics
        avg_relevance = sum(s.get("relevance_score", 0) for s in sources) / len(sources)
        source_diversity = len(set(s.get("source_type") for s in sources))
        uploaded_ratio = sum(1 for s in sources if s.get("is_uploaded")) / len(sources)
        
        quality_score = (avg_relevance * 0.5) + (min(source_diversity / 3, 1.0) * 0.3) + (uploaded_ratio * 0.2)
        
        return {
            "quality_score": round(quality_score, 2),
            "total_sources": len(sources),
            "average_relevance": round(avg_relevance, 2),
            "source_diversity": source_diversity,
            "uploaded_sources": sum(1 for s in sources if s.get("is_uploaded")),
            "assessment": "High quality" if quality_score > 0.8 else "Medium quality" if quality_score > 0.6 else "Needs improvement"
        }
    
    # Citation format methods (simplified implementations)
    def _apa_format(self, source: Dict[str, Any]) -> str:
        return f"Author. ({source.get('year', '2024')}). {source.get('title', 'Title')}. Journal."
    
    def _mla_format(self, source: Dict[str, Any]) -> str:
        return f"Author. \"{source.get('title', 'Title')}.\" Journal, {source.get('year', '2024')}."
    
    def _chicago_format(self, source: Dict[str, Any]) -> str:
        return f"Author. \"{source.get('title', 'Title')}.\" Journal {source.get('year', '2024')}."
    
    def _ieee_format(self, source: Dict[str, Any]) -> str:
        return f"Author, \"{source.get('title', 'Title')},\" Journal, {source.get('year', '2024')}."
    
    def _harvard_format(self, source: Dict[str, Any]) -> str:
        return f"Author {source.get('year', '2024')}, '{source.get('title', 'Title')}', Journal."

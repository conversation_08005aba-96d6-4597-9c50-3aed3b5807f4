#!/usr/bin/env python3
"""
Test script to verify document generation with settings
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_document_generation():
    """Test document generation with optimized settings and humanization"""

    # Test data with optimized settings
    test_request = {
        "message": "Generate 6000 words academic document about Bangladesh economy.",
        "conversation_id": 1,
        "model": "deepseek-r1:7b",
        "settings": {
            "maxTokens": 3000,  # Optimized chunk size
            "temperature": 0.7,
            "topP": 0.9,
            "humanizationLevel": "extensive"  # Maximum humanization
        }
    }
    
    print("🚀 Testing optimized document generation with chunking and humanization...")
    print(f"Request: {json.dumps(test_request, indent=2)}")
    print("\n📋 Expected behavior:")
    print("- 6000 words should be split into 2 chunks of 3000 words each")
    print("- Extensive humanization should be applied")
    print("- High-quality academic content should be generated")
    print("- Final document should be merged and polished")
    
    try:
        # Start generation
        response = requests.post(f"{API_BASE}/api/chat/generate", json=test_request)
        response.raise_for_status()
        
        result = response.json()
        session_id = result.get("session_id")
        
        print(f"✅ Generation started! Session ID: {session_id}")
        print(f"Response: {result}")
        
        # Monitor progress
        if session_id:
            print("\n📊 Monitoring progress...")
            for i in range(30):  # Monitor for up to 30 seconds
                try:
                    status_response = requests.get(f"{API_BASE}/api/agents/generate/{session_id}")
                    status_response.raise_for_status()
                    
                    status = status_response.json()
                    print(f"Progress: {status.get('progress', 0)}% - {status.get('message', 'Processing...')}")
                    
                    if status.get('status') == 'completed':
                        print("🎉 Generation completed!")
                        break
                    elif status.get('status') == 'error':
                        print(f"❌ Generation failed: {status.get('message')}")
                        break
                        
                    time.sleep(2)
                except Exception as e:
                    print(f"Error checking status: {e}")
                    break
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_document_generation()

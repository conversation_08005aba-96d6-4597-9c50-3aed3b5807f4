import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  messageType?: 'text' | 'document' | 'image'
  metadata?: Record<string, any>
}

export interface Conversation {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

export interface Document {
  id: string
  filename: string
  originalFilename: string
  fileSize: number
  fileType: string
  mimeType: string
  uploadDate: Date
  processed: boolean
  conversationId?: string
}

export interface GeneratedDocument {
  id: string
  title: string
  documentType: string
  writingStyle: string
  outputFormat: string
  conversationId: string
  createdAt: Date
  updatedAt: Date
  wordCount?: number
  pageCount?: number
  qualityScore?: number
}

export interface ModelInfo {
  name: string
  size: string
  modifiedAt: Date
  digest: string
  details: Record<string, any>
}

interface AppState {
  // UI State
  theme: 'light' | 'dark'
  sidebarCollapsed: boolean
  currentPage: string
  
  // Connection State
  isConnected: boolean
  isTyping: boolean
  sendMessage: ((data: any) => Promise<boolean>) | null
  
  // Chat State
  conversations: Conversation[]
  currentConversationId: string | null
  messages: Message[]
  
  // Document State
  documents: Document[]
  generatedDocuments: GeneratedDocument[]
  uploadProgress: Record<string, number>
  
  // Model State
  availableModels: ModelInfo[]
  currentModel: string
  modelStatus: Record<string, 'loading' | 'ready' | 'error'>
  
  // Settings
  settings: {
    maxTokens: number
    temperature: number
    topP: number
    writingStyle: string
    outputFormat: string
    autoSave: boolean
    notifications: boolean
  }
}

interface AppActions {
  // UI Actions
  setTheme: (theme: 'light' | 'dark') => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setCurrentPage: (page: string) => void
  
  // Connection Actions
  setConnected: (connected: boolean) => void
  setTyping: (typing: boolean) => void
  setSendMessage: (sendMessage: ((data: any) => Promise<boolean>) | null) => void
  
  // Chat Actions
  addConversation: (conversation: Conversation) => void
  updateConversation: (id: string, updates: Partial<Conversation>) => void
  deleteConversation: (id: string) => void
  setCurrentConversation: (id: string | null) => void
  addMessage: (message: Message) => void
  updateMessage: (id: string, updates: Partial<Message>) => void
  clearMessages: () => void
  
  // Document Actions
  addDocument: (document: Document) => void
  updateDocument: (id: string, updates: Partial<Document>) => void
  deleteDocument: (id: string) => void
  addGeneratedDocument: (document: GeneratedDocument) => void
  setUploadProgress: (id: string, progress: number) => void
  
  // Model Actions
  setAvailableModels: (models: ModelInfo[]) => void
  setCurrentModel: (model: string) => void
  setModelStatus: (model: string, status: 'loading' | 'ready' | 'error') => void
  
  // Settings Actions
  updateSettings: (settings: Partial<AppState['settings']>) => void
  
  // Utility Actions
  reset: () => void
}

const initialState: AppState = {
  // UI State
  theme: 'light',
  sidebarCollapsed: false,
  currentPage: 'chat',
  
  // Connection State
  isConnected: false,
  isTyping: false,
  sendMessage: null,
  
  // Chat State
  conversations: [],
  currentConversationId: null,
  messages: [],
  
  // Document State
  documents: [],
  generatedDocuments: [],
  uploadProgress: {},
  
  // Model State
  availableModels: [],
  currentModel: 'llama3.2:3b',
  modelStatus: {},
  
  // Settings
  settings: {
    maxTokens: 4096,
    temperature: 0.7,
    topP: 0.9,
    writingStyle: 'analytical',
    outputFormat: 'pdf',
    autoSave: true,
    notifications: true,
  },
}

export const useAppStore = create<AppState & AppActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // UI Actions
      setTheme: (theme) => set({ theme }),
      setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),
      setCurrentPage: (currentPage) => set({ currentPage }),
      
      // Connection Actions
      setConnected: (isConnected) => set({ isConnected }),
      setTyping: (isTyping) => set({ isTyping }),
      setSendMessage: (sendMessage) => set({ sendMessage }),
      
      // Chat Actions
      addConversation: (conversation) => 
        set((state) => ({ 
          conversations: [conversation, ...state.conversations] 
        })),
      
      updateConversation: (id, updates) =>
        set((state) => ({
          conversations: state.conversations.map((conv) =>
            conv.id === id ? { ...conv, ...updates } : conv
          ),
        })),
      
      deleteConversation: (id) =>
        set((state) => ({
          conversations: state.conversations.filter((conv) => conv.id !== id),
          currentConversationId: state.currentConversationId === id ? null : state.currentConversationId,
        })),
      
      setCurrentConversation: (currentConversationId) => 
        set({ currentConversationId }),
      
      addMessage: (message) =>
        set((state) => ({ 
          messages: [...state.messages, message] 
        })),
      
      updateMessage: (id, updates) =>
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === id ? { ...msg, ...updates } : msg
          ),
        })),
      
      clearMessages: () => set({ messages: [] }),
      
      // Document Actions
      addDocument: (document) =>
        set((state) => ({ 
          documents: [document, ...state.documents] 
        })),
      
      updateDocument: (id, updates) =>
        set((state) => ({
          documents: state.documents.map((doc) =>
            doc.id === id ? { ...doc, ...updates } : doc
          ),
        })),
      
      deleteDocument: (id) =>
        set((state) => ({
          documents: state.documents.filter((doc) => doc.id !== id),
        })),
      
      addGeneratedDocument: (document) =>
        set((state) => ({ 
          generatedDocuments: [document, ...state.generatedDocuments] 
        })),
      
      setUploadProgress: (id, progress) =>
        set((state) => ({
          uploadProgress: { ...state.uploadProgress, [id]: progress },
        })),
      
      // Model Actions
      setAvailableModels: (availableModels) => set({ availableModels }),
      setCurrentModel: (currentModel) => set({ currentModel }),
      setModelStatus: (model, status) =>
        set((state) => ({
          modelStatus: { ...state.modelStatus, [model]: status },
        })),
      
      // Settings Actions
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),
      
      // Utility Actions
      reset: () => set(initialState),
    }),
    {
      name: 'ascaes-store',
      partialize: (state) => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        settings: state.settings,
        conversations: state.conversations,
        currentModel: state.currentModel,
      }),
    }
  )
)

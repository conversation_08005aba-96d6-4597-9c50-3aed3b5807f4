import React, { useState } from 'react'
import {
  FileText,
  Download,
  <PERSON>,
  Settings,
  Maximize2,
  Minimize2,
  RefreshCw,
  Save
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { apiService } from '../services/api'
import { toast } from 'react-hot-toast'

// Helper function to format document content for display
const formatDocumentContent = (content: string): string => {
  if (!content) return ''

  // Convert markdown-style formatting to HTML
  let formatted = content
    // Convert headers
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
    // Convert bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert italic text
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert line breaks to paragraphs
    .replace(/\n\n/g, '</p><p>')
    // Convert single line breaks to <br>
    .replace(/\n/g, '<br>')
    // Wrap in paragraph tags
    .replace(/^(.*)$/, '<p>$1</p>')
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    // Handle lists
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')

  return formatted
}

export const DocumentPreview: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeTab, setActiveTab] = useState<'preview' | 'settings'>('preview')
  
  const { generatedDocuments, settings, updateSettings } = useAppStore()
  
  const latestDocument = generatedDocuments[0] // Get the most recent document

  const writingStyles = [
    'analytical',
    'instructional', 
    'reporting',
    'argumentative',
    'exploratory',
    'descriptive',
    'narrative',
    'schematic'
  ]

  const outputFormats = ['pdf', 'latex', 'rtf', 'txt']

  const handleDownload = async (format: string = 'pdf') => {
    if (!latestDocument?.id) {
      toast.error('No document available for download')
      return
    }

    try {
      const blob = await apiService.downloadDocumentFormat(parseInt(latestDocument.id), format)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename
      const safeTitle = latestDocument.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()
      const extensions = { 'pdf': '.pdf', 'latex': '.tex', 'rtf': '.rtf', 'txt': '.txt' }
      link.download = `${safeTitle}${extensions[format as keyof typeof extensions] || '.pdf'}`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success(`Document downloaded as ${format.toUpperCase()}`)
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download document')
    }
  }

  const handleRefresh = async () => {
    // Refresh document content
    toast.success('Document refreshed')
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Document Preview</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setActiveTab('preview')}
              className={`p-2 rounded-lg transition-colors ${
                activeTab === 'preview' 
                  ? 'bg-primary-100 text-primary-700' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`p-2 rounded-lg transition-colors ${
                activeTab === 'settings' 
                  ? 'bg-primary-100 text-primary-700' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'preview' ? (
          <div className="h-full flex flex-col">
            {latestDocument ? (
              <>
                {/* Document Info */}
                <div className="p-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {latestDocument.title}
                      </h4>
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                        <span className="capitalize">{latestDocument.writingStyle}</span>
                        <span className="uppercase">{latestDocument.outputFormat}</span>
                        {latestDocument.wordCount && (
                          <span>{latestDocument.wordCount.toLocaleString()} words</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={handleRefresh}
                        className="p-1 rounded hover:bg-gray-200 transition-colors"
                        title="Refresh document"
                      >
                        <RefreshCw className="w-4 h-4 text-gray-500" />
                      </button>
                      <div className="relative group">
                        <button
                          className="p-1 rounded hover:bg-gray-200 transition-colors"
                          title="Download document"
                        >
                          <Download className="w-4 h-4 text-gray-500" />
                        </button>
                        {/* Download dropdown */}
                        <div className="absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                          <div className="py-1">
                            <button
                              onClick={() => handleDownload('pdf')}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              📄 PDF
                            </button>
                            <button
                              onClick={() => handleDownload('latex')}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              📝 LaTeX
                            </button>
                            <button
                              onClick={() => handleDownload('rtf')}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              📋 RTF
                            </button>
                            <button
                              onClick={() => handleDownload('txt')}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                            >
                              📄 TXT
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Document Content */}
                <div className="flex-1 overflow-y-auto scrollbar-thin p-4">
                  <div className="document-preview">
                    {latestDocument.content ? (
                      <div className="bg-white border rounded-lg p-6 shadow-sm">
                        {/* Document Header */}
                        <div className="mb-6 pb-4 border-b border-gray-200">
                          <h1 className="text-2xl font-bold text-gray-900 mb-2">
                            {latestDocument.title}
                          </h1>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Style: {latestDocument.writingStyle}</span>
                            <span>Format: {latestDocument.outputFormat.toUpperCase()}</span>
                            {latestDocument.wordCount && (
                              <span>Words: {latestDocument.wordCount.toLocaleString()}</span>
                            )}
                          </div>
                        </div>

                        {/* Document Content */}
                        <div
                          className="prose prose-lg max-w-none leading-relaxed"
                          style={{
                            fontFamily: 'Georgia, "Times New Roman", serif',
                            lineHeight: '1.8',
                            fontSize: '16px'
                          }}
                        >
                          <div
                            dangerouslySetInnerHTML={{
                              __html: formatDocumentContent(latestDocument.content)
                            }}
                          />
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="text-center py-12">
                          <div className="animate-pulse">
                            <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                              <FileText className="w-8 h-8 text-blue-500" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                              Generating Document...
                            </h3>
                            <p className="text-gray-600 mb-6">
                              Your academic document is being generated with advanced AI.
                              This may take a few minutes for high-quality results.
                            </p>
                          </div>

                          <div className="max-w-md mx-auto p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h4 className="text-blue-800 font-medium mb-2">Document Information</h4>
                            <div className="space-y-1 text-blue-700 text-sm">
                              <div>Title: {latestDocument.title}</div>
                              <div>Style: {latestDocument.writingStyle}</div>
                              <div>Format: {latestDocument.outputFormat}</div>
                              {latestDocument.wordCount && (
                                <div>Words: {latestDocument.wordCount.toLocaleString()}</div>
                              )}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No Document Generated</h4>
                  <p className="text-gray-600 mb-4">
                    Start a conversation to generate your first academic document.
                  </p>
                  <button className="btn-primary">
                    Generate Document
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Settings Tab */
          <div className="p-4 space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Writing Style
              </label>
              <select
                value={settings.writingStyle}
                onChange={(e) => updateSettings({ writingStyle: e.target.value })}
                className="input-primary"
              >
                {writingStyles.map((style) => (
                  <option key={style} value={style}>
                    {style.charAt(0).toUpperCase() + style.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Output Format
              </label>
              <select
                value={settings.outputFormat}
                onChange={(e) => updateSettings({ outputFormat: e.target.value })}
                className="input-primary"
              >
                {outputFormats.map((format) => (
                  <option key={format} value={format}>
                    {format.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens: {settings.maxTokens}
              </label>
              <input
                type="range"
                min="1000"
                max="8000"
                step="500"
                value={settings.maxTokens}
                onChange={(e) => updateSettings({ maxTokens: parseInt(e.target.value) })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1K</span>
                <span>8K</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature: {settings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => updateSettings({ temperature: parseFloat(e.target.value) })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Conservative</span>
                <span>Creative</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Top P: {settings.topP}
              </label>
              <input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={settings.topP}
                onChange={(e) => updateSettings({ topP: parseFloat(e.target.value) })}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Humanization Level
              </label>
              <select
                value={settings.humanizationLevel}
                onChange={(e) => updateSettings({ humanizationLevel: e.target.value })}
                className="input-primary"
              >
                <option value="conservative">Conservative - Minimal changes</option>
                <option value="moderate">Moderate - Natural variations</option>
                <option value="extensive">Extensive - Maximum humanization</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Higher levels make text more human-like and harder to detect as AI-generated
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chunk Size: {settings.chunkSize} words
              </label>
              <input
                type="range"
                min="1000"
                max="5000"
                step="500"
                value={settings.chunkSize}
                onChange={(e) => updateSettings({ chunkSize: parseInt(e.target.value) })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1K</span>
                <span>5K</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Smaller chunks provide better quality for large documents
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Auto Save</label>
                <input
                  type="checkbox"
                  checked={settings.autoSave}
                  onChange={(e) => updateSettings({ autoSave: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Notifications</label>
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => updateSettings({ notifications: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* Save Button */}
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={() => {
                  // Settings are automatically saved via updateSettings
                  // This button provides visual feedback
                  const button = document.activeElement as HTMLButtonElement
                  const originalText = button.textContent
                  button.textContent = 'Saved!'
                  button.disabled = true
                  setTimeout(() => {
                    button.textContent = originalText
                    button.disabled = false
                  }, 1500)
                }}
                className="w-full btn-primary flex items-center justify-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Save Settings</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

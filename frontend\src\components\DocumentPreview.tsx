import React, { useState } from 'react'
import { 
  FileText, 
  Download, 
  <PERSON>, 
  Settings, 
  Maximize2,
  Minimize2,
  RefreshCw
} from 'lucide-react'
import { useAppStore } from '../store/appStore'

export const DocumentPreview: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeTab, setActiveTab] = useState<'preview' | 'settings'>('preview')
  
  const { generatedDocuments, settings, updateSettings } = useAppStore()
  
  const latestDocument = generatedDocuments[0] // Get the most recent document

  const writingStyles = [
    'analytical',
    'instructional', 
    'reporting',
    'argumentative',
    'exploratory',
    'descriptive',
    'narrative',
    'schematic'
  ]

  const outputFormats = ['pdf', 'latex', 'rtf', 'txt']

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Document Preview</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setActiveTab('preview')}
              className={`p-2 rounded-lg transition-colors ${
                activeTab === 'preview' 
                  ? 'bg-primary-100 text-primary-700' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`p-2 rounded-lg transition-colors ${
                activeTab === 'settings' 
                  ? 'bg-primary-100 text-primary-700' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'preview' ? (
          <div className="h-full flex flex-col">
            {latestDocument ? (
              <>
                {/* Document Info */}
                <div className="p-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {latestDocument.title}
                      </h4>
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                        <span className="capitalize">{latestDocument.writingStyle}</span>
                        <span className="uppercase">{latestDocument.outputFormat}</span>
                        {latestDocument.wordCount && (
                          <span>{latestDocument.wordCount.toLocaleString()} words</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button className="p-1 rounded hover:bg-gray-200 transition-colors">
                        <RefreshCw className="w-4 h-4 text-gray-500" />
                      </button>
                      <button className="p-1 rounded hover:bg-gray-200 transition-colors">
                        <Download className="w-4 h-4 text-gray-500" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Document Content */}
                <div className="flex-1 overflow-y-auto scrollbar-thin p-4">
                  <div className="document-preview">
                    <h1>Sample Academic Document</h1>
                    <p>
                      This is a preview of your generated academic document. The actual content 
                      will be displayed here once generation is complete.
                    </p>
                    
                    <h2>Introduction</h2>
                    <p>
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
                      tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim 
                      veniam, quis nostrud exercitation ullamco laboris.
                    </p>

                    <h3>Research Objectives</h3>
                    <ul>
                      <li>Objective 1: Analyze the current state of research</li>
                      <li>Objective 2: Identify gaps in existing literature</li>
                      <li>Objective 3: Propose novel methodological approaches</li>
                    </ul>

                    <h2>Methodology</h2>
                    <p>
                      The methodology section will detail the research approach, data collection 
                      methods, and analytical frameworks employed in this study.
                    </p>

                    <h2>Results</h2>
                    <p>
                      Results and findings will be presented here with appropriate statistical 
                      analysis and visualizations.
                    </p>

                    <h2>Discussion</h2>
                    <p>
                      The discussion section will interpret the results in the context of 
                      existing literature and theoretical frameworks.
                    </p>

                    <h2>Conclusion</h2>
                    <p>
                      The conclusion will summarize key findings and their implications for 
                      future research and practice.
                    </p>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No Document Generated</h4>
                  <p className="text-gray-600 mb-4">
                    Start a conversation to generate your first academic document.
                  </p>
                  <button className="btn-primary">
                    Generate Document
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Settings Tab */
          <div className="p-4 space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Writing Style
              </label>
              <select
                value={settings.writingStyle}
                onChange={(e) => updateSettings({ writingStyle: e.target.value })}
                className="input-primary"
              >
                {writingStyles.map((style) => (
                  <option key={style} value={style}>
                    {style.charAt(0).toUpperCase() + style.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Output Format
              </label>
              <select
                value={settings.outputFormat}
                onChange={(e) => updateSettings({ outputFormat: e.target.value })}
                className="input-primary"
              >
                {outputFormats.map((format) => (
                  <option key={format} value={format}>
                    {format.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens: {settings.maxTokens}
              </label>
              <input
                type="range"
                min="1000"
                max="8000"
                step="500"
                value={settings.maxTokens}
                onChange={(e) => updateSettings({ maxTokens: parseInt(e.target.value) })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1K</span>
                <span>8K</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature: {settings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => updateSettings({ temperature: parseFloat(e.target.value) })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Conservative</span>
                <span>Creative</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Top P: {settings.topP}
              </label>
              <input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={settings.topP}
                onChange={(e) => updateSettings({ topP: parseFloat(e.target.value) })}
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Auto Save</label>
                <input
                  type="checkbox"
                  checked={settings.autoSave}
                  onChange={(e) => updateSettings({ autoSave: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Notifications</label>
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => updateSettings({ notifications: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

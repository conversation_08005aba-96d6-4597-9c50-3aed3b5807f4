"""
Mock Ollama service for testing when Ollama is not working
"""

import asyncio
import random
from typing import Dict, Any, Optional, List
from core.logger import logger

class MockOllamaService:
    """Mock Ollama service that generates realistic academic content"""
    
    def __init__(self):
        self.base_url = "http://localhost:11434"
        
    async def generate(
        self,
        model: str,
        prompt: str,
        system: Optional[str] = None,
        template: Optional[str] = None,
        context: Optional[List[int]] = None,
        stream: bool = False,
        raw: bool = False,
        format: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate mock academic content based on the prompt"""
        
        # Simulate generation time
        await asyncio.sleep(2)
        
        # Extract topic from prompt
        topic = self._extract_topic(prompt)
        target_words = self._extract_word_count(prompt)
        
        logger.info(f"Mock generation: {target_words} words about '{topic}'")
        
        # Generate realistic academic content
        content = self._generate_academic_content(topic, target_words)
        
        return {
            "response": content,
            "model": model,
            "created_at": "2025-06-28T00:00:00Z",
            "done": True,
            "eval_count": len(content.split()) * 1.3,  # Approximate token count
            "eval_duration": 2000000000  # 2 seconds in nanoseconds
        }
    
    def _extract_topic(self, prompt: str) -> str:
        """Extract the main topic from the prompt"""
        # Look for common patterns
        if "about" in prompt.lower():
            parts = prompt.lower().split("about")
            if len(parts) > 1:
                topic_part = parts[1].split(".")[0].split(",")[0].strip()
                return topic_part.replace('"', '').replace("'", "")
        
        # Fallback patterns
        for keyword in ["Bangladesh economy", "artificial intelligence", "climate change", "machine learning"]:
            if keyword.lower() in prompt.lower():
                return keyword
                
        return "academic research"
    
    def _extract_word_count(self, prompt: str) -> int:
        """Extract target word count from prompt"""
        import re
        
        # Look for word count patterns
        word_patterns = [
            r"(\d+)\s*words?",
            r"(\d+)-word",
            r"Target Length:\s*EXACTLY\s*(\d+)\s*words?",
            r"approximately\s*(\d+)\s*words?"
        ]
        
        for pattern in word_patterns:
            match = re.search(pattern, prompt, re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        return 1000  # Default
    
    def _generate_academic_content(self, topic: str, target_words: int) -> str:
        """Generate realistic academic content"""
        
        # Academic content templates based on topic
        if "bangladesh" in topic.lower() and "economy" in topic.lower():
            return self._generate_bangladesh_economy_content(target_words)
        elif "artificial intelligence" in topic.lower() or "ai" in topic.lower():
            return self._generate_ai_content(target_words)
        else:
            return self._generate_generic_academic_content(topic, target_words)
    
    def _generate_bangladesh_economy_content(self, target_words: int) -> str:
        """Generate specific content about Bangladesh economy"""
        
        sections = [
            {
                "title": "Introduction to Bangladesh Economy",
                "content": """Bangladesh has emerged as one of the fastest-growing economies in South Asia, with a GDP growth rate averaging 6-7% over the past decade. The country's economy is characterized by its strong textile and garment industry, which accounts for approximately 84% of total export earnings. With a population of over 165 million people, Bangladesh represents a significant market in the region.

The economic transformation of Bangladesh since its independence in 1971 has been remarkable. From a primarily agricultural economy, the country has diversified into manufacturing, services, and technology sectors. The ready-made garment (RMG) industry has been the backbone of this transformation, employing over 4 million workers and contributing significantly to foreign exchange earnings."""
            },
            {
                "title": "Key Economic Sectors",
                "content": """The textile and garment industry remains the dominant sector, contributing over 13% to GDP and providing employment to millions of workers, particularly women. Major international brands source their products from Bangladesh due to competitive labor costs and improving quality standards.

Agriculture continues to play a vital role, employing about 40% of the workforce and contributing approximately 14% to GDP. Rice is the primary crop, with Bangladesh being one of the world's largest rice producers. The country has achieved food security through improved agricultural practices and high-yielding variety seeds.

The services sector has shown significant growth, contributing over 50% to GDP. This includes banking, telecommunications, transportation, and information technology services. The IT sector, in particular, has emerged as a promising area with government support for digital Bangladesh initiatives."""
            },
            {
                "title": "Economic Challenges and Opportunities",
                "content": """Despite impressive growth, Bangladesh faces several economic challenges. Infrastructure development remains a priority, with ongoing projects like the Padma Bridge and metro rail systems in Dhaka aimed at improving connectivity and reducing transportation costs.

The country's graduation from Least Developed Country (LDC) status by 2026 presents both opportunities and challenges. While it signifies economic progress, it also means the loss of preferential trade benefits, requiring enhanced competitiveness in global markets.

Climate change poses significant risks to the economy, particularly affecting agriculture and coastal areas. The government has initiated various adaptation and mitigation measures, including the Delta Plan 2100, to address these challenges while maintaining economic growth."""
            }
        ]
        
        # Calculate words per section
        words_per_section = target_words // len(sections)
        
        content = f"# The Economy of Bangladesh: Growth, Challenges, and Future Prospects\n\n"
        content += "## Abstract\n\n"
        content += "This comprehensive analysis examines the economic development of Bangladesh, focusing on key sectors, growth drivers, and future challenges. The study reveals significant progress in industrialization while highlighting areas requiring continued attention for sustainable development.\n\n"
        
        for section in sections:
            content += f"## {section['title']}\n\n"
            content += section['content']
            
            # Add more content if needed to reach target words
            if len(content.split()) < words_per_section * (sections.index(section) + 1):
                content += f"\n\nFurther analysis of {section['title'].lower()} reveals additional complexities and nuances that require detailed examination. The interconnected nature of economic factors means that developments in this area have far-reaching implications for overall economic performance and social development."
            
            content += "\n\n"
        
        content += "## Conclusion\n\n"
        content += "Bangladesh's economic journey demonstrates remarkable resilience and growth potential. While challenges remain, the country's strategic position, young workforce, and ongoing reforms position it well for continued economic development in the coming decades.\n\n"
        content += "## References\n\n"
        content += "[1] Bangladesh Bureau of Statistics. (2024). Economic Survey of Bangladesh.\n"
        content += "[2] World Bank. (2024). Bangladesh Development Update.\n"
        content += "[3] Asian Development Bank. (2024). Asian Development Outlook: Bangladesh.\n"
        
        return content
    
    def _generate_ai_content(self, target_words: int) -> str:
        """Generate content about AI"""
        return f"""# Artificial Intelligence: Current State and Future Directions

## Abstract

Artificial Intelligence (AI) has emerged as one of the most transformative technologies of the 21st century, revolutionizing industries and reshaping how we interact with technology. This comprehensive analysis examines the current state of AI development, its applications across various sectors, and the implications for future technological advancement.

## Introduction

The field of artificial intelligence has experienced unprecedented growth in recent years, driven by advances in machine learning, deep learning, and neural network architectures. From its theoretical foundations in the 1950s to today's practical applications, AI has evolved from a niche academic discipline to a mainstream technology with widespread commercial and social impact.

## Current Applications and Technologies

Modern AI systems demonstrate remarkable capabilities in natural language processing, computer vision, and decision-making tasks. Large language models like GPT and BERT have revolutionized text generation and understanding, while convolutional neural networks have achieved superhuman performance in image recognition tasks.

The integration of AI into everyday applications has become increasingly seamless, from recommendation systems in streaming platforms to autonomous vehicle navigation systems. These applications demonstrate the practical value of AI technologies in solving real-world problems and improving user experiences.

## Challenges and Ethical Considerations

Despite significant progress, AI development faces numerous challenges including bias in training data, interpretability of complex models, and the need for robust safety measures. The ethical implications of AI deployment require careful consideration of privacy, fairness, and accountability in automated decision-making systems.

## Future Prospects

The future of AI holds immense potential for addressing global challenges in healthcare, climate change, and education. Continued research in areas such as artificial general intelligence, quantum computing integration, and human-AI collaboration will likely define the next phase of technological evolution.

## Conclusion

Artificial intelligence represents a paradigm shift in computational capabilities and problem-solving approaches. As the technology continues to mature, its responsible development and deployment will be crucial for maximizing benefits while minimizing potential risks to society.

This analysis provides approximately {target_words} words of comprehensive coverage on artificial intelligence, examining both technical aspects and broader implications for society and future development."""
    
    def _generate_generic_academic_content(self, topic: str, target_words: int) -> str:
        """Generate generic academic content for any topic"""
        return f"""# Academic Analysis of {topic.title()}

## Abstract

This comprehensive study examines {topic}, providing detailed analysis of current research, methodologies, and findings in this important field of study. The research employs systematic approaches to present evidence-based conclusions and recommendations for future investigation.

## Introduction

The study of {topic} has gained significant attention in recent academic discourse. This document presents a thorough examination of current research trends, theoretical frameworks, and practical applications. The analytical approach ensures comprehensive coverage while maintaining academic rigor and scholarly standards.

## Literature Review and Background

Current research in {topic} demonstrates several key developments and emerging trends. Recent studies have contributed significantly to our understanding of fundamental principles and practical applications. The literature reveals both established theoretical foundations and innovative approaches to addressing complex challenges in this field.

Scholarly work in this area encompasses diverse methodological approaches, from quantitative analysis to qualitative research methods. The interdisciplinary nature of {topic} requires integration of multiple theoretical perspectives and empirical findings from various related fields.

## Methodology and Analytical Framework

This research utilizes a systematic analytical approach to examine available information and present findings in a coherent, academically sound manner. The methodology ensures comprehensive coverage of relevant topics while maintaining analytical depth and scholarly objectivity.

The analytical framework incorporates both theoretical considerations and practical applications, providing a balanced perspective on current knowledge and future research directions. This approach facilitates meaningful conclusions and actionable recommendations.

## Results and Discussion

The analysis reveals several important insights and significant findings related to {topic}. These results contribute meaningfully to the existing body of knowledge and provide a foundation for future research directions and practical applications.

Key findings demonstrate the complexity and multifaceted nature of {topic}, highlighting the need for continued investigation and interdisciplinary collaboration. The discussion integrates current findings with established theoretical frameworks and identifies areas requiring further research attention.

## Implications and Future Directions

The findings of this study have important implications for both theoretical understanding and practical applications in {topic}. Future research should focus on addressing identified gaps in current knowledge while building upon the established foundation of scholarly work.

Recommendations for future investigation include expanded empirical studies, development of new theoretical models, and exploration of innovative methodological approaches. These directions will contribute to continued advancement in understanding {topic}.

## Conclusion

This comprehensive analysis successfully presents an overview of {topic}, demonstrating effective application of analytical methodology in academic research. The findings contribute to ongoing scholarly discourse and suggest promising areas for continued investigation and development.

The study provides approximately {target_words} words of detailed academic analysis, maintaining scholarly standards while offering comprehensive coverage of the subject matter."""

# Create global instance
mock_ollama_service = MockOllamaService()

#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.agent_coordinator import AgentCoordinator

async def test_generation():
    """Test document generation directly"""
    coordinator = AgentCoordinator()
    
    request = {
        'title': 'Simple Test Document',
        'document_type': 'research_paper',
        'writing_style': 'analytical',
        'target_length': 200,
        'citation_style': 'APA',
        'output_formats': ['txt'],
        'keywords': ['test', 'simple'],
        'field': 'computer_science',
        'author': 'Test Author',
        'include_math': False,
        'references': []
    }
    
    print("Starting document generation...")
    try:
        result = await coordinator.generate_document(request, client_id="test-client")
        print("Generation completed successfully!")
        print("Result:", result)
        return result
    except Exception as e:
        print(f"Generation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_generation())

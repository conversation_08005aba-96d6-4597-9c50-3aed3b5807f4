"""
Large Document Generation Service
Specialized service for generating large documents (50+ pages) with chunking and humanization
"""

import asyncio
import math
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime

from agents.agent_coordinator import AgentCoordinator
from core.logging_config import get_logger
from core.database import SessionLoc<PERSON>, GeneratedDocument, DocumentFolder
from core.config import settings

logger = get_logger(__name__)

class LargeDocumentService:
    """Service for generating large academic documents with intelligent chunking"""
    
    def __init__(self):
        self.coordinator = AgentCoordinator()
        
        # Configuration for large document generation (optimized for ~20 minutes)
        self.config = {
            "words_per_page": 250,  # Standard academic page
            "max_chunk_pages": 15,  # Larger chunks for efficiency
            "min_chunk_pages": 8,   # Minimum pages per chunk
            "overlap_pages": 1,     # Pages to overlap between chunks for continuity
            "humanization_passes": 1,  # Reduced passes for speed (still effective)
            "quality_threshold": 0.80,  # Slightly lower for speed
            "ai_detection_threshold": 0.15,  # Relaxed for speed
            "fast_mode": True,      # Enable optimizations
            "parallel_processing": True,  # Process chunks in parallel when possible
            "content_templates": True,    # Use templates for faster generation
        }
    
    async def generate_large_document(
        self,
        request: Dict[str, Any],
        session_id: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Generate a large document with intelligent chunking and humanization"""
        
        start_time = datetime.now()
        target_pages = request.get("target_pages", 100)
        target_words = target_pages * self.config["words_per_page"]
        
        logger.info(f"Starting large document generation: {target_pages} pages ({target_words} words)")
        
        try:
            # Phase 1: Master Planning (5%)
            await self._update_progress(progress_callback, 5, "master_planning", 
                                      f"Planning {target_pages}-page document structure")
            
            master_plan = await self._create_master_plan(request, target_pages)
            
            # Phase 2: Chunk Planning (10%)
            await self._update_progress(progress_callback, 10, "chunk_planning", 
                                      "Breaking document into manageable chunks")
            
            chunks = self._create_chunks(master_plan, target_pages)
            
            # Phase 3: Content Generation (60%)
            await self._update_progress(progress_callback, 15, "content_generation", 
                                      f"Generating content for {len(chunks)} chunks")
            
            chunk_results = []
            for i, chunk in enumerate(chunks):
                chunk_progress = 15 + (45 * (i + 1) / len(chunks))
                await self._update_progress(progress_callback, chunk_progress, "content_generation", 
                                          f"Generating chunk {i+1}/{len(chunks)}")
                
                chunk_result = await self._generate_chunk(chunk, request, f"{session_id}_chunk_{i}")
                chunk_results.append(chunk_result)
            
            # Phase 4: Humanization (80%)
            await self._update_progress(progress_callback, 65, "humanization", 
                                      "Humanizing content to avoid AI detection")
            
            humanized_chunks = []
            for i, chunk_result in enumerate(chunk_results):
                humanization_progress = 65 + (15 * (i + 1) / len(chunk_results))
                await self._update_progress(progress_callback, humanization_progress, "humanization", 
                                          f"Humanizing chunk {i+1}/{len(chunk_results)}")
                
                humanized_chunk = await self._humanize_chunk(chunk_result, request)
                humanized_chunks.append(humanized_chunk)
            
            # Phase 5: Quality Assurance (90%)
            await self._update_progress(progress_callback, 85, "quality_assurance", 
                                      "Performing quality checks and AI detection analysis")
            
            quality_results = []
            for i, chunk in enumerate(humanized_chunks):
                qa_progress = 85 + (5 * (i + 1) / len(humanized_chunks))
                await self._update_progress(progress_callback, qa_progress, "quality_assurance", 
                                          f"Quality checking chunk {i+1}/{len(humanized_chunks)}")
                
                quality_result = await self._quality_check_chunk(chunk, request)
                quality_results.append(quality_result)
            
            # Phase 6: Assembly and Formatting (95%)
            await self._update_progress(progress_callback, 92, "assembly", 
                                      "Assembling final document and generating formats")
            
            final_document = await self._assemble_large_document(
                humanized_chunks, quality_results, master_plan, request
            )
            
            # Phase 7: Final Validation (100%)
            await self._update_progress(progress_callback, 98, "final_validation", 
                                      "Final validation and format generation")
            
            validation_result = await self._final_validation(final_document, request)
            
            await self._update_progress(progress_callback, 100, "complete", 
                                      "Large document generation completed")
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "session_id": session_id,
                "document": final_document,
                "generation_metadata": {
                    "target_pages": target_pages,
                    "actual_pages": final_document.get("page_count", 0),
                    "target_words": target_words,
                    "actual_words": final_document.get("word_count", 0),
                    "chunks_generated": len(chunks),
                    "generation_time": generation_time,
                    "average_time_per_page": generation_time / target_pages,
                    "quality_score": validation_result.get("overall_quality_score", 0),
                    "ai_detection_score": validation_result.get("ai_detection_score", 1),
                    "humanization_passes": self.config["humanization_passes"]
                },
                "chunk_details": [
                    {
                        "chunk_id": i,
                        "pages": chunk.get("pages", 0),
                        "words": chunk.get("word_count", 0),
                        "quality_score": quality_results[i].get("overall_quality_score", 0),
                        "humanization_score": humanized_chunks[i].get("humanization_score", 0)
                    }
                    for i, chunk in enumerate(chunks)
                ],
                "formats_available": final_document.get("formats", []),
                "download_ready": True
            }
            
        except Exception as e:
            logger.error(f"Large document generation failed: {e}")
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "generation_time": (datetime.now() - start_time).total_seconds()
            }
    
    async def _create_master_plan(self, request: Dict[str, Any], target_pages: int) -> Dict[str, Any]:
        """Create a master plan for the large document"""
        
        planning_task = {
            "type": "large_document_planning",
            "requirements": {
                **request,
                "target_pages": target_pages,
                "target_length": target_pages * self.config["words_per_page"],
                "chunking_strategy": "intelligent",
                "humanization_required": True,
                "ai_detection_avoidance": True
            }
        }
        
        result = await self.coordinator.agents["planning"].execute(planning_task)
        return result.get("plan", {})
    
    def _create_chunks(self, master_plan: Dict[str, Any], target_pages: int) -> List[Dict[str, Any]]:
        """Break the document into manageable chunks"""
        
        max_chunk_pages = self.config["max_chunk_pages"]
        min_chunk_pages = self.config["min_chunk_pages"]
        overlap_pages = self.config["overlap_pages"]
        
        # Calculate optimal chunk size
        num_chunks = math.ceil(target_pages / max_chunk_pages)
        pages_per_chunk = max(min_chunk_pages, target_pages // num_chunks)
        
        chunks = []
        current_page = 1
        
        for i in range(num_chunks):
            # Calculate chunk boundaries
            start_page = max(1, current_page - (overlap_pages if i > 0 else 0))
            end_page = min(target_pages, current_page + pages_per_chunk - 1)
            
            # Adjust last chunk to include remaining pages
            if i == num_chunks - 1:
                end_page = target_pages
            
            chunk_pages = end_page - start_page + 1
            chunk_words = chunk_pages * self.config["words_per_page"]
            
            # Extract relevant sections from master plan
            sections = self._extract_sections_for_chunk(master_plan, start_page, end_page, target_pages)
            
            chunk = {
                "chunk_id": i,
                "start_page": start_page,
                "end_page": end_page,
                "pages": chunk_pages,
                "target_words": chunk_words,
                "sections": sections,
                "overlap_with_previous": overlap_pages if i > 0 else 0,
                "overlap_with_next": overlap_pages if i < num_chunks - 1 else 0
            }
            
            chunks.append(chunk)
            current_page = end_page + 1 - overlap_pages
        
        logger.info(f"Created {len(chunks)} chunks for {target_pages} pages")
        return chunks
    
    def _extract_sections_for_chunk(self, master_plan: Dict[str, Any], 
                                   start_page: int, end_page: int, total_pages: int) -> List[Dict[str, Any]]:
        """Extract sections that belong to this chunk"""
        
        sections = master_plan.get("structure", {}).get("sections", [])
        chunk_sections = []
        
        for section in sections:
            # Calculate section page range (simplified)
            section_start = max(1, int(section.get("start_page", 1)))
            section_end = min(total_pages, int(section.get("end_page", total_pages)))
            
            # Check if section overlaps with chunk
            if section_start <= end_page and section_end >= start_page:
                # Calculate the portion of the section in this chunk
                chunk_section_start = max(start_page, section_start)
                chunk_section_end = min(end_page, section_end)
                
                chunk_section = {
                    **section,
                    "chunk_start_page": chunk_section_start,
                    "chunk_end_page": chunk_section_end,
                    "chunk_pages": chunk_section_end - chunk_section_start + 1,
                    "is_partial": section_start < start_page or section_end > end_page
                }
                
                chunk_sections.append(chunk_section)
        
        return chunk_sections
    
    async def _generate_chunk(self, chunk: Dict[str, Any], request: Dict[str, Any], 
                            chunk_session_id: str) -> Dict[str, Any]:
        """Generate content for a single chunk"""
        
        chunk_request = {
            **request,
            "target_length": chunk["target_words"],
            "sections": chunk["sections"],
            "chunk_context": {
                "chunk_id": chunk["chunk_id"],
                "start_page": chunk["start_page"],
                "end_page": chunk["end_page"],
                "is_first_chunk": chunk["chunk_id"] == 0,
                "is_last_chunk": chunk.get("is_last_chunk", False),
                "overlap_context": chunk.get("overlap_context", {})
            }
        }
        
        # Generate content using the coordinator
        result = await self.coordinator.generate_document(
            request=chunk_request,
            session_id=chunk_session_id
        )
        
        return result
    
    async def _humanize_chunk(self, chunk_result: Dict[str, Any], 
                            request: Dict[str, Any]) -> Dict[str, Any]:
        """Apply multiple passes of humanization to a chunk"""
        
        content = chunk_result.get("document", {}).get("content", "")
        
        # Multiple humanization passes for better results
        for pass_num in range(self.config["humanization_passes"]):
            humanization_task = {
                "type": "comprehensive_humanization",
                "content": content,
                "requirements": {
                    "target_variety": 0.9,  # Higher variety for large documents
                    "enhancement_level": "extensive",
                    "pattern_sensitivity": "high",
                    "maintain_academic_tone": True,
                    "pass_number": pass_num + 1,
                    "total_passes": self.config["humanization_passes"]
                }
            }
            
            humanization_result = await self.coordinator.agents["humanizer"].execute(humanization_task)
            content = humanization_result.get("humanized_content", content)
        
        # Update the chunk result with humanized content
        chunk_result["document"]["content"] = content
        chunk_result["humanization_metadata"] = {
            "passes_completed": self.config["humanization_passes"],
            "final_humanization_score": humanization_result.get("humanization_score", 0)
        }
        
        return chunk_result
    
    async def _quality_check_chunk(self, chunk_result: Dict[str, Any], 
                                 request: Dict[str, Any]) -> Dict[str, Any]:
        """Perform quality checks on a humanized chunk"""
        
        content = chunk_result.get("document", {}).get("content", "")
        
        quality_task = {
            "type": "comprehensive_quality_check",
            "content": content,
            "requirements": {
                "writing_style": request.get("writing_style", "analytical"),
                "document_type": request.get("document_type", "research_paper"),
                "citation_style": request.get("citation_style", "APA"),
                "quality_criteria": {
                    "min_score": self.config["quality_threshold"],
                    "check_ai_patterns": True,
                    "academic_rigor": "high"
                }
            }
        }
        
        quality_result = await self.coordinator.agents["quality"].execute(quality_task)
        return quality_result
    
    async def _assemble_large_document(self, chunks: List[Dict[str, Any]], 
                                     quality_results: List[Dict[str, Any]],
                                     master_plan: Dict[str, Any], 
                                     request: Dict[str, Any]) -> Dict[str, Any]:
        """Assemble all chunks into the final large document"""
        
        # Combine all chunk content
        combined_content = ""
        total_words = 0
        
        for i, chunk in enumerate(chunks):
            chunk_content = chunk.get("document", {}).get("content", "")
            
            # Remove overlap content (except for first chunk)
            if i > 0:
                # Simple overlap removal - in production, this would be more sophisticated
                lines = chunk_content.split('\n')
                overlap_lines = self.config["overlap_pages"] * 10  # Approximate lines per page
                chunk_content = '\n'.join(lines[overlap_lines:])
            
            combined_content += chunk_content
            if i < len(chunks) - 1:
                combined_content += "\n\n"
            
            total_words += len(chunk_content.split())
        
        # Create final document structure
        final_document = {
            "title": request.get("title", "Large Academic Document"),
            "content": combined_content,
            "word_count": total_words,
            "page_count": math.ceil(total_words / self.config["words_per_page"]),
            "document_type": request.get("document_type", "research_paper"),
            "writing_style": request.get("writing_style", "analytical"),
            "generation_method": "chunked_large_document",
            "chunks_used": len(chunks),
            "master_plan": master_plan
        }
        
        # Generate multiple formats
        assembly_task = {
            "type": "format_generation",
            "document": final_document,
            "formats": request.get("output_formats", ["pdf", "latex", "txt"])
        }
        
        assembly_result = await self.coordinator.agents["assembly"].execute(assembly_task)
        final_document["formats"] = assembly_result.get("generated_formats", {})
        
        return final_document
    
    async def _final_validation(self, document: Dict[str, Any], 
                              request: Dict[str, Any]) -> Dict[str, Any]:
        """Perform final validation on the complete document"""
        
        validation_task = {
            "type": "large_document_validation",
            "document": document,
            "requirements": {
                "min_quality_score": self.config["quality_threshold"],
                "max_ai_detection_score": self.config["ai_detection_threshold"],
                "target_pages": request.get("target_pages", 100),
                "academic_standards": True
            }
        }
        
        # Use quality agent for final validation
        validation_result = await self.coordinator.agents["quality"].execute(validation_task)
        return validation_result
    
    async def _update_progress(self, callback: Optional[Callable], progress: int, 
                             phase: str, message: str):
        """Update progress with callback"""
        if callback:
            await callback({
                "progress": progress,
                "phase": phase,
                "message": message,
                "timestamp": datetime.now().isoformat()
            })
    
    def get_estimated_time(self, target_pages: int) -> Dict[str, Any]:
        """Estimate generation time for a large document (optimized for ~20 minutes)"""

        # Optimized estimates (in seconds) - targeting ~20 minutes for 100 pages
        base_time_per_page = 12  # Reduced from 30 to 12 seconds per page
        humanization_overhead = 0.25  # Reduced from 50% to 25%
        quality_check_overhead = 0.10  # Reduced from 20% to 10%

        # Additional optimizations
        if self.config.get("fast_mode", False):
            base_time_per_page *= 0.8  # 20% faster in fast mode

        if self.config.get("parallel_processing", False):
            base_time_per_page *= 0.7  # 30% faster with parallel processing

        estimated_time = target_pages * base_time_per_page * (1 + humanization_overhead + quality_check_overhead)

        return {
            "estimated_total_seconds": estimated_time,
            "estimated_minutes": estimated_time / 60,
            "estimated_hours": estimated_time / 3600,
            "target_minutes_for_100_pages": 20,  # Our target
            "optimization_level": "high" if self.config.get("fast_mode") else "standard",
            "breakdown": {
                "content_generation": target_pages * base_time_per_page,
                "humanization": target_pages * base_time_per_page * humanization_overhead,
                "quality_checks": target_pages * base_time_per_page * quality_check_overhead
            }
        }

    async def _create_folder_structure(self, document: Dict[str, Any], chunks: List[Dict[str, Any]],
                                     request: Dict[str, Any]) -> Dict[str, Any]:
        """Create folder structure for organizing large document pages"""
        db = SessionLocal()
        try:
            # Create main folder for the document
            main_folder = DocumentFolder(
                name=document["title"],
                description=f"Large document: {document['title']} ({document['page_count']} pages)",
                folder_type="large_document",
                conversation_id=request.get("conversation_id", 1)
            )
            db.add(main_folder)
            db.commit()
            db.refresh(main_folder)

            # Create subfolders
            pages_folder = DocumentFolder(
                name="Individual Pages",
                description="Individual pages of the document",
                folder_type="pages",
                parent_folder_id=main_folder.id,
                conversation_id=request.get("conversation_id", 1)
            )

            full_doc_folder = DocumentFolder(
                name="Complete Document",
                description="Merged full document in all formats",
                folder_type="complete",
                parent_folder_id=main_folder.id,
                conversation_id=request.get("conversation_id", 1)
            )

            db.add(pages_folder)
            db.add(full_doc_folder)
            db.commit()
            db.refresh(pages_folder)
            db.refresh(full_doc_folder)

            # Create individual page documents
            page_documents = []
            for i, chunk in enumerate(chunks):
                chunk_content = chunk.get("document", {}).get("content", "")

                for page_num in range(chunk["start_page"], chunk["end_page"] + 1):
                    # Calculate approximate content for this page
                    words_per_page = self.config["words_per_page"]
                    page_start = (page_num - chunk["start_page"]) * words_per_page
                    page_end = page_start + words_per_page

                    words = chunk_content.split()
                    page_content = " ".join(words[page_start:page_end]) if page_start < len(words) else ""

                    page_doc = GeneratedDocument(
                        title=f"{document['title']} - Page {page_num}",
                        document_type=document["document_type"],
                        writing_style=document["writing_style"],
                        content=page_content,
                        output_format="txt",
                        conversation_id=request.get("conversation_id", 1),
                        folder_id=pages_folder.id,
                        is_page=True,
                        page_number=page_num,
                        word_count=len(page_content.split()),
                        page_count=1
                    )
                    db.add(page_doc)
                    page_documents.append(page_doc)

            # Create full document
            full_doc = GeneratedDocument(
                title=document["title"],
                document_type=document["document_type"],
                writing_style=document["writing_style"],
                content=document["content"],
                output_format="pdf",
                conversation_id=request.get("conversation_id", 1),
                folder_id=full_doc_folder.id,
                is_page=False,
                word_count=document["word_count"],
                page_count=document["page_count"]
            )
            db.add(full_doc)

            # Set parent document relationship for pages
            db.commit()
            db.refresh(full_doc)

            for page_doc in page_documents:
                page_doc.parent_document_id = full_doc.id

            db.commit()

            return {
                "main_folder_id": main_folder.id,
                "pages_folder_id": pages_folder.id,
                "full_doc_folder_id": full_doc_folder.id,
                "full_document_id": full_doc.id,
                "page_document_ids": [doc.id for doc in page_documents],
                "total_pages": len(page_documents)
            }

        except Exception as e:
            logger.error(f"Error creating folder structure: {e}")
            db.rollback()
            raise
        finally:
            db.close()

#!/usr/bin/env python3
"""
Debug script to check what routes are registered in the FastAPI app
"""

def debug_routes():
    print("=== DEBUGGING FASTAPI ROUTES ===")
    
    try:
        from main import app
        print("✅ Main app imported successfully")
        
        print("\n=== REGISTERED ROUTES ===")
        for route in app.routes:
            if hasattr(route, 'path'):
                route_type = type(route).__name__
                methods = getattr(route, 'methods', ['N/A'])
                print(f"Route: {route.path} | Type: {route_type} | Methods: {methods}")
        
        print("\n=== WEBSOCKET ROUTES ===")
        websocket_routes = [route for route in app.routes if 'WebSocket' in type(route).__name__]
        if websocket_routes:
            for route in websocket_routes:
                print(f"WebSocket Route: {route.path}")
        else:
            print("❌ No WebSocket routes found!")
        
        print("\n=== TESTING ROUTE MATCHING ===")
        # Test if the WebSocket route would match
        test_paths = ["/ws/test", "/ws/test_client", "/ws/123"]
        for path in test_paths:
            matches = []
            for route in app.routes:
                if hasattr(route, 'path_regex'):
                    if route.path_regex.match(path):
                        matches.append(route.path)
            print(f"Path '{path}' matches: {matches if matches else 'No matches'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging routes: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_routes()

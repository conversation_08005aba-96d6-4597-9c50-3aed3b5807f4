#!/usr/bin/env python3
"""
Test Large Document API
Simple test to demonstrate the large document generation API
"""

import requests
import json
import time

def test_large_document_api():
    """Test the large document generation API"""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing ASCAES Large Document API")
    print("=" * 50)
    
    # Test 1: Get time estimate
    print("\n1️⃣ Testing Time Estimation...")
    try:
        response = requests.get(f"{base_url}/api/agents/estimate/large/100")
        if response.status_code == 200:
            estimate = response.json()
            print("✅ Time estimation successful!")
            print(f"   Target Pages: {estimate['target_pages']}")
            print(f"   Estimated Time: {estimate['estimated_time']['estimated_minutes']:.1f} minutes")
            print(f"   Recommended Chunks: {estimate['recommended_chunks']}")
            print("   Features:")
            for feature, enabled in estimate['features'].items():
                print(f"     ✓ {feature.replace('_', ' ').title()}: {enabled}")
        else:
            print(f"❌ Time estimation failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error testing time estimation: {e}")
    
    # Test 2: Start large document generation
    print("\n2️⃣ Testing Large Document Generation Request...")
    try:
        request_data = {
            "title": "AI in Healthcare: A Comprehensive 100-Page Analysis",
            "document_type": "research_paper",
            "writing_style": "analytical",
            "target_pages": 100,
            "citation_style": "APA",
            "output_formats": ["pdf", "latex", "txt"],
            "keywords": ["AI", "healthcare", "machine learning", "medical diagnosis"],
            "field": "healthcare_technology",
            "author": "ASCAES Demo",
            "include_math": True,
            "humanization_level": "extensive",
            "ai_detection_avoidance": True,
            "quality_threshold": 0.85
        }
        
        response = requests.post(
            f"{base_url}/api/agents/generate/large",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            session_id = result['session_id']
            print("✅ Large document generation started!")
            print(f"   Session ID: {session_id}")
            print(f"   Status: {result['status']}")
            print(f"   Message: {result['message']}")
            
            # Test 3: Monitor progress
            print("\n3️⃣ Monitoring Progress...")
            for i in range(10):  # Check progress 10 times
                time.sleep(2)  # Wait 2 seconds between checks
                
                try:
                    progress_response = requests.get(f"{base_url}/api/agents/generate/{session_id}")
                    if progress_response.status_code == 200:
                        progress = progress_response.json()
                        status = progress.get('status', 'unknown')
                        progress_pct = progress.get('progress', 0)
                        phase = progress.get('phase', 'unknown')
                        
                        print(f"   Progress: {progress_pct}% | Status: {status} | Phase: {phase}")
                        
                        if status in ['completed', 'failed']:
                            break
                    else:
                        print(f"   ❌ Progress check failed: {progress_response.status_code}")
                        break
                        
                except Exception as e:
                    print(f"   ❌ Error checking progress: {e}")
                    break
            
            print("\n📊 Final Status Check...")
            try:
                final_response = requests.get(f"{base_url}/api/agents/generate/{session_id}")
                if final_response.status_code == 200:
                    final_status = final_response.json()
                    print(f"   Final Status: {final_status.get('status', 'unknown')}")
                    
                    if final_status.get('status') == 'completed':
                        print("🎉 Large document generation completed!")
                        
                        # Try to get the result
                        try:
                            result_response = requests.get(f"{base_url}/api/agents/generate/{session_id}/result")
                            if result_response.status_code == 200:
                                document_result = result_response.json()
                                print("📄 Document Details:")
                                
                                if 'generation_metadata' in document_result:
                                    metadata = document_result['generation_metadata']
                                    print(f"   Target Pages: {metadata.get('target_pages', 'N/A')}")
                                    print(f"   Actual Pages: {metadata.get('actual_pages', 'N/A')}")
                                    print(f"   Word Count: {metadata.get('actual_words', 'N/A'):,}")
                                    print(f"   Quality Score: {metadata.get('quality_score', 'N/A')}")
                                    print(f"   AI Detection Score: {metadata.get('ai_detection_score', 'N/A')}")
                                    print(f"   Generation Time: {metadata.get('generation_time', 'N/A'):.1f} seconds")
                                
                            else:
                                print(f"   ❌ Could not retrieve result: {result_response.status_code}")
                        except Exception as e:
                            print(f"   ❌ Error retrieving result: {e}")
                    
                    elif final_status.get('status') == 'failed':
                        print("❌ Large document generation failed!")
                        error = final_status.get('error', 'Unknown error')
                        print(f"   Error: {error}")
                    
                    else:
                        print(f"⏳ Generation still in progress: {final_status.get('status', 'unknown')}")
                        
            except Exception as e:
                print(f"❌ Error checking final status: {e}")
                
        else:
            print(f"❌ Large document generation request failed: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error testing large document generation: {e}")
    
    # Test 4: System status
    print("\n4️⃣ Testing System Status...")
    try:
        response = requests.get(f"{base_url}/api/agents/status")
        if response.status_code == 200:
            status = response.json()
            print("✅ System status retrieved!")
            print(f"   Coordinator Status: {status.get('coordinator_status', 'unknown')}")
            print(f"   Active Sessions: {status.get('active_sessions', 0)}")
            
            # Show agent statuses
            agent_statuses = status.get('agent_statuses', {})
            healthy_agents = sum(1 for agent_status in agent_statuses.values() 
                               if not agent_status.get('error', False))
            total_agents = len(agent_statuses)
            print(f"   Healthy Agents: {healthy_agents}/{total_agents}")
            
        else:
            print(f"❌ System status failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing system status: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 API Testing Complete!")
    print()
    print("Key Features Demonstrated:")
    print("✓ Time estimation for large documents")
    print("✓ Chunked document generation")
    print("✓ Real-time progress monitoring")
    print("✓ AI detection avoidance")
    print("✓ Multiple humanization passes")
    print("✓ Quality assurance checks")
    print("✓ Multiple output formats")
    print()

def test_chunking_demo():
    """Demonstrate chunking strategy"""
    
    print("🧩 Chunking Strategy Demo")
    print("=" * 30)
    
    # Simulate chunking for different document sizes
    test_cases = [
        {"pages": 50, "description": "Medium Report"},
        {"pages": 100, "description": "Large Research Paper"},
        {"pages": 200, "description": "Thesis/Dissertation"},
        {"pages": 300, "description": "Comprehensive Study"}
    ]
    
    for case in test_cases:
        pages = case["pages"]
        description = case["description"]
        
        print(f"\n📄 {description} ({pages} pages):")
        
        # Calculate chunks (simplified version of the actual algorithm)
        max_chunk_pages = 10
        num_chunks = max(1, (pages + max_chunk_pages - 1) // max_chunk_pages)
        pages_per_chunk = pages // num_chunks
        
        print(f"   Total Chunks: {num_chunks}")
        print(f"   Pages per Chunk: ~{pages_per_chunk}")
        print(f"   Words per Chunk: ~{pages_per_chunk * 250:,}")
        
        # Show chunk breakdown
        current_page = 1
        for i in range(num_chunks):
            start_page = current_page
            end_page = min(pages, current_page + pages_per_chunk - 1)
            if i == num_chunks - 1:  # Last chunk gets remaining pages
                end_page = pages
            
            chunk_pages = end_page - start_page + 1
            print(f"     Chunk {i+1}: Pages {start_page}-{end_page} ({chunk_pages} pages)")
            current_page = end_page + 1

def main():
    """Main test function"""
    print("🧪 ASCAES Large Document API Test Suite")
    print("=" * 60)
    print()
    print("This test demonstrates the large document generation capabilities:")
    print("• Intelligent chunking for 100+ page documents")
    print("• AI detection avoidance through humanization")
    print("• Real-time progress tracking")
    print("• Multiple output formats (PDF, LaTeX, TXT)")
    print("• Quality assurance and academic standards")
    print()
    
    print("Test Options:")
    print("1. Full API test (time estimate + generation + monitoring)")
    print("2. Chunking strategy demonstration")
    print("3. Time estimation only")
    print("0. Exit")
    
    try:
        choice = input("\nSelect test option (0-3): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            return
        elif choice == "1":
            test_large_document_api()
        elif choice == "2":
            test_chunking_demo()
        elif choice == "3":
            base_url = "http://localhost:8000"
            pages = int(input("Enter target pages (10-500): "))
            
            try:
                response = requests.get(f"{base_url}/api/agents/estimate/large/{pages}")
                if response.status_code == 200:
                    estimate = response.json()
                    print(f"\n⏱️  Time Estimate for {pages} pages:")
                    print(f"   Total Time: {estimate['estimated_time']['estimated_minutes']:.1f} minutes")
                    print(f"   Per Page: {estimate['estimated_time']['estimated_total_seconds'] / pages:.1f} seconds")
                    print(f"   Recommended Chunks: {estimate['recommended_chunks']}")
                else:
                    print(f"❌ Request failed: {response.status_code}")
            except Exception as e:
                print(f"❌ Error: {e}")
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test error: {e}")

if __name__ == "__main__":
    main()

import React from 'react'
import { FileText, Clock, CheckCircle, AlertCircle } from 'lucide-react'

interface DocumentProgressProps {
  progress: number
  status: 'processing' | 'completed' | 'error'
  message?: string
  estimatedTime?: string
}

export const DocumentProgress: React.FC<DocumentProgressProps> = ({
  progress,
  status,
  message,
  estimatedTime
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-success-500" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-error-500" />
      default:
        return <FileText className="w-5 h-5 text-primary-500 animate-pulse" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-success-500'
      case 'error':
        return 'bg-error-500'
      default:
        return 'bg-primary-500'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center space-x-3 mb-3">
        {getStatusIcon()}
        <div className="flex-1">
          <h4 className="font-medium text-gray-900">
            Document Generation
          </h4>
          <p className="text-sm text-gray-600">
            {message || 'Processing your document...'}
          </p>
        </div>
        {estimatedTime && status === 'processing' && (
          <div className="flex items-center text-sm text-gray-500">
            <Clock className="w-4 h-4 mr-1" />
            {estimatedTime}
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-500 ${getStatusColor()}`}
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>

      {/* Progress Text */}
      <div className="flex justify-between text-sm text-gray-600">
        <span>{progress}% complete</span>
        {status === 'processing' && (
          <span className="animate-pulse">●●●</span>
        )}
      </div>
    </div>
  )
}

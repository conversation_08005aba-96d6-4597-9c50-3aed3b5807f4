#!/usr/bin/env python3
"""
ASCAES Test Runner
Simple test runner for development and verification
"""

import asyncio
import sys
import subprocess
from pathlib import Path

def check_prerequisites():
    """Check if prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 11):
        print(f"❌ Python 3.11+ required, found {python_version.major}.{python_version.minor}")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check if in correct directory
    if not Path("backend").exists():
        print("❌ Please run from the project root directory (where backend/ folder exists)")
        return False
    print("✅ Project structure found")
    
    # Check if virtual environment is recommended
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Virtual environment not detected (recommended but not required)")
    else:
        print("✅ Virtual environment detected")
    
    return True

def install_test_dependencies():
    """Install test dependencies"""
    print("\n📦 Installing test dependencies...")
    
    try:
        # Install pytest and other test dependencies
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "pytest", "pytest-asyncio", "httpx", "pytest-mock"
        ], check=True, capture_output=True)
        print("✅ Test dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install test dependencies: {e}")
        return False

def run_unit_tests():
    """Run unit tests with pytest"""
    print("\n🧪 Running unit tests...")
    
    try:
        # Run pytest on backend tests
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "backend/tests/", 
            "-v", 
            "--tb=short"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        if result.returncode == 0:
            print("✅ Unit tests passed")
            return True
        else:
            print("❌ Some unit tests failed")
            return False
            
    except FileNotFoundError:
        print("❌ pytest not found. Installing...")
        if install_test_dependencies():
            return run_unit_tests()
        return False
    except Exception as e:
        print(f"❌ Error running unit tests: {e}")
        return False

def run_integration_tests():
    """Run integration tests"""
    print("\n🔗 Running integration tests...")
    
    try:
        # Run the comprehensive test script
        result = subprocess.run([
            sys.executable, "test_setup.py", "--comprehensive"
        ], capture_output=False)  # Don't capture output so we see real-time progress
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running integration tests: {e}")
        return False

def run_quick_verification():
    """Run quick verification"""
    print("\n⚡ Running quick verification...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_setup.py", "--quick"
        ], capture_output=False)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running quick verification: {e}")
        return False

def check_ollama_status():
    """Check if Ollama is running"""
    print("\n🤖 Checking Ollama status...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama is running with {len(models)} models")
            for model in models[:3]:  # Show first 3 models
                name = model.get("name", "Unknown")
                size = model.get("size", 0)
                size_gb = size / (1024**3) if size else 0
                print(f"   - {name} ({size_gb:.1f}GB)")
            return True
        else:
            print("❌ Ollama is not responding correctly")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Ollama is not running")
        print("   Start with: ollama serve")
        return False
    except ImportError:
        print("⚠️  requests not installed, skipping Ollama check")
        return True
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

def start_development_server():
    """Start the development server for manual testing"""
    print("\n🚀 Starting development server...")
    print("   Frontend: http://localhost:3000")
    print("   Backend API: http://localhost:8000")
    print("   API Docs: http://localhost:8000/docs")
    print("\n   Press Ctrl+C to stop")
    
    try:
        # Start both frontend and backend
        subprocess.run(["npm", "run", "dev"], cwd=".", check=True)
    except KeyboardInterrupt:
        print("\n👋 Development server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting development server: {e}")
        print("   Try: npm install && npm run install-all")
    except FileNotFoundError:
        print("❌ npm not found. Please install Node.js")

def main():
    """Main test runner"""
    print("🧪 ASCAES Test Runner")
    print("=" * 40)
    
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        return
    
    print("\nSelect test type:")
    print("1. Quick verification (essential components)")
    print("2. Unit tests (pytest)")
    print("3. Integration tests (comprehensive)")
    print("4. All tests (quick + unit + integration)")
    print("5. Check Ollama status")
    print("6. Start development server")
    print("0. Exit")
    
    try:
        choice = input("\nEnter choice (1-6, 0 to exit): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            return
        elif choice == "1":
            success = run_quick_verification()
            if success:
                print("\n✅ Quick verification passed!")
            else:
                print("\n❌ Quick verification failed!")
        elif choice == "2":
            success = run_unit_tests()
            if success:
                print("\n✅ Unit tests passed!")
            else:
                print("\n❌ Some unit tests failed!")
        elif choice == "3":
            success = run_integration_tests()
            if success:
                print("\n✅ Integration tests passed!")
            else:
                print("\n❌ Some integration tests failed!")
        elif choice == "4":
            print("Running all tests...")
            quick_ok = run_quick_verification()
            unit_ok = run_unit_tests()
            integration_ok = run_integration_tests()
            
            total_passed = sum([quick_ok, unit_ok, integration_ok])
            print(f"\n📊 Test Summary: {total_passed}/3 test suites passed")
            
            if total_passed == 3:
                print("🎉 All tests passed! ASCAES is ready!")
            else:
                print("⚠️  Some tests failed. Check the output above.")
        elif choice == "5":
            check_ollama_status()
        elif choice == "6":
            start_development_server()
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Test runner interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()

import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  MessageSquare, 
  FileText, 
  Brain, 
  Settings, 
  Plus,
  ChevronLeft,
  ChevronRight,
  Trash2,
  Edit3
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { formatDistanceToNow } from 'date-fns'

export const Sidebar: React.FC = () => {
  const location = useLocation()
  const { 
    sidebarCollapsed, 
    setSidebarCollapsed, 
    conversations, 
    currentConversationId,
    setCurrentConversation,
    deleteConversation 
  } = useAppStore()

  const navigationItems = [
    { icon: MessageSquare, label: 'Chat', path: '/chat' },
    { icon: FileText, label: 'Documents', path: '/documents' },
    { icon: Brain, label: 'Models', path: '/models' },
    { icon: Settings, label: 'Settings', path: '/settings' },
  ]

  const handleNewChat = () => {
    setCurrentConversation(null)
  }

  const handleDeleteConversation = (e: React.MouseEvent, id: string) => {
    e.preventDefault()
    e.stopPropagation()
    deleteConversation(id)
  }

  return (
    <div className="h-full bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <span className="font-bold text-gray-900">ASCAES</span>
            </div>
          )}
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-1 rounded-md hover:bg-gray-100 transition-colors"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-gray-500" />
            )}
          </button>
        </div>
      </div>

      {/* New Chat Button */}
      <div className="p-4">
        <Link
          to="/chat"
          onClick={handleNewChat}
          className={`flex items-center space-x-3 w-full p-3 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors ${
            sidebarCollapsed ? 'justify-center' : ''
          }`}
        >
          <Plus className="w-5 h-5" />
          {!sidebarCollapsed && <span className="font-medium">New Chat</span>}
        </Link>
      </div>

      {/* Navigation */}
      <nav className="px-4 space-y-1">
        {navigationItems.map((item) => {
          const isActive = location.pathname.startsWith(item.path)
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`sidebar-item ${isActive ? 'active' : ''} ${
                sidebarCollapsed ? 'justify-center' : ''
              }`}
            >
              <item.icon className="w-5 h-5" />
              {!sidebarCollapsed && <span>{item.label}</span>}
            </Link>
          )
        })}
      </nav>

      {/* Conversations List */}
      {!sidebarCollapsed && location.pathname.startsWith('/chat') && (
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="px-4 py-2 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
              Recent Chats
            </h3>
          </div>
          <div className="flex-1 overflow-y-auto scrollbar-thin">
            <div className="p-2 space-y-1">
              {conversations.map((conversation) => (
                <Link
                  key={conversation.id}
                  to={`/chat/${conversation.id}`}
                  onClick={() => setCurrentConversation(conversation.id)}
                  className={`group flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                    currentConversationId === conversation.id ? 'bg-primary-50 border-l-2 border-primary-600' : ''
                  }`}
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {conversation.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatDistanceToNow(conversation.updatedAt, { addSuffix: true })}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        // TODO: Implement edit conversation title
                      }}
                      className="p-1 rounded hover:bg-gray-200 transition-colors"
                    >
                      <Edit3 className="w-3 h-3 text-gray-400" />
                    </button>
                    <button
                      onClick={(e) => handleDeleteConversation(e, conversation.id)}
                      className="p-1 rounded hover:bg-red-100 transition-colors"
                    >
                      <Trash2 className="w-3 h-3 text-red-400" />
                    </button>
                  </div>
                </Link>
              ))}
              
              {conversations.length === 0 && (
                <div className="text-center py-8">
                  <MessageSquare className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No conversations yet</p>
                  <p className="text-xs text-gray-400">Start a new chat to begin</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        {!sidebarCollapsed && (
          <div className="text-xs text-gray-500 text-center">
            <p>ASCAES v1.0.0</p>
            <p>Academic Document AI</p>
          </div>
        )}
      </div>
    </div>
  )
}

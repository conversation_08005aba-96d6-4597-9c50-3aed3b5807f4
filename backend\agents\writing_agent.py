"""
Writing Agent
Responsible for content generation across 8 academic writing styles
"""

from typing import Dict, Any, List
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class WritingAgent(BaseAgent):
    """Agent specialized in academic content generation with multiple writing styles"""
    
    def __init__(self):
        super().__init__(
            agent_id="writing_agent",
            name="Writing Agent",
            description="Generates academic content across 8 distinct writing styles with proper structure and flow"
        )
        
        # Writing-specific configuration
        self.config.update({
            "writing_styles": self._define_writing_styles(),
            "quality_standards": {
                "coherence_threshold": 0.8,
                "academic_tone_threshold": 0.9,
                "citation_density": "15-25 per 1000 words",
                "paragraph_length": "100-200 words"
            },
            "content_templates": self._load_content_templates()
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define writing agent capabilities"""
        return [
            AgentCapability(
                name="content_generation",
                description="Generate academic content in specified writing style",
                input_types=["content_plan", "writing_requirements", "research_data"],
                output_types=["written_content", "style_metadata"]
            ),
            AgentCapability(
                name="section_writing",
                description="Write specific document sections with proper structure",
                input_types=["section_plan", "style_guide", "context"],
                output_types=["section_content", "integration_notes"]
            ),
            AgentCapability(
                name="style_adaptation",
                description="Adapt content to different academic writing styles",
                input_types=["existing_content", "target_style"],
                output_types=["adapted_content", "style_analysis"]
            ),
            AgentCapability(
                name="content_refinement",
                description="Refine and improve existing content",
                input_types=["draft_content", "feedback", "quality_criteria"],
                output_types=["refined_content", "improvement_notes"]
            )
        ]
    
    def _define_writing_styles(self) -> Dict[str, Dict[str, Any]]:
        """Define the 8 academic writing styles"""
        return {
            "analytical": {
                "description": "Critical examination and data interpretation with evidence-based conclusions",
                "characteristics": [
                    "Systematic analysis of data or concepts",
                    "Evidence-based reasoning",
                    "Critical evaluation of sources",
                    "Logical argument structure",
                    "Objective tone with reasoned conclusions"
                ],
                "structure": "Problem → Analysis → Evidence → Conclusion",
                "tone": "objective, critical, systematic",
                "typical_sections": ["Introduction", "Analysis", "Discussion", "Conclusion"],
                "citation_style": "Heavy use of evidence and citations",
                "sentence_patterns": "Complex sentences with subordinate clauses for nuanced analysis"
            },
            "instructional": {
                "description": "Clear step-by-step guidance and teaching methodology",
                "characteristics": [
                    "Sequential presentation of information",
                    "Clear procedural steps",
                    "Examples and illustrations",
                    "Learning objectives",
                    "Assessment criteria"
                ],
                "structure": "Objective → Method → Steps → Examples → Assessment",
                "tone": "clear, directive, supportive",
                "typical_sections": ["Objectives", "Methodology", "Procedures", "Examples", "Evaluation"],
                "citation_style": "Pedagogical sources and best practices",
                "sentence_patterns": "Imperative and declarative sentences with clear transitions"
            },
            "reporting": {
                "description": "Factual documentation and objective findings presentation",
                "characteristics": [
                    "Objective presentation of facts",
                    "Systematic data organization",
                    "Minimal interpretation",
                    "Comprehensive coverage",
                    "Neutral tone"
                ],
                "structure": "Background → Methods → Findings → Summary",
                "tone": "neutral, factual, comprehensive",
                "typical_sections": ["Executive Summary", "Background", "Findings", "Recommendations"],
                "citation_style": "Factual sources and official documents",
                "sentence_patterns": "Declarative sentences with passive voice for objectivity"
            },
            "argumentative": {
                "description": "Persuasive reasoning with strong position and counterarguments",
                "characteristics": [
                    "Clear thesis statement",
                    "Persuasive evidence",
                    "Counterargument consideration",
                    "Logical reasoning",
                    "Strong conclusions"
                ],
                "structure": "Thesis → Arguments → Counterarguments → Refutation → Conclusion",
                "tone": "persuasive, confident, logical",
                "typical_sections": ["Introduction", "Arguments", "Counterarguments", "Conclusion"],
                "citation_style": "Authoritative sources supporting arguments",
                "sentence_patterns": "Assertive statements with supporting evidence"
            },
            "exploratory": {
                "description": "Investigation and hypothesis development with open-ended inquiry",
                "characteristics": [
                    "Open-ended investigation",
                    "Hypothesis formation",
                    "Multiple perspectives",
                    "Tentative conclusions",
                    "Future research directions"
                ],
                "structure": "Question → Exploration → Hypotheses → Implications → Future Work",
                "tone": "inquisitive, tentative, comprehensive",
                "typical_sections": ["Research Questions", "Literature Exploration", "Preliminary Findings", "Future Directions"],
                "citation_style": "Diverse sources representing multiple viewpoints",
                "sentence_patterns": "Questioning and conditional statements"
            },
            "descriptive": {
                "description": "Technical specifications and comprehensive detailed explanations",
                "characteristics": [
                    "Detailed technical information",
                    "Systematic organization",
                    "Comprehensive coverage",
                    "Precise terminology",
                    "Clear categorization"
                ],
                "structure": "Overview → Components → Details → Specifications → Summary",
                "tone": "precise, technical, comprehensive",
                "typical_sections": ["Overview", "Technical Details", "Specifications", "Implementation"],
                "citation_style": "Technical standards and authoritative references",
                "sentence_patterns": "Detailed descriptions with technical terminology"
            },
            "narrative": {
                "description": "Story-driven explanations and case study methodology",
                "characteristics": [
                    "Chronological structure",
                    "Case study approach",
                    "Personal or organizational stories",
                    "Contextual background",
                    "Lessons learned"
                ],
                "structure": "Context → Events → Analysis → Lessons → Implications",
                "tone": "engaging, contextual, reflective",
                "typical_sections": ["Background", "Case Description", "Analysis", "Lessons Learned"],
                "citation_style": "Case studies and experiential sources",
                "sentence_patterns": "Narrative flow with temporal connectors"
            },
            "schematic": {
                "description": "Systematic documentation and reference material structure",
                "characteristics": [
                    "Hierarchical organization",
                    "Reference format",
                    "Systematic categorization",
                    "Cross-references",
                    "Comprehensive indexing"
                ],
                "structure": "Categories → Subcategories → Details → Cross-references → Index",
                "tone": "systematic, reference-oriented, organized",
                "typical_sections": ["Classification", "Categories", "Detailed Descriptions", "References"],
                "citation_style": "Authoritative references and standards",
                "sentence_patterns": "Structured lists and categorical statements"
            }
        }
    
    def _load_content_templates(self) -> Dict[str, str]:
        """Load content templates for different sections"""
        return {
            "introduction": """
The introduction should:
1. Establish context and background
2. Present the research problem or question
3. State the thesis or main argument
4. Outline the document structure
5. Engage the reader's interest
""",
            "literature_review": """
The literature review should:
1. Survey relevant existing research
2. Identify gaps in current knowledge
3. Establish theoretical framework
4. Position the current work
5. Synthesize key findings
""",
            "methodology": """
The methodology should:
1. Describe research approach
2. Explain data collection methods
3. Detail analysis procedures
4. Address limitations
5. Justify methodological choices
""",
            "conclusion": """
The conclusion should:
1. Summarize key findings
2. Restate the thesis
3. Discuss implications
4. Suggest future research
5. Provide closure
"""
        }
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute writing task"""
        task_type = task.get("type", "content_generation")
        
        if task_type == "content_generation":
            return await self._generate_content(task)
        elif task_type == "section_writing":
            return await self._write_section(task)
        elif task_type == "style_adaptation":
            return await self._adapt_style(task)
        elif task_type == "content_refinement":
            return await self._refine_content(task)
        else:
            raise ValueError(f"Unknown writing task type: {task_type}")
    
    async def _generate_content(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate content based on plan and requirements"""
        plan = task.get("plan", {})
        writing_style = task.get("writing_style", "analytical")
        research_data = task.get("research_data", {})
        requirements = task.get("requirements", {})
        
        # Get style configuration
        style_config = self.config["writing_styles"].get(writing_style, 
                      self.config["writing_styles"]["analytical"])
        
        # Build comprehensive writing prompt
        writing_prompt = self._build_writing_prompt(plan, style_config, research_data, requirements)
        
        # Generate content using LLM
        content = await self._call_llm(writing_prompt, self._get_style_system_prompt(style_config))
        
        # Analyze generated content
        content_analysis = await self._analyze_generated_content(content, writing_style)
        
        return {
            "success": True,
            "content": content,
            "writing_style": writing_style,
            "word_count": len(content.split()),
            "analysis": content_analysis,
            "generation_timestamp": datetime.now().isoformat(),
            "style_adherence": content_analysis.get("style_score", 0.8)
        }
    
    async def _write_section(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Write a specific document section"""
        section_plan = task.get("section_plan", {})
        section_name = section_plan.get("section_name", "Unknown Section")
        writing_style = task.get("writing_style", "analytical")
        context = task.get("context", {})
        
        # Get section template
        section_template = self.config["content_templates"].get(
            section_name.lower().replace(" ", "_"), 
            "Write a comprehensive section that addresses the specified requirements."
        )
        
        # Build section-specific prompt
        section_prompt = f"""Write the {section_name} section for an academic document.

Writing Style: {writing_style}
Word Count Target: {section_plan.get('word_count', '500-800 words')}

Section Requirements:
{section_template}

Content Guidelines:
{section_plan.get('plan', 'No specific guidelines provided')}

Context from other sections:
{context.get('previous_sections', 'No previous context')}

Research findings to incorporate:
{context.get('research_findings', 'No specific research findings')}

Write a well-structured, academically rigorous section that follows the {writing_style} writing style."""

        # Generate section content
        section_content = await self._call_llm(
            section_prompt, 
            self._get_style_system_prompt(self.config["writing_styles"][writing_style])
        )
        
        return {
            "success": True,
            "section_name": section_name,
            "content": section_content,
            "word_count": len(section_content.split()),
            "writing_style": writing_style,
            "integration_notes": self._generate_integration_notes(section_name, section_content)
        }
    
    async def _adapt_style(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt existing content to a different writing style"""
        existing_content = task.get("content", "")
        target_style = task.get("target_style", "analytical")
        
        # Get target style configuration
        target_config = self.config["writing_styles"][target_style]
        
        adaptation_prompt = f"""Adapt the following content to the {target_style} writing style:

Target Style Characteristics:
- {target_config['description']}
- Tone: {target_config['tone']}
- Structure: {target_config['structure']}

Original Content:
{existing_content}

Rewrite the content to match the {target_style} style while preserving the core information and academic rigor. 
Ensure the adapted content follows the characteristic patterns and tone of {target_style} writing."""

        adapted_content = await self._call_llm(
            adaptation_prompt,
            self._get_style_system_prompt(target_config)
        )
        
        return {
            "success": True,
            "original_content": existing_content,
            "adapted_content": adapted_content,
            "target_style": target_style,
            "adaptation_notes": f"Content adapted from original to {target_style} style"
        }
    
    async def _refine_content(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Refine and improve existing content"""
        draft_content = task.get("content", "")
        feedback = task.get("feedback", "")
        quality_criteria = task.get("quality_criteria", {})
        
        refinement_prompt = f"""Refine and improve the following academic content:

Draft Content:
{draft_content}

Feedback to address:
{feedback}

Quality Criteria:
- Academic rigor: {quality_criteria.get('academic_rigor', 'high')}
- Clarity: {quality_criteria.get('clarity', 'high')}
- Coherence: {quality_criteria.get('coherence', 'high')}
- Citation integration: {quality_criteria.get('citations', 'proper')}

Improve the content by:
1. Enhancing clarity and flow
2. Strengthening arguments
3. Improving academic tone
4. Better integrating citations
5. Addressing any feedback points

Provide refined, publication-ready content."""

        refined_content = await self._call_llm(refinement_prompt)
        
        return {
            "success": True,
            "original_content": draft_content,
            "refined_content": refined_content,
            "improvements_made": self._identify_improvements(draft_content, refined_content),
            "quality_score": self._assess_content_quality(refined_content)
        }
    
    def _build_writing_prompt(self, plan: Dict[str, Any], style_config: Dict[str, Any], 
                             research_data: Dict[str, Any], requirements: Dict[str, Any]) -> str:
        """Build comprehensive writing prompt"""
        
        prompt = f"""Generate academic content with the following specifications:

WRITING STYLE: {style_config.get('description', 'Academic writing')}
TONE: {style_config.get('tone', 'academic')}
STRUCTURE: {style_config.get('structure', 'standard academic')}

DOCUMENT PLAN:
{plan.get('structure', 'No specific structure provided')}

RESEARCH FINDINGS:
{research_data.get('synthesis', 'No research synthesis provided')}

REQUIREMENTS:
- Target length: {requirements.get('target_length', '2000-3000 words')}
- Citation style: {requirements.get('citation_style', 'APA')}
- Academic level: {requirements.get('academic_level', 'graduate')}
- Field: {requirements.get('field', 'general academic')}

STYLE CHARACTERISTICS TO FOLLOW:
{chr(10).join(f"- {char}" for char in style_config.get('characteristics', []))}

Generate comprehensive, well-structured academic content that strictly adheres to the specified writing style and incorporates the research findings appropriately."""

        return prompt
    
    def _get_style_system_prompt(self, style_config: Dict[str, Any]) -> str:
        """Get system prompt for specific writing style"""
        return f"""You are an expert academic writer specializing in {style_config.get('description', 'academic writing')}. 

Your writing must exhibit these characteristics:
{chr(10).join(f"- {char}" for char in style_config.get('characteristics', []))}

Maintain a {style_config.get('tone', 'academic')} tone throughout and follow the {style_config.get('structure', 'standard')} structure pattern.

Use {style_config.get('sentence_patterns', 'clear academic sentences')} and ensure proper {style_config.get('citation_style', 'citation integration')}.

Write at a graduate-level academic standard with proper scholarly rigor."""
    
    async def _analyze_generated_content(self, content: str, writing_style: str) -> Dict[str, Any]:
        """Analyze generated content for quality and style adherence"""
        
        analysis_prompt = f"""Analyze the following academic content for adherence to {writing_style} writing style:

Content:
{content[:1500]}...

Evaluate:
1. Style adherence (0.0-1.0)
2. Academic rigor (0.0-1.0)
3. Clarity and coherence (0.0-1.0)
4. Structure quality (0.0-1.0)
5. Citation integration (0.0-1.0)

Provide scores and brief explanations for each criterion."""

        analysis_result = await self._call_llm(analysis_prompt)
        
        # Extract scores (simplified parsing)
        return {
            "style_score": 0.85,  # Placeholder - would parse from LLM response
            "academic_rigor": 0.9,
            "clarity": 0.8,
            "structure": 0.85,
            "citation_quality": 0.8,
            "overall_quality": 0.84,
            "analysis_text": analysis_result
        }
    
    def _generate_integration_notes(self, section_name: str, content: str) -> List[str]:
        """Generate notes for integrating section with rest of document"""
        return [
            f"Section {section_name} introduces key concepts that should be referenced in subsequent sections",
            "Ensure consistent terminology throughout the document",
            "Consider adding cross-references to related sections",
            "Verify citation consistency with document style"
        ]
    
    def _identify_improvements(self, original: str, refined: str) -> List[str]:
        """Identify improvements made during refinement"""
        # Simplified improvement identification
        improvements = []
        
        if len(refined.split()) > len(original.split()):
            improvements.append("Expanded content for better coverage")
        
        if refined.count('.') > original.count('.'):
            improvements.append("Improved sentence structure and flow")
        
        improvements.extend([
            "Enhanced academic tone",
            "Improved clarity and coherence",
            "Better integration of evidence"
        ])
        
        return improvements
    
    def _assess_content_quality(self, content: str) -> float:
        """Assess overall content quality"""
        # Simplified quality assessment
        word_count = len(content.split())
        sentence_count = content.count('.')
        
        # Basic metrics
        avg_sentence_length = word_count / max(sentence_count, 1)
        
        # Quality score based on various factors
        quality_score = 0.8  # Base score
        
        if 15 <= avg_sentence_length <= 25:  # Good academic sentence length
            quality_score += 0.1
        
        if word_count >= 500:  # Sufficient length
            quality_score += 0.05
        
        return min(quality_score, 1.0)

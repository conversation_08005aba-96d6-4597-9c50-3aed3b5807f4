# ASCAES Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Prerequisites
- Node.js 18+
- Python 3.11+
- Ollama 0.9+
- Git

### Installation

#### Option 1: Automated Setup (Recommended)
```bash
# Clone repository
git clone https://github.com/yourusername/ascaes.git
cd ascaes

# Run automated setup
npm run setup
```

#### Option 2: Manual Setup
```bash
# Clone repository
git clone https://github.com/yourusername/ascaes.git
cd ascaes

# Install dependencies
npm run install-all

# Set up environment
cp backend/.env.example backend/.env

# Download AI models
npm run setup-models

# Start application
npm run dev
```

### First Use

1. **Open Application**: http://localhost:3000
2. **Start New Chat**: Click "New Chat" in sidebar
3. **Generate Document**: Try this prompt:
   ```
   Generate a 1000-word analytical research paper about the impact of AI on academic writing. Use analytical writing style and output as PDF.
   ```

### Key Features

#### 🎯 8 Writing Styles
- **Analytical**: Critical examination and data interpretation
- **Instructional**: Teaching and step-by-step guidance
- **Reporting**: Factual documentation and findings
- **Argumentative**: Position papers and debates
- **Exploratory**: Investigation and hypothesis development
- **Descriptive**: Technical specifications and details
- **Narrative**: Case studies and story-driven explanations
- **Schematic**: Reference materials and systematic documentation

#### 📄 Multiple Output Formats
- **PDF**: Professional formatting for submission
- **LaTeX**: Source code for academic publishing
- **RTF**: Compatible with word processors
- **TXT**: Plain text for universal compatibility

#### 🤖 Multi-Agent System
- **Planning Agent**: Document structure
- **Research Agent**: Information gathering
- **Writing Agent**: Content generation
- **LaTeX Agent**: Mathematical formatting
- **Visual Agent**: Figures and tables
- **Quality Agent**: Grammar and consistency
- **Humanizer Agent**: Natural language improvement
- **Assembly Agent**: Final compilation

### Example Workflows

#### Research Paper Generation
```
1. "Create an outline for a computer science research paper on machine learning ethics"
2. "Generate the introduction section using analytical writing style"
3. "Add a literature review section with 10-15 citations"
4. "Create methodology and results sections"
5. "Compile everything into a PDF with proper formatting"
```

#### Technical Documentation
```
1. "Write technical documentation for a REST API using instructional style"
2. "Include code examples and step-by-step implementation guide"
3. "Add troubleshooting section and FAQ"
4. "Export as both PDF and RTF formats"
```

#### Literature Review
```
1. Upload relevant research papers (PDF format)
2. "Analyze the uploaded papers and identify key themes"
3. "Generate a comprehensive literature review using analytical style"
4. "Include proper citations and reference formatting"
```

### Tips for Best Results

#### Effective Prompting
- Be specific about document type and requirements
- Specify target audience and technical level
- Include word count and section requirements
- Mention citation style preferences

#### Document Quality
- Upload reference materials for better context
- Use iterative refinement through conversation
- Review and provide feedback on generated sections
- Verify technical accuracy and citations

#### Performance Optimization
- Close unnecessary applications (8GB RAM systems)
- Use appropriate model for task complexity
- Monitor system resources during generation

### Troubleshooting

#### Common Issues

**Connection Failed**
```bash
# Check if Ollama is running
ollama serve

# Verify models are installed
ollama list
```

**Slow Performance**
- Check available RAM (8GB minimum)
- Close other applications
- Use smaller models if needed

**Generation Errors**
- Check model status in Models page
- Verify internet connection for initial setup
- Review logs in `backend/logs/`

#### Getting Help
- **Health Check**: http://localhost:8000/api/health
- **API Documentation**: http://localhost:8000/api/docs
- **User Guide**: [docs/user-guide.md](docs/user-guide.md)
- **Installation Guide**: [docs/installation.md](docs/installation.md)

### Next Steps

1. **Explore Writing Styles**: Try different styles for various document types
2. **Upload Documents**: Add reference materials for analysis
3. **Customize Settings**: Adjust model parameters and preferences
4. **Create Templates**: Set up reusable document templates
5. **Advanced Features**: Explore multi-agent coordination and LaTeX generation

### System Architecture

```
ASCAES/
├── frontend/          # React + TypeScript UI
├── backend/           # FastAPI + Python API
├── agents/            # Multi-agent system
├── models/            # Local AI models (Ollama)
├── storage/           # Documents and vectors
└── docs/              # Documentation
```

### Production Deployment

For production use:
```bash
# Build application
npm run build

# Start production servers
npm run start

# Monitor health
npm run health
```

### Support

- **Documentation**: Complete guides in `docs/` folder
- **Examples**: Sample prompts and workflows
- **Community**: User forums and discussions
- **Issues**: Report bugs and feature requests

---

**Ready to generate your first academic document? Start with `npm run setup` and begin creating!** 🎓

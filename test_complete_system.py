#!/usr/bin/env python3
"""
Complete System Test for ASCAES Large Document Generation
Tests the full pipeline from natural language input to document generation
"""

import requests
import json
import time
from datetime import datetime

def test_complete_system():
    """Test the complete ASCAES system"""
    
    print("🧪 ASCAES Complete System Test")
    print("=" * 60)
    print()
    
    base_url = "http://localhost:8000"
    
    # Test cases for different document types and sizes
    test_cases = [
        {
            "name": "Large Research Paper (100 pages)",
            "message": "Generate a 100-page research paper about artificial intelligence in healthcare using APA citations",
            "expected_type": "large_document_request",
            "expected_pages": 100
        },
        {
            "name": "Medium Report (50 pages)", 
            "message": "Create a 50-page analytical report on climate change impacts",
            "expected_type": "large_document_request",
            "expected_pages": 50
        },
        {
            "name": "Thesis (200 pages)",
            "message": "Write a 200-page thesis about machine learning applications in medical diagnosis",
            "expected_type": "large_document_request", 
            "expected_pages": 200
        },
        {
            "name": "Small Document (10 pages)",
            "message": "Generate a 10-page essay about renewable energy",
            "expected_type": "document_request",
            "expected_pages": 10
        }
    ]
    
    print("🎯 Test Cases:")
    for i, case in enumerate(test_cases, 1):
        print(f"   {i}. {case['name']}")
    print()
    
    # Let user choose test case
    try:
        choice = int(input("Select test case (1-4): ")) - 1
        if choice < 0 or choice >= len(test_cases):
            print("❌ Invalid choice")
            return
    except ValueError:
        print("❌ Invalid input")
        return
    
    selected_case = test_cases[choice]
    print(f"\n🚀 Running Test: {selected_case['name']}")
    print(f"📝 Message: {selected_case['message']}")
    print()
    
    # Step 1: Test natural language processing
    print("1️⃣ Testing Natural Language Processing...")
    try:
        response = requests.post(f"{base_url}/api/chat/generate", json={
            "message": selected_case["message"]
        })
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Natural language processing successful!")
            print(f"   Response Type: {result.get('type', 'unknown')}")
            print(f"   Session ID: {result.get('session_id', 'N/A')}")
            
            # Verify expected type
            if result.get('type') == selected_case['expected_type']:
                print("✅ Correct document type detected")
            else:
                print(f"⚠️  Expected {selected_case['expected_type']}, got {result.get('type')}")
            
            # Show response
            print(f"   Response: {result.get('response', 'No response')[:200]}...")
            
            session_id = result.get('session_id')
            if not session_id:
                print("❌ No session ID returned")
                return
                
        else:
            print(f"❌ Natural language processing failed: {response.status_code}")
            print(response.text)
            return
            
    except Exception as e:
        print(f"❌ Error in natural language processing: {e}")
        return
    
    # Step 2: Monitor generation progress
    print(f"\n2️⃣ Monitoring Document Generation Progress...")
    print(f"   Session ID: {session_id}")
    
    max_checks = 60  # Check for up to 60 times (30 minutes)
    check_interval = 30  # Check every 30 seconds
    
    for check_num in range(1, max_checks + 1):
        try:
            print(f"\n   📊 Progress Check {check_num}/{max_checks}")
            
            progress_response = requests.get(f"{base_url}/api/chat/generation/{session_id}")
            
            if progress_response.status_code == 200:
                progress = progress_response.json()
                
                status = progress.get('status', 'unknown')
                progress_pct = progress.get('progress', 0)
                phase = progress.get('phase', 'unknown')
                message = progress.get('message', '')
                target_pages = progress.get('target_pages', 0)
                
                print(f"   Status: {status}")
                print(f"   Progress: {progress_pct}%")
                print(f"   Phase: {phase}")
                if message:
                    print(f"   Message: {message}")
                if target_pages:
                    print(f"   Target Pages: {target_pages}")
                
                if status == 'completed':
                    print("\n🎉 Document Generation Completed!")
                    
                    # Try to get detailed results
                    try:
                        result_response = requests.get(f"{base_url}/api/agents/generate/{session_id}/result")
                        if result_response.status_code == 200:
                            document_result = result_response.json()
                            
                            print("\n📊 Final Results:")
                            if 'generation_metadata' in document_result:
                                metadata = document_result['generation_metadata']
                                print(f"   Target Pages: {metadata.get('target_pages', 'N/A')}")
                                print(f"   Actual Pages: {metadata.get('actual_pages', 'N/A')}")
                                print(f"   Word Count: {metadata.get('actual_words', 'N/A'):,}")
                                print(f"   Quality Score: {metadata.get('quality_score', 'N/A')}")
                                print(f"   AI Detection Score: {metadata.get('ai_detection_score', 'N/A')}")
                                print(f"   Generation Time: {metadata.get('generation_time', 'N/A'):.1f} seconds")
                                print(f"   Chunks Used: {metadata.get('chunks_generated', 'N/A')}")
                            
                            if 'formats_available' in document_result:
                                formats = document_result['formats_available']
                                print(f"   Available Formats: {', '.join(formats)}")
                        
                    except Exception as e:
                        print(f"   ⚠️  Could not retrieve detailed results: {e}")
                    
                    break
                    
                elif status == 'failed':
                    print(f"\n❌ Document Generation Failed!")
                    error = progress.get('error', 'Unknown error')
                    print(f"   Error: {error}")
                    break
                    
                elif status == 'running':
                    print(f"   ⏳ Generation in progress...")
                    
                else:
                    print(f"   ❓ Unknown status: {status}")
                
            else:
                print(f"   ❌ Progress check failed: {progress_response.status_code}")
                if progress_response.status_code == 404:
                    print("   Session not found - generation may have completed or failed")
                    break
            
            # Wait before next check (unless it's the last check or generation is complete)
            if check_num < max_checks and status not in ['completed', 'failed']:
                print(f"   ⏱️  Waiting {check_interval} seconds before next check...")
                time.sleep(check_interval)
                
        except Exception as e:
            print(f"   ❌ Error checking progress: {e}")
            break
    
    # Step 3: Test system status
    print(f"\n3️⃣ Testing System Status...")
    try:
        status_response = requests.get(f"{base_url}/api/agents/status")
        if status_response.status_code == 200:
            status = status_response.json()
            print("✅ System status retrieved!")
            print(f"   Coordinator Status: {status.get('coordinator_status', 'unknown')}")
            print(f"   Active Sessions: {status.get('active_sessions', 0)}")
            
            agent_statuses = status.get('agent_statuses', {})
            healthy_agents = sum(1 for agent_status in agent_statuses.values() 
                               if not agent_status.get('error', False))
            total_agents = len(agent_statuses)
            print(f"   Healthy Agents: {healthy_agents}/{total_agents}")
            
        else:
            print(f"❌ System status failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
    
    print(f"\n" + "=" * 60)
    print("🏁 Complete System Test Finished!")
    print()
    print("✨ Key Features Demonstrated:")
    print("• Natural language document request processing")
    print("• Intelligent document type detection")
    print("• Large document chunking and generation")
    print("• Real-time progress monitoring")
    print("• AI detection avoidance")
    print("• Multiple output formats")
    print("• Quality assurance checks")
    print()
    
    # Show usage examples
    print("💬 Try these examples in the frontend:")
    print("• 'Generate a 100-page research paper about AI in healthcare'")
    print("• 'Create a 50-page report on climate change using APA citations'") 
    print("• 'Write a 200-page thesis about machine learning in medical diagnosis'")
    print("• 'Generate a 75-page analytical study on renewable energy technologies'")
    print()

def test_time_estimates():
    """Test time estimation for different document sizes"""
    
    print("⏱️  Time Estimation Test")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    test_sizes = [50, 100, 150, 200, 300]
    
    for pages in test_sizes:
        try:
            response = requests.get(f"{base_url}/api/agents/estimate/large/{pages}")
            if response.status_code == 200:
                estimate = response.json()
                print(f"\n📄 {pages} pages:")
                print(f"   Estimated Time: {estimate['estimated_time']['estimated_minutes']:.1f} minutes")
                print(f"   Recommended Chunks: {estimate['recommended_chunks']}")
            else:
                print(f"❌ Failed to get estimate for {pages} pages")
        except Exception as e:
            print(f"❌ Error getting estimate for {pages} pages: {e}")

def main():
    """Main test function"""
    print("🎓 ASCAES Complete System Test Suite")
    print("=" * 60)
    print()
    print("This test demonstrates the complete ASCAES workflow:")
    print("1. Natural language processing")
    print("2. Document generation with progress monitoring")
    print("3. System status verification")
    print()
    
    print("Test Options:")
    print("1. Complete system test (recommended)")
    print("2. Time estimation test")
    print("0. Exit")
    
    try:
        choice = input("\nSelect test option (0-2): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            return
        elif choice == "1":
            test_complete_system()
        elif choice == "2":
            test_time_estimates()
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test error: {e}")

if __name__ == "__main__":
    main()

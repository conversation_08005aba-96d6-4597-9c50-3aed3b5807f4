"""
Database migration to add folder system for document organization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from core.database import engine

def upgrade():
    """Add folder system tables and columns"""
    
    with engine.connect() as conn:
        # Create document_folders table
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS document_folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                folder_type VARCHAR(50) NOT NULL,
                parent_folder_id INTEGER,
                conversation_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_folder_id) REFERENCES document_folders (id)
            )
        """))
        
        # Add new columns to generated_documents table
        try:
            conn.execute(text("ALTER TABLE generated_documents ADD COLUMN folder_id INTEGER"))
        except:
            pass  # Column might already exist
            
        try:
            conn.execute(text("ALTER TABLE generated_documents ADD COLUMN is_page BOOLEAN DEFAULT 0"))
        except:
            pass
            
        try:
            conn.execute(text("ALTER TABLE generated_documents ADD COLUMN page_number INTEGER"))
        except:
            pass
            
        try:
            conn.execute(text("ALTER TABLE generated_documents ADD COLUMN parent_document_id INTEGER"))
        except:
            pass
        
        # Add foreign key constraints (SQLite doesn't support adding FK constraints to existing tables)
        # These will be enforced at the application level
        
        conn.commit()
        print("Database migration completed successfully")

def downgrade():
    """Remove folder system (not recommended for production)"""
    
    with engine.connect() as conn:
        # Remove columns from generated_documents (SQLite doesn't support DROP COLUMN)
        # This would require recreating the table
        
        # Drop document_folders table
        conn.execute(text("DROP TABLE IF EXISTS document_folders"))
        
        conn.commit()
        print("Database migration rolled back")

if __name__ == "__main__":
    upgrade()

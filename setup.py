#!/usr/bin/env python3
"""
ASCAES Setup Script
Automated setup for Academic Scholarly Content & Analysis Expert System
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Print ASCAES banner"""
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                            ASCAES                             ║
    ║        Academic Scholarly Content & Analysis Expert System    ║
    ║                                                               ║
    ║                    Production Setup v1.0.0                   ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 11):
        print(f"❌ Python 3.11+ required, found {python_version.major}.{python_version.minor}")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print(f"✅ Node.js {node_version}")
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False
    
    # Check Ollama
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            ollama_version = result.stdout.strip()
            print(f"✅ Ollama {ollama_version}")
        else:
            print("❌ Ollama not found")
            return False
    except FileNotFoundError:
        print("❌ Ollama not found")
        print("   Please install Ollama from https://ollama.ai/download")
        return False
    
    # Check Git
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            git_version = result.stdout.strip()
            print(f"✅ Git {git_version}")
        else:
            print("❌ Git not found")
            return False
    except FileNotFoundError:
        print("❌ Git not found")
        return False
    
    return True

def setup_environment():
    """Set up environment configuration"""
    print("\n📝 Setting up environment...")
    
    env_file = Path("backend/.env")
    env_example = Path("backend/.env.example")
    
    if not env_file.exists() and env_example.exists():
        # Copy example environment file
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Environment configuration created")
    else:
        print("✅ Environment configuration already exists")

def install_dependencies():
    """Install all dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        # Install root dependencies
        print("   Installing root dependencies...")
        subprocess.run(['npm', 'install'], check=True, cwd='.')
        
        # Install frontend dependencies
        print("   Installing frontend dependencies...")
        subprocess.run(['npm', 'install'], check=True, cwd='frontend')
        
        # Install backend dependencies
        print("   Installing backend dependencies...")
        subprocess.run(['pip', 'install', '-r', 'requirements.txt'], check=True, cwd='backend')
        
        print("✅ All dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def setup_models():
    """Set up AI models"""
    print("\n🤖 Setting up AI models...")
    
    try:
        # Run model setup script
        subprocess.run([sys.executable, 'backend/scripts/setup_models.py'], check=True)
        print("✅ AI models configured successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error setting up models: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "storage",
        "storage/documents", 
        "storage/vectors",
        "storage/temp",
        "models",
        "templates",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directory structure created")

def verify_installation():
    """Verify the installation"""
    print("\n🔍 Verifying installation...")
    
    # Check if all required files exist
    required_files = [
        "package.json",
        "frontend/package.json", 
        "backend/requirements.txt",
        "backend/main.py",
        "backend/.env"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Missing required file: {file_path}")
            return False
    
    print("✅ All required files present")
    
    # Test import of main modules
    try:
        sys.path.append('backend')
        import main
        print("✅ Backend modules can be imported")
    except ImportError as e:
        print(f"❌ Backend import error: {e}")
        return False
    
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 ASCAES Setup Complete!")
    print("="*60)
    print("\n📋 Next Steps:")
    print("   1. Start the application:")
    print("      npm run dev")
    print("\n   2. Open your browser:")
    print("      Frontend: http://localhost:3000")
    print("      API Docs: http://localhost:8000/api/docs")
    print("\n   3. Read the documentation:")
    print("      User Guide: docs/user-guide.md")
    print("      Installation: docs/installation.md")
    print("\n   4. Start generating academic documents!")
    print("\n💡 Tips:")
    print("   - Upload reference documents for better context")
    print("   - Try different writing styles for various document types")
    print("   - Use the chat interface to refine and iterate")
    print("\n🆘 Need Help?")
    print("   - Check logs in backend/logs/")
    print("   - Visit the documentation in docs/")
    print("   - Review system health at http://localhost:8000/api/health")

def main():
    """Main setup function"""
    print_banner()
    
    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please install missing software and try again.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Set up environment
    setup_environment()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies. Please check the error messages above.")
        sys.exit(1)
    
    # Set up models
    if not setup_models():
        print("\n⚠️  Model setup failed, but you can continue and set up models later.")
        print("   Run: npm run setup-models")
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed. Please check the error messages above.")
        sys.exit(1)
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)

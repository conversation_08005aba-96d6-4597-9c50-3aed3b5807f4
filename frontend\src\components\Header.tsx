import React from 'react'
import { 
  <PERSON>, 
  <PERSON>, 
  Wifi, 
  WifiOff, 
  <PERSON><PERSON><PERSON>,
  <PERSON>r,
  <PERSON>,
  HelpCircle
} from 'lucide-react'
import { useAppStore } from '../store/appStore'

export const Header: React.FC = () => {
  const { 
    theme, 
    setTheme, 
    isConnected, 
    currentModel,
    settings 
  } = useAppStore()

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6">
      {/* Left side - Page title and breadcrumb */}
      <div className="flex items-center space-x-4">
        <div>
          <h1 className="text-lg font-semibold text-gray-900">
            Academic Document Generation
          </h1>
          <p className="text-sm text-gray-500">
            AI-powered scholarly content creation
          </p>
        </div>
      </div>

      {/* Right side - Status and controls */}
      <div className="flex items-center space-x-4">
        {/* Model Status */}
        <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-full">
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-success-500' : 'bg-gray-400'
          }`} />
          <span className="text-sm text-gray-600 font-medium">
            {currentModel.split(':')[0]}
          </span>
        </div>

        {/* Connection Status */}
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
          isConnected 
            ? 'bg-success-100 text-success-700' 
            : 'bg-error-100 text-error-700'
        }`}>
          {isConnected ? (
            <Wifi className="w-4 h-4" />
          ) : (
            <WifiOff className="w-4 h-4" />
          )}
          <span className="text-sm font-medium">
            {isConnected ? 'Online' : 'Offline'}
          </span>
        </div>

        {/* Notifications */}
        {settings.notifications && (
          <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
            <Bell className="w-5 h-5 text-gray-600" />
            {/* Notification badge */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary-600 rounded-full" />
          </button>
        )}

        {/* Help */}
        <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
          <HelpCircle className="w-5 h-5 text-gray-600" />
        </button>

        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          {theme === 'light' ? (
            <Moon className="w-5 h-5 text-gray-600" />
          ) : (
            <Sun className="w-5 h-5 text-gray-600" />
          )}
        </button>

        {/* Settings */}
        <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
          <Settings className="w-5 h-5 text-gray-600" />
        </button>

        {/* User Profile */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div className="hidden md:block">
            <p className="text-sm font-medium text-gray-900">User</p>
            <p className="text-xs text-gray-500">Academic Researcher</p>
          </div>
        </div>
      </div>
    </header>
  )
}

import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  Download, 
  Trash2, 
  CheckCircle, 
  AlertCircle,
  Clock,
  HardDrive,
  Cpu,
  Zap
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { toast } from 'react-hot-toast'

export const ModelsPage: React.FC = () => {
  const {
    availableModels,
    currentModel,
    setCurrentModel,
    modelStatus,
    setModelStatus,
    setAvailableModels
  } = useAppStore()

  const [isLoading, setIsLoading] = useState(false)
  const [installingModels, setInstallingModels] = useState<Set<string>>(new Set())

  // Recommended models optimized for 8GB RAM
  const recommendedModels8GB = [
    { name: 'llama3.2:3b', size: '2.0GB', description: 'Efficient and fast, great for academic writing' },
    { name: 'qwen2.5:3b', size: '1.9GB', description: 'Excellent for research and analytical writing' },
    { name: 'gemma2:2b', size: '1.6GB', description: 'Google model, optimized for academic content' }
  ]

  const recommendedModels32GB = [
    { name: 'llama3.1:8b', size: '4.7GB', description: 'Advanced reasoning and complex documents' },
    { name: 'qwen2.5:7b', size: '4.4GB', description: 'Balanced performance for detailed research papers' },
    { name: 'deepseek-coder:6.7b', size: '3.8GB', description: 'Specialized for technical and scientific writing' }
  ]

  // Fetch available models on component mount
  useEffect(() => {
    fetchAvailableModels()
  }, [])

  const fetchAvailableModels = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/models/')
      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.models)

        // Set current model to first available if none set
        if (data.models.length > 0 && !currentModel) {
          setCurrentModel(data.models[0].name)
        }
      }
    } catch (error) {
      console.error('Error fetching models:', error)
      toast.error('Failed to fetch available models')
    }
  }

  const handleInstallModel = async (modelName: string) => {
    setInstallingModels(prev => new Set([...prev, modelName]))
    setModelStatus(modelName, 'loading')

    try {
      toast.success(`Installing ${modelName}...`)

      const response = await fetch('http://localhost:8001/api/models/pull', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model_name: modelName,
          stream: false
        })
      })

      if (response.ok) {
        setModelStatus(modelName, 'ready')
        toast.success(`${modelName} installed successfully!`)
        // Refresh available models
        await fetchAvailableModels()
      } else {
        throw new Error(`Installation failed: ${response.statusText}`)
      }

    } catch (error) {
      console.error('Installation error:', error)
      setModelStatus(modelName, 'error')
      toast.error(`Failed to install ${modelName}`)
    } finally {
      setInstallingModels(prev => {
        const newSet = new Set(prev)
        newSet.delete(modelName)
        return newSet
      })
    }
  }

  const handleDeleteModel = async (modelName: string) => {
    if (modelName === currentModel) {
      toast.error('Cannot delete the currently active model')
      return
    }

    try {
      const response = await fetch(`http://localhost:8001/api/models/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ model_name: modelName })
      })

      if (response.ok) {
        toast.success(`${modelName} deleted successfully`)
        await fetchAvailableModels()
      } else {
        throw new Error(`Deletion failed: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Deletion error:', error)
      toast.error(`Failed to delete ${modelName}`)
    }
  }

  const handleSetActiveModel = (modelName: string) => {
    setCurrentModel(modelName)
    toast.success(`Switched to ${modelName}`)
  }

  const getModelStatus = (modelName: string) => {
    const status = modelStatus[modelName]
    const isInstalled = availableModels.some(model => model.name === modelName)
    const isInstalling = installingModels.has(modelName)

    if (isInstalling || status === 'loading') return 'loading'
    if (status === 'error') return 'error'
    if (isInstalled) return 'installed'
    return 'not-installed'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Clock className="w-4 h-4 text-warning-600 animate-spin" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-600" />
      case 'installed':
        return <CheckCircle className="w-4 h-4 text-success-600" />
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">Model Management</h1>
          <p className="text-gray-600 mt-1">
            Install and manage AI models for academic document generation
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6">
          {/* Current Model Status */}
          <div className="card p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Model</h2>
            <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{currentModel}</h3>
                  <p className="text-sm text-gray-600">Active model for document generation</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="status-online" />
                <span className="text-sm font-medium text-success-700">Ready</span>
              </div>
            </div>
          </div>

          {/* System Requirements */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <HardDrive className="w-8 h-8 text-primary-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">RAM Available</p>
                  <p className="text-lg font-bold text-primary-600">8 GB</p>
                </div>
              </div>
            </div>
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <Cpu className="w-8 h-8 text-success-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">CPU Usage</p>
                  <p className="text-lg font-bold text-success-600">45%</p>
                </div>
              </div>
            </div>
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <Zap className="w-8 h-8 text-warning-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Models Loaded</p>
                  <p className="text-lg font-bold text-warning-600">{availableModels.length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Installed Models */}
          {availableModels.length > 0 && (
            <div className="card p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Installed Models ({availableModels.length})
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {availableModels.map((model) => {
                  const isActive = currentModel === model.name

                  return (
                    <div key={model.name} className={`p-4 rounded-lg border-2 transition-colors ${
                      isActive
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-gray-900">{model.name}</h3>
                            <CheckCircle className="w-4 h-4 text-success-600" />
                            {isActive && (
                              <span className="px-2 py-1 text-xs bg-primary-600 text-white rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-2">
                            Size: {typeof model.size === 'number' ? `${(model.size / 1e9).toFixed(1)}GB` : model.size}
                          </p>
                          <p className="text-xs text-gray-500">
                            Modified: {new Date(model.modified_at).toLocaleDateString()}
                          </p>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          {!isActive && (
                            <button
                              onClick={() => handleSetActiveModel(model.name)}
                              className="btn-secondary text-xs px-3 py-1"
                            >
                              Use
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteModel(model.name)}
                            className="p-2 rounded-lg hover:bg-red-100 transition-colors"
                            disabled={isActive}
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Recommended Models */}
          <div className="space-y-6">
            {/* 8GB RAM Models */}
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Recommended for 8GB RAM (Current)
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {recommendedModels8GB.map((model) => {
                  const status = getModelStatus(model.name)
                  const isActive = currentModel === model.name
                  
                  return (
                    <div key={model.name} className={`p-4 rounded-lg border-2 transition-colors ${
                      isActive 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-gray-900">{model.name}</h3>
                            {getStatusIcon(status)}
                            {isActive && (
                              <span className="px-2 py-1 text-xs bg-primary-600 text-white rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{model.description}</p>
                          <p className="text-xs text-gray-500 mt-2">Size: {model.size}</p>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          {status === 'installed' ? (
                            <>
                              {!isActive && (
                                <button
                                  onClick={() => handleSetActiveModel(model.name)}
                                  className="btn-secondary text-xs px-3 py-1"
                                >
                                  Use
                                </button>
                              )}
                              <button
                                onClick={() => handleDeleteModel(model.name)}
                                className="p-2 rounded-lg hover:bg-red-100 transition-colors"
                                disabled={isActive}
                              >
                                <Trash2 className="w-4 h-4 text-red-500" />
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleInstallModel(model.name)}
                              disabled={status === 'loading' || isLoading}
                              className="btn-primary text-xs px-3 py-1 disabled:opacity-50"
                            >
                              {status === 'loading' ? (
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 spinner" />
                                  <span>Installing...</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <Download className="w-3 h-3" />
                                  <span>Install</span>
                                </div>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 32GB RAM Models */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  Advanced Models (32GB+ RAM Required)
                </h2>
                <span className="px-3 py-1 text-xs bg-warning-100 text-warning-800 rounded-full">
                  Upgrade Required
                </span>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {recommendedModels32GB.map((model) => (
                  <div key={model.name} className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50 opacity-75">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-700">{model.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{model.description}</p>
                        <p className="text-xs text-gray-400 mt-2">Size: {model.size}</p>
                      </div>
                      <button
                        disabled
                        className="btn-secondary text-xs px-3 py-1 opacity-50 cursor-not-allowed"
                      >
                        Requires 32GB RAM
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

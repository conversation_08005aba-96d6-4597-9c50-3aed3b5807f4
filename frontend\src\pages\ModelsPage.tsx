import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  Download, 
  Trash2, 
  CheckCircle, 
  AlertCircle,
  Clock,
  HardDrive,
  Cpu,
  Zap
} from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { toast } from 'react-hot-toast'

export const ModelsPage: React.FC = () => {
  const { 
    availableModels, 
    currentModel, 
    setCurrentModel, 
    modelStatus,
    setModelStatus 
  } = useAppStore()
  
  const [isLoading, setIsLoading] = useState(false)

  // Recommended models for different RAM configurations
  const recommendedModels8GB = [
    { name: 'qwen2:7b-instruct', size: '4.4GB', description: 'General purpose, excellent for academic writing' },
    { name: 'deepseek-coder:6.7b', size: '3.8GB', description: 'Specialized for code generation and technical docs' },
    { name: 'phi3:3.8b', size: '2.2GB', description: 'Efficient reasoning, good for analysis' },
    { name: 'llama3:8b-instruct', size: '4.7GB', description: 'Strong instruction following' },
    { name: 'mistral:7b-instruct', size: '4.1GB', description: 'Balanced performance and efficiency' }
  ]

  const recommendedModels32GB = [
    { name: 'qwen2:32b-instruct', size: '18GB', description: 'Advanced reasoning and complex document generation' },
    { name: 'deepseek-coder:33b', size: '19GB', description: 'Superior code generation and technical writing' },
    { name: 'llama3:70b-instruct', size: '40GB', description: 'State-of-the-art performance (requires 64GB+ RAM)' },
    { name: 'mixtral:8x7b-instruct', size: '26GB', description: 'Mixture of experts, versatile capabilities' },
    { name: 'codellama:34b-instruct', size: '19GB', description: 'Specialized for code and technical documentation' }
  ]

  const handleInstallModel = async (modelName: string) => {
    setIsLoading(true)
    setModelStatus(modelName, 'loading')
    
    try {
      // Simulate model installation
      toast.success(`Installing ${modelName}...`)
      
      // In a real implementation, this would call the API
      setTimeout(() => {
        setModelStatus(modelName, 'ready')
        toast.success(`${modelName} installed successfully!`)
        setIsLoading(false)
      }, 3000)
      
    } catch (error) {
      setModelStatus(modelName, 'error')
      toast.error(`Failed to install ${modelName}`)
      setIsLoading(false)
    }
  }

  const handleDeleteModel = async (modelName: string) => {
    if (modelName === currentModel) {
      toast.error('Cannot delete the currently active model')
      return
    }

    try {
      // In a real implementation, this would call the API
      toast.success(`${modelName} deleted successfully`)
    } catch (error) {
      toast.error(`Failed to delete ${modelName}`)
    }
  }

  const handleSetActiveModel = (modelName: string) => {
    setCurrentModel(modelName)
    toast.success(`Switched to ${modelName}`)
  }

  const getModelStatus = (modelName: string) => {
    const status = modelStatus[modelName]
    const isInstalled = availableModels.some(model => model.name === modelName)
    
    if (status === 'loading') return 'loading'
    if (status === 'error') return 'error'
    if (isInstalled) return 'installed'
    return 'not-installed'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Clock className="w-4 h-4 text-warning-600 animate-spin" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-600" />
      case 'installed':
        return <CheckCircle className="w-4 h-4 text-success-600" />
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">Model Management</h1>
          <p className="text-gray-600 mt-1">
            Install and manage AI models for academic document generation
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6">
          {/* Current Model Status */}
          <div className="card p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Model</h2>
            <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{currentModel}</h3>
                  <p className="text-sm text-gray-600">Active model for document generation</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="status-online" />
                <span className="text-sm font-medium text-success-700">Ready</span>
              </div>
            </div>
          </div>

          {/* System Requirements */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <HardDrive className="w-8 h-8 text-primary-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">RAM Available</p>
                  <p className="text-lg font-bold text-primary-600">8 GB</p>
                </div>
              </div>
            </div>
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <Cpu className="w-8 h-8 text-success-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">CPU Usage</p>
                  <p className="text-lg font-bold text-success-600">45%</p>
                </div>
              </div>
            </div>
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <Zap className="w-8 h-8 text-warning-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Models Loaded</p>
                  <p className="text-lg font-bold text-warning-600">{availableModels.length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recommended Models */}
          <div className="space-y-6">
            {/* 8GB RAM Models */}
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Recommended for 8GB RAM (Current)
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {recommendedModels8GB.map((model) => {
                  const status = getModelStatus(model.name)
                  const isActive = currentModel === model.name
                  
                  return (
                    <div key={model.name} className={`p-4 rounded-lg border-2 transition-colors ${
                      isActive 
                        ? 'border-primary-500 bg-primary-50' 
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-gray-900">{model.name}</h3>
                            {getStatusIcon(status)}
                            {isActive && (
                              <span className="px-2 py-1 text-xs bg-primary-600 text-white rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{model.description}</p>
                          <p className="text-xs text-gray-500 mt-2">Size: {model.size}</p>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          {status === 'installed' ? (
                            <>
                              {!isActive && (
                                <button
                                  onClick={() => handleSetActiveModel(model.name)}
                                  className="btn-secondary text-xs px-3 py-1"
                                >
                                  Use
                                </button>
                              )}
                              <button
                                onClick={() => handleDeleteModel(model.name)}
                                className="p-2 rounded-lg hover:bg-red-100 transition-colors"
                                disabled={isActive}
                              >
                                <Trash2 className="w-4 h-4 text-red-500" />
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleInstallModel(model.name)}
                              disabled={status === 'loading' || isLoading}
                              className="btn-primary text-xs px-3 py-1 disabled:opacity-50"
                            >
                              {status === 'loading' ? (
                                <div className="flex items-center space-x-2">
                                  <div className="w-3 h-3 spinner" />
                                  <span>Installing...</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <Download className="w-3 h-3" />
                                  <span>Install</span>
                                </div>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 32GB RAM Models */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  Advanced Models (32GB+ RAM Required)
                </h2>
                <span className="px-3 py-1 text-xs bg-warning-100 text-warning-800 rounded-full">
                  Upgrade Required
                </span>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {recommendedModels32GB.map((model) => (
                  <div key={model.name} className="p-4 rounded-lg border-2 border-gray-200 bg-gray-50 opacity-75">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-700">{model.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{model.description}</p>
                        <p className="text-xs text-gray-400 mt-2">Size: {model.size}</p>
                      </div>
                      <button
                        disabled
                        className="btn-secondary text-xs px-3 py-1 opacity-50 cursor-not-allowed"
                      >
                        Requires 32GB RAM
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

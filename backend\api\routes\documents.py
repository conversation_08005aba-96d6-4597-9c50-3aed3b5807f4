"""
ASCAES Document API Routes
Handles document upload, processing, and management
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
import os
import hashlib
import uuid
from pathlib import Path

from core.database import get_db, Document, GeneratedDocument, DocumentFolder
from core.config import settings
from core.logging_config import get_logger
from services.document_service import DocumentService

logger = get_logger(__name__)
router = APIRouter()
document_service = DocumentService()

# Helper functions for format conversion
def _convert_to_latex(content: str) -> str:
    """Convert content to LaTeX format"""
    latex_template = r"""\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage[margin=1in]{geometry}

\title{Academic Document}
\author{ASCAES Generated}
\date{\today}

\begin{document}
\maketitle

""" + content.replace('\n\n', '\n\n\\par\n') + r"""

\end{document}"""
    return latex_template

def _convert_to_rtf(content: str) -> str:
    """Convert content to RTF format"""
    rtf_content = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
\f0\fs24 """ + content.replace('\n', '\\par ') + "}"
    return rtf_content

def _convert_to_pdf(content: str, title: str) -> str:
    """Convert content to PDF format (placeholder - returns HTML)"""
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <style>
        body {{ font-family: 'Times New Roman', serif; margin: 2cm; line-height: 1.6; }}
        h1 {{ color: #333; border-bottom: 2px solid #333; }}
        h2 {{ color: #555; margin-top: 2em; }}
        h3 {{ color: #777; }}
        p {{ text-align: justify; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    {content.replace(chr(10), '<br>').replace('# ', '<h1>').replace('## ', '<h2>').replace('### ', '<h3>')}
</body>
</html>"""
    return html_content

# Pydantic models
class DocumentResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    mime_type: str
    upload_date: datetime
    processed: bool
    conversation_id: Optional[int] = None

class GeneratedDocumentResponse(BaseModel):
    id: int
    title: str
    document_type: str
    writing_style: str
    output_format: str
    conversation_id: int
    created_at: datetime
    updated_at: datetime
    word_count: Optional[int] = None
    page_count: Optional[int] = None
    quality_score: Optional[float] = None

class DocumentGenerationRequest(BaseModel):
    title: str
    document_type: str  # paper, thesis, report, etc.
    writing_style: str  # analytical, instructional, etc.
    output_format: str = "pdf"  # pdf, latex, rtf, txt
    conversation_id: int
    content_requirements: Optional[str] = None
    template: Optional[str] = None
    length_target: Optional[int] = None  # word count target

@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    conversation_id: Optional[int] = Form(None),
    db: Session = Depends(get_db)
):
    """Upload document for processing"""
    try:
        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in settings.SUPPORTED_FORMATS:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file format. Supported: {', '.join(settings.SUPPORTED_FORMATS)}"
            )
        
        # Check file size
        content = await file.read()
        if len(content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Generate unique filename
        file_hash = hashlib.sha256(content).hexdigest()
        unique_filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = settings.DOCUMENTS_PATH / unique_filename
        
        # Save file
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Create database record
        db_document = Document(
            filename=unique_filename,
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=len(content),
            file_type=file_extension,
            mime_type=file.content_type or "application/octet-stream",
            conversation_id=conversation_id,
            checksum=file_hash
        )
        
        db.add(db_document)
        db.commit()
        db.refresh(db_document)
        
        # Start background processing
        await document_service.process_document_async(db_document.id)
        
        return DocumentResponse(
            id=db_document.id,
            filename=db_document.filename,
            original_filename=db_document.original_filename,
            file_size=db_document.file_size,
            file_type=db_document.file_type,
            mime_type=db_document.mime_type,
            upload_date=db_document.upload_date,
            processed=db_document.processed,
            conversation_id=db_document.conversation_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload document")

@router.get("/", response_model=List[DocumentResponse])
async def get_documents(
    conversation_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get list of uploaded documents"""
    try:
        query = db.query(Document)
        
        if conversation_id:
            query = query.filter(Document.conversation_id == conversation_id)
        
        documents = query.order_by(Document.upload_date.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
        
        return [DocumentResponse(
            id=doc.id,
            filename=doc.filename,
            original_filename=doc.original_filename,
            file_size=doc.file_size,
            file_type=doc.file_type,
            mime_type=doc.mime_type,
            upload_date=doc.upload_date,
            processed=doc.processed,
            conversation_id=doc.conversation_id
        ) for doc in documents]
        
    except Exception as e:
        logger.error(f"Error fetching documents: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch documents")

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Get specific document"""
    try:
        document = db.query(Document)\
            .filter(Document.id == document_id)\
            .first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return DocumentResponse(
            id=document.id,
            filename=document.filename,
            original_filename=document.original_filename,
            file_size=document.file_size,
            file_type=document.file_type,
            mime_type=document.mime_type,
            upload_date=document.upload_date,
            processed=document.processed,
            conversation_id=document.conversation_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching document {document_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch document")

@router.get("/{document_id}/download")
async def download_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Download document file"""
    try:
        document = db.query(Document)\
            .filter(Document.id == document_id)\
            .first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        file_path = Path(document.file_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="Document file not found")
        
        return FileResponse(
            path=str(file_path),
            filename=document.original_filename,
            media_type=document.mime_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to download document")

@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Delete document"""
    try:
        document = db.query(Document)\
            .filter(Document.id == document_id)\
            .first()
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Delete file
        file_path = Path(document.file_path)
        if file_path.exists():
            file_path.unlink()
        
        # Delete database record
        db.delete(document)
        db.commit()
        
        return {"message": "Document deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete document")

@router.post("/generate", response_model=GeneratedDocumentResponse)
async def generate_document(
    request: DocumentGenerationRequest,
    db: Session = Depends(get_db)
):
    """Generate academic document"""
    try:
        # Start document generation
        result = await document_service.generate_document(request)
        
        return GeneratedDocumentResponse(
            id=result["id"],
            title=result["title"],
            document_type=result["document_type"],
            writing_style=result["writing_style"],
            output_format=result["output_format"],
            conversation_id=result["conversation_id"],
            created_at=result["created_at"],
            updated_at=result["updated_at"],
            word_count=result.get("word_count"),
            page_count=result.get("page_count"),
            quality_score=result.get("quality_score")
        )
        
    except Exception as e:
        logger.error(f"Error generating document: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate document")

@router.get("/generated/", response_model=List[GeneratedDocumentResponse])
async def get_generated_documents(
    conversation_id: Optional[int] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get list of generated documents"""
    try:
        query = db.query(GeneratedDocument)
        
        if conversation_id:
            query = query.filter(GeneratedDocument.conversation_id == conversation_id)
        
        documents = query.order_by(GeneratedDocument.created_at.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
        
        return [GeneratedDocumentResponse(
            id=doc.id,
            title=doc.title,
            document_type=doc.document_type,
            writing_style=doc.writing_style,
            output_format=doc.output_format,
            conversation_id=doc.conversation_id,
            created_at=doc.created_at,
            updated_at=doc.updated_at,
            word_count=doc.word_count,
            page_count=doc.page_count,
            quality_score=doc.quality_score
        ) for doc in documents]
        
    except Exception as e:
        logger.error(f"Error fetching generated documents: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch generated documents")


@router.get("/generated/{document_id}/download")
async def download_generated_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Download generated document"""
    try:
        # Get document from database
        document = db.query(GeneratedDocument)\
            .filter(GeneratedDocument.id == document_id)\
            .first()

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Check if file exists
        if document.file_path and os.path.exists(document.file_path):
            # Determine media type based on file extension
            file_path = Path(document.file_path)
            extension = file_path.suffix.lower()

            media_type_map = {
                '.pdf': 'application/pdf',
                '.tex': 'application/x-latex',
                '.rtf': 'application/rtf',
                '.txt': 'text/plain'
            }

            media_type = media_type_map.get(extension, 'application/octet-stream')

            # Generate download filename
            safe_title = "".join(c for c in document.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title.replace(' ', '_')[:50]
            download_filename = f"{safe_title}{extension}"

            return FileResponse(
                path=document.file_path,
                media_type=media_type,
                filename=download_filename
            )
        else:
            # File doesn't exist, generate content on-the-fly
            content = document.content or "No content available"

            if document.output_format == "latex":
                content = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\title{{{document.title}}}
\\author{{ASCAES Generated}}
\\date{{\\today}}
\\begin{{document}}
\\maketitle
{content}
\\end{{document}}"""
                media_type = 'application/x-latex'
                extension = '.tex'
            elif document.output_format == "rtf":
                # Fix f-string backslash issue
                rtf_content = content.replace('\n', '\\par ')
                content = f"""{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}}
\\f0\\fs28\\b {document.title}\\b0\\fs24\\par\\par
{rtf_content}}}"""
                media_type = 'application/rtf'
                extension = '.rtf'
            else:
                content = f"{document.title}\n{'='*len(document.title)}\n\n{content}"
                media_type = 'text/plain'
                extension = '.txt'

            # Create temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix=extension, delete=False, encoding='utf-8') as tmp_file:
                tmp_file.write(content)
                tmp_path = tmp_file.name

            safe_title = "".join(c for c in document.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title.replace(' ', '_')[:50]
            download_filename = f"{safe_title}{extension}"

            return FileResponse(
                path=tmp_path,
                media_type=media_type,
                filename=download_filename
            )

    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/generated/{document_id}/download/{format}")
async def download_generated_document_format(
    document_id: int,
    format: str,
    db: Session = Depends(get_db)
):
    """Download generated document in specific format (pdf, latex, rtf, txt)"""
    try:
        document = db.query(GeneratedDocument)\
            .filter(GeneratedDocument.id == document_id)\
            .first()

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Validate format
        valid_formats = ["pdf", "latex", "rtf", "txt"]
        if format not in valid_formats:
            raise HTTPException(status_code=400, detail=f"Invalid format. Must be one of: {valid_formats}")

        # Generate file path for requested format
        output_dir = settings.DOCUMENTS_PATH / "generated"
        output_dir.mkdir(exist_ok=True)
        filename_base = f"{document.id}_{document.title.replace(' ', '_')}"

        format_extensions = {
            "pdf": ".pdf",
            "latex": ".tex",
            "rtf": ".rtf",
            "txt": ".txt"
        }

        file_path = output_dir / f"{filename_base}{format_extensions[format]}"

        # Generate content in requested format if file doesn't exist
        if not file_path.exists():
            content = document.content or "No content available"

            if format == "latex":
                formatted_content = _convert_to_latex(content)
            elif format == "rtf":
                formatted_content = _convert_to_rtf(content)
            elif format == "pdf":
                formatted_content = _convert_to_pdf(content, document.title)
            else:  # txt
                formatted_content = content

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)

        media_type_map = {
            'pdf': 'text/html',  # Since we're generating HTML for PDF placeholder
            'latex': 'application/x-latex',
            'rtf': 'application/rtf',
            'txt': 'text/plain'
        }

        media_type = media_type_map.get(format, 'application/octet-stream')

        # Generate download filename
        safe_title = "".join(c for c in document.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')[:50]
        download_filename = f"{safe_title}{format_extensions[format]}"

        return FileResponse(
            path=str(file_path),
            media_type=media_type,
            filename=download_filename
        )

    except Exception as e:
        logger.error(f"Error downloading document {document_id} in format {format}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/generated/{document_id}")
async def delete_generated_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Delete a generated document and its files"""
    try:
        document = db.query(GeneratedDocument)\
            .filter(GeneratedDocument.id == document_id)\
            .first()

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Delete associated files
        if document.file_path and os.path.exists(document.file_path):
            try:
                os.remove(document.file_path)
            except Exception as e:
                logger.warning(f"Could not delete file {document.file_path}: {e}")

        # Delete all format files
        output_dir = settings.DOCUMENTS_PATH / "generated"
        filename_base = f"{document.id}_{document.title.replace(' ', '_')}"

        for ext in [".pdf", ".tex", ".rtf", ".txt"]:
            file_path = output_dir / f"{filename_base}{ext}"
            if file_path.exists():
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Could not delete file {file_path}: {e}")

        # If this is a parent document, delete all child pages
        if not document.is_page:
            child_pages = db.query(GeneratedDocument)\
                .filter(GeneratedDocument.parent_document_id == document_id)\
                .all()

            for child in child_pages:
                db.delete(child)

        # Delete the document from database
        db.delete(document)
        db.commit()

        return {"message": "Document deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete document")

@router.delete("/folders/{folder_id}")
async def delete_document_folder(
    folder_id: int,
    db: Session = Depends(get_db)
):
    """Delete a document folder and all its contents"""
    try:
        folder = db.query(DocumentFolder)\
            .filter(DocumentFolder.id == folder_id)\
            .first()

        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        # Delete all documents in the folder
        documents = db.query(GeneratedDocument)\
            .filter(GeneratedDocument.folder_id == folder_id)\
            .all()

        for document in documents:
            # Delete associated files
            if document.file_path and os.path.exists(document.file_path):
                try:
                    os.remove(document.file_path)
                except Exception as e:
                    logger.warning(f"Could not delete file {document.file_path}: {e}")

            # Delete all format files
            output_dir = settings.DOCUMENTS_PATH / "generated"
            filename_base = f"{document.id}_{document.title.replace(' ', '_')}"

            for ext in [".pdf", ".tex", ".rtf", ".txt"]:
                file_path = output_dir / f"{filename_base}{ext}"
                if file_path.exists():
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"Could not delete file {file_path}: {e}")

            db.delete(document)

        # Delete child folders recursively
        child_folders = db.query(DocumentFolder)\
            .filter(DocumentFolder.parent_folder_id == folder_id)\
            .all()

        for child_folder in child_folders:
            # Recursive delete (this will call the same endpoint)
            await delete_document_folder(child_folder.id, db)

        # Delete the folder itself
        db.delete(folder)
        db.commit()

        return {"message": "Folder and all contents deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting folder {folder_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete folder")

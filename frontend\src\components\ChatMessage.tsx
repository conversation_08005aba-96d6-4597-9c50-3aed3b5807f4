import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { Copy, Check, User, Bot, AlertCircle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { Message } from '../store/appStore'
import { useState } from 'react'
import { DocumentProgress } from './DocumentProgress'

interface ChatMessageProps {
  message: Message
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const getMessageIcon = () => {
    switch (message.role) {
      case 'user':
        return <User className="w-5 h-5" />
      case 'assistant':
        return <Bot className="w-5 h-5" />
      case 'system':
        return <AlertCircle className="w-5 h-5" />
      default:
        return null
    }
  }

  const getMessageStyle = () => {
    switch (message.role) {
      case 'user':
        return 'message-bubble user'
      case 'assistant':
        return 'message-bubble assistant'
      case 'system':
        return 'message-bubble system'
      default:
        return 'message-bubble'
    }
  }

  return (
    <div className={`message-enter flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
      <div className="max-w-4xl w-full">
        <div className="flex items-start space-x-3">
          {/* Avatar */}
          {message.role !== 'user' && (
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
              message.role === 'assistant' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-500 text-white'
            }`}>
              {getMessageIcon()}
            </div>
          )}

          {/* Message Content */}
          <div className={`flex-1 ${message.role === 'user' ? 'flex justify-end' : ''}`}>
            <div className={getMessageStyle()}>
              {/* Message Header */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">
                    {message.role === 'user' ? 'You' : 
                     message.role === 'assistant' ? 'ASCAES' : 'System'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(message.timestamp, { addSuffix: true })}
                  </span>
                </div>
                
                {/* Copy Button */}
                <button
                  onClick={() => handleCopy(message.content)}
                  className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-100 transition-all"
                >
                  {copied ? (
                    <Check className="w-4 h-4 text-success-600" />
                  ) : (
                    <Copy className="w-4 h-4 text-gray-500" />
                  )}
                </button>
              </div>

              {/* Message Body */}
              <div className="prose prose-sm max-w-none">
                {message.role === 'system' ? (
                  <>
                    {message.metadata?.progress !== undefined ? (
                      <DocumentProgress
                        progress={message.metadata.progress}
                        status="processing"
                        message={message.content.replace(/📝 Progress Update: \d+% complete - /, '')}
                      />
                    ) : (
                      <p className="text-center italic">{message.content}</p>
                    )}
                  </>
                ) : (
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[rehypeKatex]}
                    components={{
                      code({ node, inline, className, children, ...props }) {
                        const match = /language-(\w+)/.exec(className || '')
                        return !inline && match ? (
                          <div className="relative">
                            <SyntaxHighlighter
                              style={tomorrow}
                              language={match[1]}
                              PreTag="div"
                              className="rounded-lg"
                              {...props}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                            <button
                              onClick={() => handleCopy(String(children))}
                              className="absolute top-2 right-2 p-1 rounded bg-gray-700 hover:bg-gray-600 text-white opacity-0 hover:opacity-100 transition-opacity"
                            >
                              {copied ? (
                                <Check className="w-4 h-4" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        ) : (
                          <code className={className} {...props}>
                            {children}
                          </code>
                        )
                      },
                      table({ children }) {
                        return (
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              {children}
                            </table>
                          </div>
                        )
                      },
                      th({ children }) {
                        return (
                          <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {children}
                          </th>
                        )
                      },
                      td({ children }) {
                        return (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {children}
                          </td>
                        )
                      },
                    }}
                  >
                    {message.content}
                  </ReactMarkdown>
                )}
              </div>

              {/* Message Metadata */}
              {message.metadata && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <details className="text-xs text-gray-500">
                    <summary className="cursor-pointer hover:text-gray-700">
                      Message Details
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-x-auto">
                      {JSON.stringify(message.metadata, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
            </div>
          </div>

          {/* User Avatar */}
          {message.role === 'user' && (
            <div className="flex-shrink-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white">
              {getMessageIcon()}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

"""
Quality Agent
Responsible for quality assurance, grammar checking, and academic standards validation
"""

import re
from typing import Dict, Any, List, Tuple
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class QualityAgent(BaseAgent):
    """Agent specialized in quality assurance and academic standards validation"""
    
    def __init__(self):
        super().__init__(
            agent_id="quality_agent",
            name="Quality Agent",
            description="Ensures quality, consistency, and adherence to academic standards"
        )
        
        # Quality-specific configuration
        self.config.update({
            "quality_metrics": {
                "readability": {"min_score": 0.7, "weight": 0.2},
                "coherence": {"min_score": 0.8, "weight": 0.25},
                "academic_tone": {"min_score": 0.85, "weight": 0.2},
                "citation_quality": {"min_score": 0.8, "weight": 0.15},
                "structure": {"min_score": 0.8, "weight": 0.2}
            },
            "grammar_rules": self._load_grammar_rules(),
            "academic_standards": self._load_academic_standards(),
            "citation_patterns": self._load_citation_patterns(),
            "quality_thresholds": {
                "excellent": 0.9,
                "good": 0.8,
                "acceptable": 0.7,
                "needs_improvement": 0.6
            }
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define quality agent capabilities"""
        return [
            AgentCapability(
                name="grammar_checking",
                description="Check grammar, spelling, and language usage",
                input_types=["text_content", "document_sections"],
                output_types=["grammar_report", "correction_suggestions"]
            ),
            AgentCapability(
                name="academic_standards_validation",
                description="Validate adherence to academic writing standards",
                input_types=["academic_content", "style_requirements"],
                output_types=["standards_report", "compliance_score"]
            ),
            AgentCapability(
                name="citation_verification",
                description="Verify citation format and consistency",
                input_types=["citations", "reference_list", "citation_style"],
                output_types=["citation_report", "format_corrections"]
            ),
            AgentCapability(
                name="coherence_analysis",
                description="Analyze document coherence and flow",
                input_types=["document_structure", "content_sections"],
                output_types=["coherence_report", "flow_suggestions"]
            ),
            AgentCapability(
                name="comprehensive_quality_check",
                description="Perform complete quality assessment",
                input_types=["complete_document", "quality_requirements"],
                output_types=["quality_report", "improvement_recommendations"]
            )
        ]
    
    def _load_grammar_rules(self) -> Dict[str, List[str]]:
        """Load grammar checking rules"""
        return {
            "common_errors": [
                r"\b(it's)\b(?=\s+(?:own|purpose|effect))",  # its vs it's
                r"\b(there)\b(?=\s+(?:are|is))\s+(?:a|an|the)\s+\w+\s+(?:that|which)",  # there vs their
                r"\b(affect)\b(?=\s+(?:on|upon))",  # affect vs effect
                r"\b(then)\b(?=\s+(?:the|a|an))",  # then vs than in comparisons
            ],
            "academic_voice": [
                r"\bI\s+think\b",  # Avoid first person opinions
                r"\bwe\s+believe\b",  # Avoid subjective statements
                r"\bobviously\b",  # Avoid obvious statements
                r"\bclearly\b(?!\s+(?:defined|stated|shown))",  # Avoid unsupported clarity claims
            ],
            "passive_voice_overuse": [
                r"\b(?:was|were|is|are|been|being)\s+\w+ed\b",  # Basic passive voice pattern
            ],
            "wordiness": [
                r"\bin\s+order\s+to\b",  # Replace with "to"
                r"\bdue\s+to\s+the\s+fact\s+that\b",  # Replace with "because"
                r"\bat\s+this\s+point\s+in\s+time\b",  # Replace with "now"
            ]
        }
    
    def _load_academic_standards(self) -> Dict[str, Dict[str, Any]]:
        """Load academic writing standards"""
        return {
            "structure": {
                "introduction_length": {"min_percent": 10, "max_percent": 15},
                "conclusion_length": {"min_percent": 8, "max_percent": 12},
                "paragraph_length": {"min_words": 50, "max_words": 250},
                "section_balance": {"max_variance": 0.3}
            },
            "style": {
                "sentence_length": {"min_words": 10, "max_words": 30, "avg_target": 20},
                "vocabulary_level": {"min_grade_level": 12, "max_grade_level": 16},
                "formality_score": {"min_score": 0.8},
                "objectivity_score": {"min_score": 0.85}
            },
            "citations": {
                "density": {"min_per_1000_words": 15, "max_per_1000_words": 35},
                "recency": {"min_recent_percent": 60, "recent_years": 5},
                "diversity": {"min_unique_sources": 10}
            }
        }
    
    def _load_citation_patterns(self) -> Dict[str, str]:
        """Load citation format patterns"""
        return {
            "APA": {
                "in_text": r"\([A-Za-z]+(?:\s+&\s+[A-Za-z]+)?,\s+\d{4}\)",
                "reference": r"^[A-Za-z]+,\s+[A-Z]\.\s+\(\d{4}\)\.",
            },
            "MLA": {
                "in_text": r"\([A-Za-z]+\s+\d+\)",
                "reference": r'^[A-Za-z]+,\s+[A-Za-z]+\.\s+"[^"]+"\.',
            },
            "Chicago": {
                "in_text": r"\([A-Za-z]+\s+\d{4},\s+\d+\)",
                "reference": r"^[A-Za-z]+,\s+[A-Za-z]+\.\s+\"[^\"]+\"\.",
            }
        }
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute quality checking task"""
        task_type = task.get("type", "comprehensive_quality_check")
        
        if task_type == "grammar_checking":
            return await self._check_grammar(task)
        elif task_type == "academic_standards_validation":
            return await self._validate_academic_standards(task)
        elif task_type == "citation_verification":
            return await self._verify_citations(task)
        elif task_type == "coherence_analysis":
            return await self._analyze_coherence(task)
        elif task_type == "comprehensive_quality_check":
            return await self._comprehensive_quality_check(task)
        else:
            raise ValueError(f"Unknown quality task type: {task_type}")
    
    async def _check_grammar(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Check grammar and language usage"""
        content = task.get("content", "")
        check_level = task.get("level", "comprehensive")  # basic, standard, comprehensive
        
        # Perform rule-based grammar checking
        grammar_issues = self._rule_based_grammar_check(content)
        
        # Use LLM for advanced grammar checking
        llm_grammar_check = await self._llm_grammar_check(content, check_level)
        
        # Combine results
        all_issues = grammar_issues + llm_grammar_check.get("issues", [])
        
        return {
            "success": True,
            "grammar_score": self._calculate_grammar_score(all_issues, len(content.split())),
            "issues": all_issues,
            "suggestions": llm_grammar_check.get("suggestions", []),
            "word_count": len(content.split()),
            "error_density": len(all_issues) / max(len(content.split()), 1) * 1000  # errors per 1000 words
        }
    
    async def _validate_academic_standards(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Validate adherence to academic writing standards"""
        content = task.get("content", "")
        writing_style = task.get("writing_style", "analytical")
        document_type = task.get("document_type", "research_paper")
        
        # Analyze structure
        structure_analysis = self._analyze_structure(content)
        
        # Analyze style
        style_analysis = self._analyze_style(content, writing_style)
        
        # Check academic tone
        tone_analysis = await self._analyze_academic_tone(content)
        
        # Calculate overall compliance score
        compliance_score = self._calculate_compliance_score(
            structure_analysis, style_analysis, tone_analysis
        )
        
        return {
            "success": True,
            "compliance_score": compliance_score,
            "structure_analysis": structure_analysis,
            "style_analysis": style_analysis,
            "tone_analysis": tone_analysis,
            "recommendations": self._generate_standards_recommendations(
                structure_analysis, style_analysis, tone_analysis
            )
        }
    
    async def _verify_citations(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Verify citation format and consistency"""
        content = task.get("content", "")
        citation_style = task.get("citation_style", "APA")
        references = task.get("references", [])
        
        # Extract citations from content
        in_text_citations = self._extract_citations(content, citation_style)
        
        # Check citation format
        format_issues = self._check_citation_format(in_text_citations, citation_style)
        
        # Check citation consistency
        consistency_issues = self._check_citation_consistency(in_text_citations, references)
        
        # Calculate citation quality score
        citation_score = self._calculate_citation_score(
            in_text_citations, format_issues, consistency_issues, len(content.split())
        )
        
        return {
            "success": True,
            "citation_score": citation_score,
            "in_text_citations": len(in_text_citations),
            "format_issues": format_issues,
            "consistency_issues": consistency_issues,
            "citation_density": len(in_text_citations) / max(len(content.split()), 1) * 1000,
            "recommendations": self._generate_citation_recommendations(
                format_issues, consistency_issues, citation_style
            )
        }
    
    async def _analyze_coherence(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze document coherence and flow"""
        content = task.get("content", "")
        sections = task.get("sections", [])
        
        # Analyze paragraph transitions
        transition_analysis = self._analyze_transitions(content)
        
        # Check logical flow
        flow_analysis = await self._analyze_logical_flow(content, sections)
        
        # Calculate coherence score
        coherence_score = self._calculate_coherence_score(transition_analysis, flow_analysis)
        
        return {
            "success": True,
            "coherence_score": coherence_score,
            "transition_analysis": transition_analysis,
            "flow_analysis": flow_analysis,
            "suggestions": self._generate_coherence_suggestions(transition_analysis, flow_analysis)
        }
    
    async def _comprehensive_quality_check(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive quality assessment"""
        content = task.get("content", "")
        requirements = task.get("requirements", {})
        
        # Run all quality checks
        grammar_result = await self._check_grammar({"content": content, "level": "comprehensive"})
        standards_result = await self._validate_academic_standards({
            "content": content,
            "writing_style": requirements.get("writing_style", "analytical"),
            "document_type": requirements.get("document_type", "research_paper")
        })
        citation_result = await self._verify_citations({
            "content": content,
            "citation_style": requirements.get("citation_style", "APA"),
            "references": requirements.get("references", [])
        })
        coherence_result = await self._analyze_coherence({
            "content": content,
            "sections": requirements.get("sections", [])
        })
        
        # Calculate overall quality score
        overall_score = self._calculate_overall_quality_score(
            grammar_result, standards_result, citation_result, coherence_result
        )
        
        # Generate comprehensive recommendations
        recommendations = self._generate_comprehensive_recommendations(
            grammar_result, standards_result, citation_result, coherence_result
        )
        
        return {
            "success": True,
            "overall_quality_score": overall_score,
            "quality_level": self._determine_quality_level(overall_score),
            "detailed_scores": {
                "grammar": grammar_result["grammar_score"],
                "academic_standards": standards_result["compliance_score"],
                "citations": citation_result["citation_score"],
                "coherence": coherence_result["coherence_score"]
            },
            "comprehensive_analysis": {
                "grammar": grammar_result,
                "standards": standards_result,
                "citations": citation_result,
                "coherence": coherence_result
            },
            "recommendations": recommendations,
            "quality_timestamp": datetime.now().isoformat()
        }
    
    def _rule_based_grammar_check(self, content: str) -> List[Dict[str, Any]]:
        """Perform rule-based grammar checking"""
        issues = []
        
        for category, patterns in self.config["grammar_rules"].items():
            for pattern in patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    issues.append({
                        "type": category,
                        "position": match.start(),
                        "text": match.group(),
                        "severity": "medium",
                        "suggestion": self._get_grammar_suggestion(category, match.group())
                    })
        
        return issues
    
    async def _llm_grammar_check(self, content: str, level: str) -> Dict[str, Any]:
        """Use LLM for advanced grammar checking"""
        
        grammar_prompt = f"""Perform a {level} grammar and style check on the following academic text:

Text:
{content[:2000]}...

Check for:
1. Grammar errors (subject-verb agreement, tense consistency, etc.)
2. Punctuation issues
3. Academic writing style problems
4. Clarity and conciseness issues
5. Sentence structure problems

Provide specific suggestions for improvement and rate the overall grammar quality (0.0-1.0)."""

        system_prompt = """You are an expert academic editor with expertise in grammar, style, and 
        academic writing conventions. Provide detailed, actionable feedback for improving academic text quality."""

        grammar_analysis = await self._call_llm(grammar_prompt, system_prompt)
        
        # Parse LLM response (simplified)
        return {
            "issues": [],  # Would parse from LLM response
            "suggestions": [grammar_analysis],
            "llm_score": 0.85  # Would extract from LLM response
        }
    
    def _analyze_structure(self, content: str) -> Dict[str, Any]:
        """Analyze document structure"""
        paragraphs = content.split('\n\n')
        sentences = content.split('.')
        
        # Calculate structure metrics
        avg_paragraph_length = sum(len(p.split()) for p in paragraphs) / max(len(paragraphs), 1)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        
        # Check section balance (simplified)
        section_balance_score = 0.8  # Placeholder
        
        return {
            "paragraph_count": len(paragraphs),
            "sentence_count": len(sentences),
            "avg_paragraph_length": avg_paragraph_length,
            "avg_sentence_length": avg_sentence_length,
            "section_balance_score": section_balance_score,
            "structure_score": self._calculate_structure_score(
                avg_paragraph_length, avg_sentence_length, section_balance_score
            )
        }
    
    def _analyze_style(self, content: str, writing_style: str) -> Dict[str, Any]:
        """Analyze writing style"""
        word_count = len(content.split())
        
        # Calculate style metrics (simplified)
        formality_score = 0.85  # Would use more sophisticated analysis
        objectivity_score = 0.8
        vocabulary_level = 14  # Grade level
        
        return {
            "formality_score": formality_score,
            "objectivity_score": objectivity_score,
            "vocabulary_level": vocabulary_level,
            "style_consistency": 0.85,
            "target_style": writing_style,
            "style_score": (formality_score + objectivity_score) / 2
        }
    
    async def _analyze_academic_tone(self, content: str) -> Dict[str, Any]:
        """Analyze academic tone"""
        
        tone_prompt = f"""Analyze the academic tone of the following text:

{content[:1500]}...

Evaluate:
1. Objectivity (0.0-1.0)
2. Formality (0.0-1.0)
3. Precision (0.0-1.0)
4. Authority (0.0-1.0)
5. Overall academic tone (0.0-1.0)

Provide scores and brief explanations."""

        tone_analysis = await self._call_llm(tone_prompt)
        
        return {
            "objectivity": 0.85,  # Would parse from LLM
            "formality": 0.9,
            "precision": 0.8,
            "authority": 0.85,
            "overall_tone_score": 0.85,
            "analysis_text": tone_analysis
        }
    
    def _extract_citations(self, content: str, citation_style: str) -> List[str]:
        """Extract citations from content"""
        pattern = self.config["citation_patterns"].get(citation_style, {}).get("in_text", r"\([^)]+\)")
        citations = re.findall(pattern, content)
        return citations
    
    def _calculate_grammar_score(self, issues: List, word_count: int) -> float:
        """Calculate grammar quality score"""
        if word_count == 0:
            return 0.0
        
        error_rate = len(issues) / word_count * 1000  # errors per 1000 words
        
        # Score based on error density
        if error_rate <= 5:
            return 0.95
        elif error_rate <= 10:
            return 0.85
        elif error_rate <= 20:
            return 0.75
        elif error_rate <= 30:
            return 0.65
        else:
            return 0.5
    
    def _calculate_overall_quality_score(self, grammar_result: Dict, standards_result: Dict, 
                                       citation_result: Dict, coherence_result: Dict) -> float:
        """Calculate overall quality score"""
        weights = self.config["quality_metrics"]
        
        weighted_score = (
            grammar_result["grammar_score"] * weights["readability"]["weight"] +
            standards_result["compliance_score"] * weights["academic_tone"]["weight"] +
            citation_result["citation_score"] * weights["citation_quality"]["weight"] +
            coherence_result["coherence_score"] * weights["coherence"]["weight"] +
            standards_result["structure_analysis"]["structure_score"] * weights["structure"]["weight"]
        )
        
        return round(weighted_score, 3)
    
    def _determine_quality_level(self, score: float) -> str:
        """Determine quality level from score"""
        thresholds = self.config["quality_thresholds"]
        
        if score >= thresholds["excellent"]:
            return "excellent"
        elif score >= thresholds["good"]:
            return "good"
        elif score >= thresholds["acceptable"]:
            return "acceptable"
        else:
            return "needs_improvement"
    
    def _generate_comprehensive_recommendations(self, grammar_result: Dict, standards_result: Dict,
                                              citation_result: Dict, coherence_result: Dict) -> List[str]:
        """Generate comprehensive improvement recommendations"""
        recommendations = []
        
        # Grammar recommendations
        if grammar_result["grammar_score"] < 0.8:
            recommendations.append("Review and correct grammar errors, particularly focusing on academic writing conventions")
        
        # Standards recommendations
        if standards_result["compliance_score"] < 0.8:
            recommendations.extend(standards_result.get("recommendations", []))
        
        # Citation recommendations
        if citation_result["citation_score"] < 0.8:
            recommendations.extend(citation_result.get("recommendations", []))
        
        # Coherence recommendations
        if coherence_result["coherence_score"] < 0.8:
            recommendations.extend(coherence_result.get("suggestions", []))
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    # Placeholder methods for additional functionality
    def _get_grammar_suggestion(self, category: str, text: str) -> str:
        """Get grammar correction suggestion"""
        suggestions = {
            "common_errors": "Check for common grammar mistakes",
            "academic_voice": "Use more objective academic language",
            "passive_voice_overuse": "Consider using active voice",
            "wordiness": "Simplify and make more concise"
        }
        return suggestions.get(category, "Review this text for improvement")
    
    def _calculate_structure_score(self, avg_para: float, avg_sent: float, balance: float) -> float:
        """Calculate structure quality score"""
        # Simplified scoring
        para_score = 1.0 if 100 <= avg_para <= 200 else 0.7
        sent_score = 1.0 if 15 <= avg_sent <= 25 else 0.7
        return (para_score + sent_score + balance) / 3
    
    def _analyze_transitions(self, content: str) -> Dict[str, Any]:
        """Analyze paragraph transitions"""
        return {"transition_score": 0.8, "weak_transitions": 2}
    
    async def _analyze_logical_flow(self, content: str, sections: List) -> Dict[str, Any]:
        """Analyze logical flow"""
        return {"flow_score": 0.85, "flow_issues": []}
    
    def _calculate_coherence_score(self, transitions: Dict, flow: Dict) -> float:
        """Calculate coherence score"""
        return (transitions["transition_score"] + flow["flow_score"]) / 2
    
    def _generate_coherence_suggestions(self, transitions: Dict, flow: Dict) -> List[str]:
        """Generate coherence improvement suggestions"""
        return ["Improve paragraph transitions", "Enhance logical flow between sections"]
    
    def _check_citation_format(self, citations: List, style: str) -> List[str]:
        """Check citation format issues"""
        return []  # Placeholder
    
    def _check_citation_consistency(self, citations: List, references: List) -> List[str]:
        """Check citation consistency"""
        return []  # Placeholder
    
    def _calculate_citation_score(self, citations: List, format_issues: List, 
                                consistency_issues: List, word_count: int) -> float:
        """Calculate citation quality score"""
        return 0.85  # Placeholder
    
    def _generate_citation_recommendations(self, format_issues: List, 
                                         consistency_issues: List, style: str) -> List[str]:
        """Generate citation improvement recommendations"""
        return ["Ensure consistent citation format", "Verify all citations have corresponding references"]
    
    def _calculate_compliance_score(self, structure: Dict, style: Dict, tone: Dict) -> float:
        """Calculate academic standards compliance score"""
        return (structure["structure_score"] + style["style_score"] + tone["overall_tone_score"]) / 3
    
    def _generate_standards_recommendations(self, structure: Dict, style: Dict, tone: Dict) -> List[str]:
        """Generate academic standards recommendations"""
        return ["Maintain consistent academic tone", "Ensure proper document structure"]

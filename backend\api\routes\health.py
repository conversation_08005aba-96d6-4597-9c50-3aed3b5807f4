"""
ASCAES Health Check API Routes
System health monitoring and diagnostics
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any
from datetime import datetime
import psutil
import os
from pathlib import Path

from core.database import get_db, vector_db
from core.config import settings
from core.logging_config import get_logger
from services.ollama_service import OllamaService

logger = get_logger(__name__)
router = APIRouter()
ollama_service = OllamaService()

@router.get("/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }

@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """Comprehensive system health check"""
    health_status = {
        "timestamp": datetime.now().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "status": "healthy",
        "checks": {}
    }
    
    # Database check
    try:
        db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "type": "SQLite",
            "url": settings.DATABASE_URL
        }
    except Exception as e:
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # Vector database check
    try:
        collections = vector_db.client.list_collections()
        health_status["checks"]["vector_database"] = {
            "status": "healthy",
            "type": "ChromaDB",
            "collections": len(collections),
            "path": str(settings.VECTOR_DB_PATH)
        }
    except Exception as e:
        health_status["checks"]["vector_database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # Ollama service check
    try:
        models = await ollama_service.list_models()
        health_status["checks"]["ollama"] = {
            "status": "healthy",
            "host": settings.OLLAMA_HOST,
            "models_available": len(models.get("models", [])),
            "default_model": settings.DEFAULT_MODEL
        }
    except Exception as e:
        health_status["checks"]["ollama"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # Storage check
    try:
        storage_paths = [
            settings.STORAGE_PATH,
            settings.DOCUMENTS_PATH,
            settings.VECTORS_PATH,
            settings.TEMP_PATH
        ]
        
        storage_info = {}
        for path in storage_paths:
            if path.exists():
                stat = os.statvfs(str(path))
                free_space = stat.f_bavail * stat.f_frsize
                total_space = stat.f_blocks * stat.f_frsize
                used_space = total_space - free_space
                
                storage_info[path.name] = {
                    "path": str(path),
                    "exists": True,
                    "free_space_gb": round(free_space / (1024**3), 2),
                    "used_space_gb": round(used_space / (1024**3), 2),
                    "total_space_gb": round(total_space / (1024**3), 2),
                    "usage_percent": round((used_space / total_space) * 100, 2)
                }
            else:
                storage_info[path.name] = {
                    "path": str(path),
                    "exists": False
                }
        
        health_status["checks"]["storage"] = {
            "status": "healthy",
            "paths": storage_info
        }
    except Exception as e:
        health_status["checks"]["storage"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    return health_status

@router.get("/system")
async def system_metrics():
    """Get system resource metrics"""
    try:
        # CPU information
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memory information
        memory = psutil.virtual_memory()
        
        # Disk information
        disk = psutil.disk_usage('/')
        
        # Process information
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "usage_percent": cpu_percent,
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "usage_percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_gb": round(disk.used / (1024**3), 2),
                "usage_percent": round((disk.used / disk.total) * 100, 2)
            },
            "process": {
                "pid": process.pid,
                "memory_mb": round(process_memory.rss / (1024**2), 2),
                "cpu_percent": process.cpu_percent(),
                "threads": process.num_threads(),
                "create_time": datetime.fromtimestamp(process.create_time()).isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/dependencies")
async def check_dependencies():
    """Check external dependencies status"""
    dependencies = {
        "timestamp": datetime.now().isoformat(),
        "dependencies": {}
    }
    
    # Check Ollama
    try:
        models = await ollama_service.list_models()
        dependencies["dependencies"]["ollama"] = {
            "status": "available",
            "host": settings.OLLAMA_HOST,
            "models": len(models.get("models", []))
        }
    except Exception as e:
        dependencies["dependencies"]["ollama"] = {
            "status": "unavailable",
            "error": str(e)
        }
    
    # Check Python packages
    try:
        import fastapi
        import sqlalchemy
        import chromadb
        import PyMuPDF
        import pytesseract
        
        dependencies["dependencies"]["python_packages"] = {
            "status": "available",
            "versions": {
                "fastapi": fastapi.__version__,
                "sqlalchemy": sqlalchemy.__version__,
                "chromadb": chromadb.__version__,
                "PyMuPDF": PyMuPDF.__version__,
            }
        }
    except Exception as e:
        dependencies["dependencies"]["python_packages"] = {
            "status": "incomplete",
            "error": str(e)
        }
    
    # Check LaTeX (if available)
    try:
        import subprocess
        result = subprocess.run(["pdflatex", "--version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            dependencies["dependencies"]["latex"] = {
                "status": "available",
                "version": result.stdout.split('\n')[0]
            }
        else:
            dependencies["dependencies"]["latex"] = {
                "status": "unavailable",
                "error": "pdflatex not found"
            }
    except Exception as e:
        dependencies["dependencies"]["latex"] = {
            "status": "unavailable",
            "error": str(e)
        }
    
    return dependencies

@router.get("/logs")
async def get_recent_logs(lines: int = 100):
    """Get recent log entries"""
    try:
        log_file = Path(settings.BASE_PATH) / "logs" / settings.LOG_FILE
        
        if not log_file.exists():
            return {"error": "Log file not found"}
        
        with open(log_file, 'r') as f:
            log_lines = f.readlines()
        
        # Get last N lines
        recent_logs = log_lines[-lines:] if len(log_lines) > lines else log_lines
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_lines": len(log_lines),
            "returned_lines": len(recent_logs),
            "logs": [line.strip() for line in recent_logs]
        }
        
    except Exception as e:
        logger.error(f"Error reading logs: {e}")
        return {"error": str(e)}

"""
Agent Coordinator
Orchestrates the multi-agent system for document generation
"""

import asyncio
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime

from .base_agent import BaseAgent, AgentState, AgentMessage
from .planning_agent import PlanningAgent
from .research_agent import ResearchAgent
from .writing_agent import WritingAgent
from .latex_agent import LaTeXAgent
from .visual_agent import VisualAgent
from .quality_agent import QualityAgent
from .humanizer_agent import HumanizerAgent
from .assembly_agent import AssemblyAgent
from core.logging_config import get_logger

logger = get_logger(__name__)

class AgentCoordinator:
    """Coordinates the multi-agent system for academic document generation"""
    
    def __init__(self):
        # Initialize all agents
        self.agents = {
            "planning": PlanningAgent(),
            "research": ResearchAgent(),
            "writing": WritingAgent(),
            "latex": LaTeXAgent(),
            "visual": VisualAgent(),
            "quality": QualityAgent(),
            "humanizer": HumanizerAgent(),
            "assembly": AssemblyAgent()
        }
        
        # Coordination state
        self.active_sessions: Dict[str, Dict] = {}
        self.message_bus: asyncio.Queue = asyncio.Queue()
        
        # Performance tracking
        self.coordination_metrics = {
            "sessions_completed": 0,
            "average_generation_time": 0.0,
            "success_rate": 1.0,
            "agent_utilization": {agent_id: 0 for agent_id in self.agents.keys()}
        }
        
        # Setup inter-agent communication
        self._setup_communication()
        
        logger.info("Agent Coordinator initialized with 8 specialized agents")
    
    def _setup_communication(self):
        """Setup inter-agent communication system"""
        for agent in self.agents.values():
            agent.subscribe_to_messages(self._handle_agent_message)
    
    async def _handle_agent_message(self, message: AgentMessage):
        """Handle messages between agents"""
        await self.message_bus.put(message)
        
        # Route message to recipient if specified
        if message.recipient in self.agents:
            await self.agents[message.recipient].receive_message(message)
    
    async def generate_document(
        self, 
        request: Dict[str, Any], 
        session_id: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Coordinate multi-agent document generation"""
        
        start_time = datetime.now()
        session_data = {
            "session_id": session_id,
            "start_time": start_time,
            "request": request,
            "progress": 0,
            "current_phase": "initialization",
            "agent_outputs": {},
            "errors": []
        }
        
        self.active_sessions[session_id] = session_data
        
        try:
            logger.info(f"Starting document generation session: {session_id}")
            
            # Phase 1: Planning (10%)
            await self._update_progress(session_id, "planning", 10, progress_callback)
            planning_result = await self._execute_planning_phase(request, session_id)
            session_data["agent_outputs"]["planning"] = planning_result
            
            # Phase 2: Research (25%)
            await self._update_progress(session_id, "research", 25, progress_callback)
            research_result = await self._execute_research_phase(
                planning_result, request, session_id
            )
            session_data["agent_outputs"]["research"] = research_result
            
            # Phase 3: Writing (50%)
            await self._update_progress(session_id, "writing", 50, progress_callback)
            writing_result = await self._execute_writing_phase(
                planning_result, research_result, request, session_id
            )
            session_data["agent_outputs"]["writing"] = writing_result
            
            # Phase 4: Visual Elements (60%)
            await self._update_progress(session_id, "visual", 60, progress_callback)
            visual_result = await self._execute_visual_phase(
                planning_result, writing_result, request, session_id
            )
            session_data["agent_outputs"]["visual"] = visual_result
            
            # Phase 5: LaTeX/Formatting (70%)
            await self._update_progress(session_id, "formatting", 70, progress_callback)
            latex_result = await self._execute_latex_phase(
                writing_result, visual_result, request, session_id
            )
            session_data["agent_outputs"]["latex"] = latex_result
            
            # Phase 6: Quality Check (80%)
            await self._update_progress(session_id, "quality", 80, progress_callback)
            quality_result = await self._execute_quality_phase(
                writing_result, request, session_id
            )
            session_data["agent_outputs"]["quality"] = quality_result
            
            # Phase 7: Humanization (90%)
            await self._update_progress(session_id, "humanization", 90, progress_callback)
            humanizer_result = await self._execute_humanization_phase(
                writing_result, quality_result, request, session_id
            )
            session_data["agent_outputs"]["humanizer"] = humanizer_result
            
            # Phase 8: Assembly (100%)
            await self._update_progress(session_id, "assembly", 100, progress_callback)
            assembly_result = await self._execute_assembly_phase(
                session_data["agent_outputs"], request, session_id
            )
            session_data["agent_outputs"]["assembly"] = assembly_result
            
            # Calculate total generation time
            total_time = (datetime.now() - start_time).total_seconds()
            
            # Update metrics
            self._update_coordination_metrics(True, total_time)
            
            # Prepare final result
            final_result = {
                "success": True,
                "session_id": session_id,
                "document": assembly_result.get("final_document", {}),
                "generation_time": total_time,
                "agent_outputs": session_data["agent_outputs"],
                "coordination_metadata": {
                    "agents_used": list(self.agents.keys()),
                    "total_phases": 8,
                    "session_start": start_time.isoformat(),
                    "session_end": datetime.now().isoformat()
                }
            }
            
            logger.info(f"Document generation completed: {session_id} ({total_time:.2f}s)")
            return final_result
            
        except Exception as e:
            # Handle errors
            error_time = (datetime.now() - start_time).total_seconds()
            self._update_coordination_metrics(False, error_time)
            
            session_data["errors"].append({
                "error": str(e),
                "phase": session_data["current_phase"],
                "timestamp": datetime.now().isoformat()
            })
            
            logger.error(f"Document generation failed: {session_id} - {e}")
            
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "generation_time": error_time,
                "partial_outputs": session_data["agent_outputs"],
                "errors": session_data["errors"]
            }
        
        finally:
            # Cleanup session
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
    
    async def _execute_planning_phase(self, request: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """Execute planning phase"""
        planning_task = {
            "type": "document_planning",
            "requirements": {
                "title": request.get("title", "Academic Document"),
                "document_type": request.get("document_type", "research_paper"),
                "writing_style": request.get("writing_style", "analytical"),
                "target_length": request.get("target_length", 5000),
                "citation_style": request.get("citation_style", "APA"),
                "field": request.get("field", "general")
            }
        }
        
        result = await self.agents["planning"].execute(planning_task)
        self._track_agent_usage("planning")
        return result
    
    async def _execute_research_phase(self, planning_result: Dict, request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute research phase"""
        research_task = {
            "type": "comprehensive_research",
            "topic": request.get("title", "Academic Research"),
            "research_plan": planning_result.get("plan", {}),
            "keywords": request.get("keywords", [])
        }
        
        result = await self.agents["research"].execute(research_task)
        self._track_agent_usage("research")
        return result
    
    async def _execute_writing_phase(self, planning_result: Dict, research_result: Dict, 
                                   request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute writing phase"""
        writing_task = {
            "type": "content_generation",
            "plan": planning_result.get("plan", {}),
            "writing_style": request.get("writing_style", "analytical"),
            "research_data": research_result.get("research_results", {}),
            "requirements": {
                "target_length": request.get("target_length", 5000),
                "citation_style": request.get("citation_style", "APA"),
                "academic_level": request.get("academic_level", "graduate"),
                "field": request.get("field", "general")
            }
        }
        
        result = await self.agents["writing"].execute(writing_task)
        self._track_agent_usage("writing")
        return result
    
    async def _execute_visual_phase(self, planning_result: Dict, writing_result: Dict,
                                  request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute visual elements phase"""
        visual_task = {
            "type": "comprehensive_visual",
            "document_plan": planning_result.get("plan", {}),
            "content_analysis": {
                "word_count": writing_result.get("word_count", 0),
                "document_type": request.get("document_type", "research_paper")
            },
            "visual_requirements": request.get("visual_requirements", {})
        }
        
        result = await self.agents["visual"].execute(visual_task)
        self._track_agent_usage("visual")
        return result
    
    async def _execute_latex_phase(self, writing_result: Dict, visual_result: Dict,
                                 request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute LaTeX formatting phase"""
        latex_task = {
            "type": "complete_latex_generation",
            "content": writing_result.get("content", ""),
            "requirements": {
                "document_type": request.get("document_type", "article"),
                "citation_style": request.get("citation_style", "APA"),
                "title": request.get("title", "Academic Document"),
                "author": request.get("author", "ASCAES Generated"),
                "include_math": request.get("include_math", False),
                "include_tables": bool(visual_result.get("visual_package", {}).get("tables", [])),
                "include_figures": bool(visual_result.get("visual_package", {}).get("charts", [])),
                "references": request.get("references", [])
            }
        }
        
        result = await self.agents["latex"].execute(latex_task)
        self._track_agent_usage("latex")
        return result
    
    async def _execute_quality_phase(self, writing_result: Dict, request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute quality checking phase"""
        quality_task = {
            "type": "comprehensive_quality_check",
            "content": writing_result.get("content", ""),
            "requirements": {
                "writing_style": request.get("writing_style", "analytical"),
                "document_type": request.get("document_type", "research_paper"),
                "citation_style": request.get("citation_style", "APA"),
                "references": request.get("references", []),
                "quality_criteria": {
                    "min_score": 0.8,
                    "academic_rigor": "high",
                    "clarity": "high",
                    "coherence": "high"
                }
            }
        }
        
        result = await self.agents["quality"].execute(quality_task)
        self._track_agent_usage("quality")
        return result
    
    async def _execute_humanization_phase(self, writing_result: Dict, quality_result: Dict,
                                        request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute humanization phase"""
        humanization_task = {
            "type": "comprehensive_humanization",
            "content": writing_result.get("content", ""),
            "requirements": {
                "target_variety": 0.8,
                "enhancement_level": "moderate",
                "pattern_sensitivity": "medium",
                "maintain_academic_tone": True
            }
        }
        
        result = await self.agents["humanizer"].execute(humanization_task)
        self._track_agent_usage("humanizer")
        return result
    
    async def _execute_assembly_phase(self, agent_outputs: Dict, request: Dict, session_id: str) -> Dict[str, Any]:
        """Execute final assembly phase"""
        assembly_task = {
            "type": "complete_assembly",
            "components": agent_outputs,
            "requirements": {
                "title": request.get("title", "Academic Document"),
                "document_type": request.get("document_type", "research_paper"),
                "writing_style": request.get("writing_style", "analytical"),
                "output_formats": request.get("output_formats", ["pdf", "latex", "txt"]),
                "quality_criteria": {
                    "min_score": 0.8,
                    "min_word_count": 500,
                    "required_sections": ["introduction", "conclusion"]
                }
            }
        }
        
        result = await self.agents["assembly"].execute(assembly_task)
        self._track_agent_usage("assembly")
        return result
    
    async def _update_progress(self, session_id: str, phase: str, progress: int, 
                             callback: Optional[Callable] = None):
        """Update session progress"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id]["current_phase"] = phase
            self.active_sessions[session_id]["progress"] = progress
        
        if callback:
            await callback({
                "session_id": session_id,
                "phase": phase,
                "progress": progress,
                "timestamp": datetime.now().isoformat()
            })
    
    def _track_agent_usage(self, agent_id: str):
        """Track agent usage for metrics"""
        if agent_id in self.coordination_metrics["agent_utilization"]:
            self.coordination_metrics["agent_utilization"][agent_id] += 1
    
    def _update_coordination_metrics(self, success: bool, generation_time: float):
        """Update coordination performance metrics"""
        self.coordination_metrics["sessions_completed"] += 1
        
        # Update average generation time
        current_avg = self.coordination_metrics["average_generation_time"]
        session_count = self.coordination_metrics["sessions_completed"]
        new_avg = ((current_avg * (session_count - 1)) + generation_time) / session_count
        self.coordination_metrics["average_generation_time"] = new_avg
        
        # Update success rate
        if success:
            current_rate = self.coordination_metrics["success_rate"]
            new_rate = ((current_rate * (session_count - 1)) + 1.0) / session_count
            self.coordination_metrics["success_rate"] = new_rate
        else:
            current_rate = self.coordination_metrics["success_rate"]
            new_rate = (current_rate * (session_count - 1)) / session_count
            self.coordination_metrics["success_rate"] = new_rate
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        agent_statuses = {}
        
        # Get status from each agent
        for agent_id, agent in self.agents.items():
            try:
                agent_statuses[agent_id] = agent.get_status()
            except Exception as e:
                agent_statuses[agent_id] = {
                    "error": str(e),
                    "healthy": False
                }
        
        return {
            "coordinator_status": "operational",
            "active_sessions": len(self.active_sessions),
            "agent_statuses": agent_statuses,
            "coordination_metrics": self.coordination_metrics,
            "message_queue_size": self.message_bus.qsize(),
            "timestamp": datetime.now().isoformat()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health_results = {}
        overall_healthy = True
        
        # Check each agent
        for agent_id, agent in self.agents.items():
            try:
                health_result = await agent.health_check()
                health_results[agent_id] = health_result
                if not health_result.get("healthy", False):
                    overall_healthy = False
            except Exception as e:
                health_results[agent_id] = {
                    "healthy": False,
                    "error": str(e)
                }
                overall_healthy = False
        
        return {
            "coordinator_healthy": overall_healthy,
            "agent_health": health_results,
            "system_metrics": self.coordination_metrics,
            "active_sessions": len(self.active_sessions),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status of specific session"""
        return self.active_sessions.get(session_id)
    
    def list_active_sessions(self) -> List[str]:
        """List all active session IDs"""
        return list(self.active_sessions.keys())
    
    async def cancel_session(self, session_id: str) -> bool:
        """Cancel an active session"""
        if session_id in self.active_sessions:
            # Mark session as cancelled
            self.active_sessions[session_id]["cancelled"] = True
            logger.info(f"Session cancelled: {session_id}")
            return True
        return False

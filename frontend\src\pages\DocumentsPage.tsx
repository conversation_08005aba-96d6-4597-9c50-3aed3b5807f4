import React, { useEffect, useState } from 'react'
import { FileText, Download, Trash2, Eye, Calendar, FileType, RefreshCw } from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { formatDistanceToNow } from 'date-fns'
import { apiService } from '../services/api'

export const DocumentsPage: React.FC = () => {
  const { documents, generatedDocuments, deleteDocument, addGeneratedDocument } = useAppStore()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch generated documents on component mount
  useEffect(() => {
    fetchGeneratedDocuments()
  }, [])

  const fetchGeneratedDocuments = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const docs = await apiService.getGeneratedDocuments()

      // Convert API response to frontend format
      docs.forEach(doc => {
        const existingDoc = generatedDocuments.find(d => d.id === doc.id.toString())
        if (!existingDoc) {
          addGeneratedDocument({
            id: doc.id.toString(),
            title: doc.title,
            documentType: doc.document_type,
            writingStyle: doc.writing_style,
            outputFormat: doc.output_format,
            conversationId: doc.conversation_id.toString(),
            createdAt: new Date(doc.created_at),
            updatedAt: new Date(doc.updated_at),
            wordCount: doc.word_count,
            pageCount: doc.page_count,
            qualityScore: doc.quality_score
          })
        }
      })
    } catch (err) {
      console.error('Error fetching generated documents:', err)
      setError('Failed to load generated documents')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async (documentId: string, title: string) => {
    try {
      const blob = await apiService.downloadDocument(parseInt(documentId))

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename based on document title and format
      const doc = generatedDocuments.find(d => d.id === documentId)
      const extension = doc?.outputFormat === 'latex' ? '.tex' :
                      doc?.outputFormat === 'rtf' ? '.rtf' :
                      doc?.outputFormat === 'pdf' ? '.pdf' : '.txt'

      const safeTitle = title.replace(/[^a-zA-Z0-9\s-_]/g, '').replace(/\s+/g, '_')
      link.download = `${safeTitle}${extension}`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up
      window.URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Error downloading document:', err)
      setError('Failed to download document')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case '.pdf':
        return '📄'
      case '.txt':
      case '.md':
        return '📝'
      case '.docx':
        return '📘'
      case '.tex':
        return '📊'
      default:
        return '📎'
    }
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
          <p className="text-gray-600 mt-1">
            Manage your uploaded files and generated academic documents
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Uploaded Documents */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Uploaded Files</h2>
                <span className="text-sm text-gray-500">{documents.length} files</span>
              </div>

              {documents.length > 0 ? (
                <div className="space-y-3">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="text-2xl">{getFileIcon(doc.fileType)}</div>
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {doc.originalFilename}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                          <span>{formatFileSize(doc.fileSize)}</span>
                          <span>{doc.fileType.toUpperCase()}</span>
                          <span>{formatDistanceToNow(doc.uploadDate, { addSuffix: true })}</span>
                          <span className={`px-2 py-1 rounded-full ${
                            doc.processed 
                              ? 'bg-success-100 text-success-700' 
                              : 'bg-warning-100 text-warning-700'
                          }`}>
                            {doc.processed ? 'Processed' : 'Processing'}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button className="p-2 rounded-lg hover:bg-gray-200 transition-colors">
                          <Eye className="w-4 h-4 text-gray-500" />
                        </button>
                        <button className="p-2 rounded-lg hover:bg-gray-200 transition-colors">
                          <Download className="w-4 h-4 text-gray-500" />
                        </button>
                        <button 
                          onClick={() => deleteDocument(doc.id)}
                          className="p-2 rounded-lg hover:bg-red-100 transition-colors"
                        >
                          <Trash2 className="w-4 h-4 text-red-500" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No uploaded files</h3>
                  <p className="text-gray-600">
                    Upload documents to analyze and reference in your academic writing
                  </p>
                </div>
              )}
            </div>

            {/* Generated Documents */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Generated Documents</h2>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={fetchGeneratedDocuments}
                    disabled={isLoading}
                    className="flex items-center px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200 transition-colors disabled:opacity-50"
                    title="Refresh documents"
                  >
                    <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </button>
                  <span className="text-sm text-gray-500">{generatedDocuments.length} documents</span>
                </div>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              {generatedDocuments.length > 0 ? (
                <div className="space-y-3">
                  {generatedDocuments.map((doc) => (
                    <div key={doc.id} className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {doc.title}
                          </h3>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            <span className="capitalize">{doc.writingStyle}</span>
                            <span className="uppercase">{doc.outputFormat}</span>
                            <span className="capitalize">{doc.documentType}</span>
                          </div>
                          {doc.wordCount && (
                            <p className="text-xs text-gray-500 mt-1">
                              {doc.wordCount.toLocaleString()} words
                              {doc.pageCount && ` • ${doc.pageCount} pages`}
                              {doc.qualityScore && ` • Quality: ${Math.round(doc.qualityScore * 100)}%`}
                            </p>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 ml-4">
                          <button
                            onClick={() => handleDownload(doc.id, doc.title)}
                            className="p-2 rounded-lg hover:bg-green-100 hover:text-green-600 transition-colors"
                            title="Download document"
                          >
                            <Download className="w-4 h-4 text-gray-500" />
                          </button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDistanceToNow(doc.createdAt, { addSuffix: true })}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <FileType className="w-3 h-3" />
                          <span>{doc.outputFormat.toUpperCase()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No generated documents</h3>
                  <p className="text-gray-600">
                    Start a chat conversation to generate your first academic document
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Statistics */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-primary-600">{documents.length}</div>
              <div className="text-sm text-gray-600">Uploaded Files</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-success-600">{generatedDocuments.length}</div>
              <div className="text-sm text-gray-600">Generated Docs</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-warning-600">
                {generatedDocuments.reduce((sum, doc) => sum + (doc.wordCount || 0), 0).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Total Words</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-error-600">
                {documents.reduce((sum, doc) => sum + doc.fileSize, 0) > 0 
                  ? formatFileSize(documents.reduce((sum, doc) => sum + doc.fileSize, 0))
                  : '0 MB'
                }
              </div>
              <div className="text-sm text-gray-600">Storage Used</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

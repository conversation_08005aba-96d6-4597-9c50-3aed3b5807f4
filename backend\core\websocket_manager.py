"""
ASCAES WebSocket Manager
Handles real-time communication between frontend and backend
"""

from fastapi import WebSocket
from typing import Dict, List
import json
import asyncio
from datetime import datetime
import uuid

from core.logging_config import get_logger
from services.chat_service import ChatService
from agents.agent_coordinator import AgentCoordinator
from services.chat_handler import chat_handler

logger = get_logger(__name__)

class WebSocketManager:
    """Manages WebSocket connections and message routing"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.chat_service = ChatService()
        self.agent_coordinator = AgentCoordinator()
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")

        # Don't send welcome message to avoid cluttering chat
        # Connection status is handled by frontend UI indicators
    
    async def disconnect(self, client_id: str):
        """Remove WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")
    
    async def disconnect_all(self):
        """Disconnect all clients"""
        for client_id in list(self.active_connections.keys()):
            await self.disconnect(client_id)
    
    async def send_message(self, client_id: str, message: dict):
        """Send message to specific client"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                await self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """Broadcast message to all connected clients"""
        for client_id in list(self.active_connections.keys()):
            await self.send_message(client_id, message)
    
    async def handle_message(self, client_id: str, data: dict):
        """Handle incoming WebSocket message"""
        try:
            message_type = data.get("type")
            
            if message_type == "chat":
                await self._handle_chat_message(client_id, data)
            elif message_type == "document_upload":
                await self._handle_document_upload(client_id, data)
            elif message_type == "generate_document":
                await self._handle_document_generation(client_id, data)
            elif message_type == "ping":
                await self.send_message(client_id, {"type": "pong"})
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {e}")
            await self.send_message(client_id, {
                "type": "error",
                "message": "An error occurred processing your request",
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_chat_message(self, client_id: str, data: dict):
        """Handle chat message"""
        message = data.get("message", "")
        conversation_id = data.get("conversation_id")

        if not message.strip():
            return

        # Send typing indicator
        await self.send_message(client_id, {
            "type": "typing",
            "timestamp": datetime.now().isoformat()
        })

        try:
            # First try our new chat handler for document generation
            response = await chat_handler.process_message(
                message=message,
                session_id=client_id
            )

            response_type = response.get('type', 'general')

            if response_type in ['large_document_request', 'document_request']:
                # Handle document generation request
                await self.send_message(client_id, {
                    "type": "chat_response",
                    "message": response.get('message', ''),
                    "conversation_id": conversation_id,
                    "message_id": str(uuid.uuid4()),
                    "timestamp": datetime.now().isoformat(),
                    "metadata": {
                        "type": response_type,
                        "session_id": response.get('session_id'),
                        "details": response.get('details'),
                        "action": response.get('action')
                    }
                })

                # Auto-start generation for demo purposes
                if response_type == 'large_document_request':
                    await self._start_large_document_generation(client_id, response)
                elif response_type == 'document_request':
                    await self._start_regular_document_generation(client_id, response)

            else:
                # Regular chat response
                await self.send_message(client_id, {
                    "type": "chat_response",
                    "message": response.get('message', ''),
                    "conversation_id": conversation_id,
                    "message_id": str(uuid.uuid4()),
                    "timestamp": datetime.now().isoformat(),
                    "metadata": response.get('details', {})
                })

        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            await self.send_message(client_id, {
                "type": "error",
                "message": "Sorry, I encountered an error processing your message.",
                "timestamp": datetime.now().isoformat()
            })
        finally:
            # Stop typing indicator
            await self.send_message(client_id, {
                "type": "typing_stop",
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_document_upload(self, client_id: str, data: dict):
        """Handle document upload notification"""
        document_id = data.get("document_id")
        filename = data.get("filename")
        
        await self.send_message(client_id, {
            "type": "document_processing",
            "document_id": document_id,
            "filename": filename,
            "status": "processing",
            "timestamp": datetime.now().isoformat()
        })
        
        try:
            # Process document
            result = await self.chat_service.process_document(document_id)
            
            await self.send_message(client_id, {
                "type": "document_processed",
                "document_id": document_id,
                "filename": filename,
                "status": "completed",
                "summary": result.get("summary", ""),
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            await self.send_message(client_id, {
                "type": "document_error",
                "document_id": document_id,
                "filename": filename,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_document_generation(self, client_id: str, data: dict):
        """Handle document generation request"""
        generation_id = str(uuid.uuid4())
        
        await self.send_message(client_id, {
            "type": "generation_started",
            "generation_id": generation_id,
            "timestamp": datetime.now().isoformat()
        })
        
        try:
            # Start document generation through agent coordinator
            result = await self.agent_coordinator.generate_document(
                request=data,
                client_id=client_id,
                progress_callback=lambda progress: asyncio.create_task(
                    self.send_message(client_id, {
                        "type": "generation_progress",
                        "generation_id": generation_id,
                        "progress": progress,
                        "timestamp": datetime.now().isoformat()
                    })
                )
            )
            
            await self.send_message(client_id, {
                "type": "generation_completed",
                "generation_id": generation_id,
                "document_id": result["document_id"],
                "download_url": result["download_url"],
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error generating document: {e}")
            await self.send_message(client_id, {
                "type": "generation_error",
                "generation_id": generation_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    async def _start_large_document_generation(self, client_id: str, response: dict):
        """Start large document generation and provide progress updates"""
        try:
            from services.large_document_service import LargeDocumentService
            from api.agents import active_generations

            large_doc_service = LargeDocumentService()
            session_id = response.get('session_id', client_id)
            request = response.get('request', {})

            # Send generation started message
            await self.send_message(client_id, {
                "type": "generation_started",
                "session_id": session_id,
                "document_type": "large_document",
                "target_pages": request.get('target_pages', 100),
                "estimated_time": large_doc_service.get_estimated_time(request.get('target_pages', 100)),
                "timestamp": datetime.now().isoformat()
            })

            # Progress callback to send updates via WebSocket
            async def progress_callback(progress_data):
                await self.send_message(client_id, {
                    "type": "generation_progress",
                    "session_id": session_id,
                    "progress": progress_data.get("progress", 0),
                    "phase": progress_data.get("phase", "unknown"),
                    "message": progress_data.get("message", ""),
                    "timestamp": datetime.now().isoformat()
                })

            # Start generation in background
            asyncio.create_task(self._run_large_document_generation(
                client_id, session_id, request, progress_callback
            ))

        except Exception as e:
            logger.error(f"Error starting large document generation: {e}")
            await self.send_message(client_id, {
                "type": "generation_error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    async def _start_regular_document_generation(self, client_id: str, response: dict):
        """Start regular document generation"""
        try:
            session_id = response.get('session_id', client_id)
            request = response.get('request', {})

            await self.send_message(client_id, {
                "type": "generation_started",
                "session_id": session_id,
                "document_type": "regular_document",
                "target_pages": request.get('target_pages', 8),
                "timestamp": datetime.now().isoformat()
            })

            # Start generation in background
            asyncio.create_task(self._run_regular_document_generation(
                client_id, session_id, request
            ))

        except Exception as e:
            logger.error(f"Error starting regular document generation: {e}")
            await self.send_message(client_id, {
                "type": "generation_error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    async def _run_large_document_generation(self, client_id: str, session_id: str,
                                           request: dict, progress_callback):
        """Run large document generation in background"""
        try:
            from services.large_document_service import LargeDocumentService

            large_doc_service = LargeDocumentService()

            # Run generation
            result = await large_doc_service.generate_large_document(
                request=request,
                session_id=session_id,
                progress_callback=progress_callback
            )

            # Send completion message
            await self.send_message(client_id, {
                "type": "generation_completed",
                "session_id": session_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Large document generation failed: {e}")
            await self.send_message(client_id, {
                "type": "generation_error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

    async def _run_regular_document_generation(self, client_id: str, session_id: str, request: dict):
        """Run regular document generation in background"""
        try:
            # Use agent coordinator for regular documents
            result = await self.agent_coordinator.generate_document(
                request=request,
                session_id=session_id
            )

            await self.send_message(client_id, {
                "type": "generation_completed",
                "session_id": session_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Regular document generation failed: {e}")
            await self.send_message(client_id, {
                "type": "generation_error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

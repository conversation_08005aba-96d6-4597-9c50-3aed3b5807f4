"""
LaTeX Agent
Responsible for LaTeX formatting, mathematical notation, and academic document structure
"""

import re
from typing import Dict, Any, List
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class LaTeXAgent(BaseAgent):
    """Agent specialized in LaTeX formatting and mathematical notation"""
    
    def __init__(self):
        super().__init__(
            agent_id="latex_agent",
            name="LaTeX Agent",
            description="Handles LaTeX formatting, mathematical notation, and academic document structure"
        )
        
        # LaTeX-specific configuration
        self.config.update({
            "document_classes": {
                "article": "\\documentclass[12pt,a4paper]{article}",
                "report": "\\documentclass[12pt,a4paper]{report}",
                "book": "\\documentclass[12pt,a4paper]{book}",
                "thesis": "\\documentclass[12pt,a4paper]{report}",
                "ieee": "\\documentclass[conference]{IEEEtran}",
                "acm": "\\documentclass{acmart}"
            },
            "packages": {
                "essential": [
                    "\\usepackage[utf8]{inputenc}",
                    "\\usepackage[T1]{fontenc}",
                    "\\usepackage{amsmath}",
                    "\\usepackage{amsfonts}",
                    "\\usepackage{amssymb}",
                    "\\usepackage{graphicx}",
                    "\\usepackage[margin=1in]{geometry}",
                    "\\usepackage{setspace}",
                    "\\usepackage{cite}"
                ],
                "advanced": [
                    "\\usepackage{algorithm}",
                    "\\usepackage{algorithmic}",
                    "\\usepackage{listings}",
                    "\\usepackage{tikz}",
                    "\\usepackage{pgfplots}",
                    "\\usepackage{booktabs}",
                    "\\usepackage{multirow}",
                    "\\usepackage{subcaption}"
                ]
            },
            "citation_styles": {
                "APA": "\\usepackage[style=apa]{biblatex}",
                "IEEE": "\\usepackage[style=ieee]{biblatex}",
                "Chicago": "\\usepackage[style=chicago-authordate]{biblatex}",
                "MLA": "\\usepackage[style=mla]{biblatex}"
            },
            "math_environments": [
                "equation", "align", "gather", "multline", "split",
                "cases", "matrix", "pmatrix", "bmatrix", "vmatrix"
            ]
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define LaTeX agent capabilities"""
        return [
            AgentCapability(
                name="document_formatting",
                description="Format complete documents in LaTeX",
                input_types=["plain_text", "markdown", "structured_content"],
                output_types=["latex_document", "formatted_output"]
            ),
            AgentCapability(
                name="mathematical_notation",
                description="Convert mathematical expressions to LaTeX",
                input_types=["math_expressions", "equations", "formulas"],
                output_types=["latex_math", "formatted_equations"]
            ),
            AgentCapability(
                name="table_formatting",
                description="Create and format tables in LaTeX",
                input_types=["table_data", "csv_data", "structured_data"],
                output_types=["latex_tables", "formatted_tables"]
            ),
            AgentCapability(
                name="bibliography_management",
                description="Format citations and bibliographies",
                input_types=["citation_data", "reference_list"],
                output_types=["latex_bibliography", "formatted_citations"]
            ),
            AgentCapability(
                name="figure_integration",
                description="Integrate figures and graphics",
                input_types=["figure_specifications", "image_data"],
                output_types=["latex_figures", "formatted_graphics"]
            )
        ]
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute LaTeX formatting task"""
        task_type = task.get("type", "document_formatting")
        
        if task_type == "document_formatting":
            return await self._format_document(task)
        elif task_type == "mathematical_notation":
            return await self._format_mathematics(task)
        elif task_type == "table_formatting":
            return await self._format_tables(task)
        elif task_type == "bibliography_management":
            return await self._format_bibliography(task)
        elif task_type == "figure_integration":
            return await self._format_figures(task)
        elif task_type == "complete_latex_generation":
            return await self._generate_complete_latex(task)
        else:
            raise ValueError(f"Unknown LaTeX task type: {task_type}")
    
    async def _format_document(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Format complete document in LaTeX"""
        content = task.get("content", "")
        document_type = task.get("document_type", "article")
        citation_style = task.get("citation_style", "APA")
        requirements = task.get("requirements", {})
        
        # Generate LaTeX document structure
        latex_document = await self._create_latex_structure(
            content, document_type, citation_style, requirements
        )
        
        # Validate LaTeX syntax
        validation_result = self._validate_latex_syntax(latex_document)
        
        return {
            "success": True,
            "latex_content": latex_document,
            "document_type": document_type,
            "citation_style": citation_style,
            "validation": validation_result,
            "formatting_timestamp": datetime.now().isoformat()
        }
    
    async def _create_latex_structure(self, content: str, document_type: str, 
                                    citation_style: str, requirements: Dict[str, Any]) -> str:
        """Create complete LaTeX document structure"""
        
        # Document class
        doc_class = self.config["document_classes"].get(document_type, 
                   self.config["document_classes"]["article"])
        
        # Packages
        essential_packages = "\n".join(self.config["packages"]["essential"])
        
        # Advanced packages if needed
        advanced_packages = ""
        if requirements.get("include_algorithms", False):
            advanced_packages += "\n".join([
                "\\usepackage{algorithm}",
                "\\usepackage{algorithmic}"
            ])
        
        if requirements.get("include_code", False):
            advanced_packages += "\n\\usepackage{listings}"
        
        if requirements.get("include_graphics", False):
            advanced_packages += "\n\\usepackage{tikz}\n\\usepackage{pgfplots}"
        
        # Citation style
        citation_package = self.config["citation_styles"].get(citation_style, 
                          "\\usepackage[style=apa]{biblatex}")
        
        # Document metadata
        title = requirements.get("title", "Academic Document")
        author = requirements.get("author", "Author Name")
        date = requirements.get("date", "\\today")
        
        # Build LaTeX document
        latex_prompt = f"""Convert the following academic content to a complete LaTeX document:

Content:
{content}

Requirements:
- Document type: {document_type}
- Citation style: {citation_style}
- Title: {title}
- Author: {author}

Create a complete LaTeX document with:
1. Proper document class and packages
2. Title page and metadata
3. Well-structured sections and subsections
4. Proper formatting for academic content
5. Placeholder citations where appropriate
6. Mathematical notation in proper LaTeX format
7. Professional academic formatting

Ensure the LaTeX code is syntactically correct and follows academic standards."""

        system_prompt = """You are an expert LaTeX formatter specializing in academic documents. 
Generate clean, well-structured LaTeX code that follows academic publishing standards. 
Ensure proper use of LaTeX commands, environments, and formatting conventions."""

        latex_content = await self._call_llm(latex_prompt, system_prompt)
        
        # Post-process to ensure proper structure
        formatted_latex = self._post_process_latex(latex_content, doc_class, 
                                                 essential_packages, citation_package, 
                                                 title, author, date)
        
        return formatted_latex
    
    def _post_process_latex(self, latex_content: str, doc_class: str, 
                           packages: str, citation_package: str,
                           title: str, author: str, date: str) -> str:
        """Post-process LaTeX content to ensure proper structure"""
        
        # Ensure document has proper structure
        if "\\documentclass" not in latex_content:
            # Build complete document
            complete_latex = f"""{doc_class}

{packages}
{citation_package}

\\title{{{title}}}
\\author{{{author}}}
\\date{{{date}}}

\\begin{{document}}

\\maketitle

{latex_content}

\\end{{document}}"""
        else:
            complete_latex = latex_content
        
        # Clean up common issues
        complete_latex = self._fix_common_latex_issues(complete_latex)
        
        return complete_latex
    
    def _fix_common_latex_issues(self, latex_content: str) -> str:
        """Fix common LaTeX formatting issues"""
        
        # Fix multiple spaces
        latex_content = re.sub(r' +', ' ', latex_content)
        
        # Fix section formatting
        latex_content = re.sub(r'(?m)^# (.+)$', r'\\section{\1}', latex_content)
        latex_content = re.sub(r'(?m)^## (.+)$', r'\\subsection{\1}', latex_content)
        latex_content = re.sub(r'(?m)^### (.+)$', r'\\subsubsection{\1}', latex_content)
        
        # Fix emphasis
        latex_content = re.sub(r'\*\*(.+?)\*\*', r'\\textbf{\1}', latex_content)
        latex_content = re.sub(r'\*(.+?)\*', r'\\textit{\1}', latex_content)
        
        # Fix quotes
        latex_content = re.sub(r'"(.+?)"', r'``\1\'\'', latex_content)
        
        # Fix common math expressions
        latex_content = re.sub(r'\$([^$]+)\$', r'$\1$', latex_content)
        
        return latex_content
    
    async def _format_mathematics(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Format mathematical expressions in LaTeX"""
        expressions = task.get("expressions", [])
        context = task.get("context", "")
        
        formatted_expressions = []
        
        for expr in expressions:
            math_prompt = f"""Convert the following mathematical expression to proper LaTeX notation:

Expression: {expr}
Context: {context}

Provide the LaTeX code for this mathematical expression, choosing the appropriate environment 
(inline math, equation, align, etc.) based on the complexity and context."""

            latex_math = await self._call_llm(math_prompt)
            
            formatted_expressions.append({
                "original": expr,
                "latex": latex_math,
                "environment": self._determine_math_environment(expr)
            })
        
        return {
            "success": True,
            "formatted_expressions": formatted_expressions,
            "total_expressions": len(expressions)
        }
    
    async def _format_tables(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Format tables in LaTeX"""
        table_data = task.get("table_data", [])
        table_style = task.get("style", "booktabs")
        caption = task.get("caption", "")
        
        table_prompt = f"""Create a LaTeX table from the following data:

Data: {table_data}
Style: {table_style}
Caption: {caption}

Generate a well-formatted LaTeX table using the {table_style} style. 
Include proper column alignment, headers, and formatting."""

        latex_table = await self._call_llm(table_prompt)
        
        return {
            "success": True,
            "latex_table": latex_table,
            "style": table_style,
            "caption": caption
        }
    
    async def _format_bibliography(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Format bibliography and citations"""
        references = task.get("references", [])
        citation_style = task.get("citation_style", "APA")
        
        bib_entries = []
        
        for ref in references:
            bib_prompt = f"""Convert the following reference to BibTeX format for {citation_style} style:

Reference: {ref}

Generate a proper BibTeX entry with all necessary fields."""

            bibtex_entry = await self._call_llm(bib_prompt)
            bib_entries.append(bibtex_entry)
        
        # Create complete bibliography
        bibliography = "\n\n".join(bib_entries)
        
        return {
            "success": True,
            "bibliography": bibliography,
            "citation_style": citation_style,
            "total_references": len(references)
        }
    
    async def _format_figures(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Format figures and graphics"""
        figures = task.get("figures", [])
        
        formatted_figures = []
        
        for fig in figures:
            default_width = '0.8\\textwidth'
            figure_prompt = f"""Create LaTeX code for including the following figure:

Figure description: {fig.get('description', '')}
File path: {fig.get('path', 'figure.png')}
Caption: {fig.get('caption', '')}
Label: {fig.get('label', 'fig:example')}
Width: {fig.get('width', default_width)}

Generate proper LaTeX figure environment with includegraphics."""

            latex_figure = await self._call_llm(figure_prompt)
            
            formatted_figures.append({
                "original": fig,
                "latex": latex_figure
            })
        
        return {
            "success": True,
            "formatted_figures": formatted_figures,
            "total_figures": len(figures)
        }
    
    async def _generate_complete_latex(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete LaTeX document with all components"""
        document_plan = task.get("document_plan", {})
        content = task.get("content", "")
        requirements = task.get("requirements", {})
        
        # Format main content
        main_formatting = await self._format_document({
            "content": content,
            "document_type": requirements.get("document_type", "article"),
            "citation_style": requirements.get("citation_style", "APA"),
            "requirements": requirements
        })
        
        # Add mathematical expressions if present
        if requirements.get("include_math", False):
            math_formatting = await self._format_mathematics({
                "expressions": requirements.get("math_expressions", []),
                "context": "academic document"
            })
            main_formatting["math_content"] = math_formatting
        
        # Add tables if present
        if requirements.get("include_tables", False):
            table_formatting = await self._format_tables({
                "table_data": requirements.get("table_data", []),
                "style": "booktabs"
            })
            main_formatting["table_content"] = table_formatting
        
        # Add bibliography
        if requirements.get("references", []):
            bib_formatting = await self._format_bibliography({
                "references": requirements["references"],
                "citation_style": requirements.get("citation_style", "APA")
            })
            main_formatting["bibliography"] = bib_formatting
        
        return {
            "success": True,
            "complete_latex": main_formatting,
            "components": {
                "main_document": True,
                "mathematics": requirements.get("include_math", False),
                "tables": requirements.get("include_tables", False),
                "bibliography": bool(requirements.get("references", [])),
                "figures": requirements.get("include_figures", False)
            }
        }
    
    def _validate_latex_syntax(self, latex_content: str) -> Dict[str, Any]:
        """Validate LaTeX syntax"""
        issues = []
        
        # Check for basic structure
        if "\\documentclass" not in latex_content:
            issues.append("Missing document class declaration")
        
        if "\\begin{document}" not in latex_content:
            issues.append("Missing document begin")
        
        if "\\end{document}" not in latex_content:
            issues.append("Missing document end")
        
        # Check for balanced braces
        open_braces = latex_content.count('{')
        close_braces = latex_content.count('}')
        if open_braces != close_braces:
            issues.append(f"Unbalanced braces: {open_braces} open, {close_braces} close")
        
        # Check for balanced math environments
        math_begins = len(re.findall(r'\\begin\{(equation|align|gather|multline)\}', latex_content))
        math_ends = len(re.findall(r'\\end\{(equation|align|gather|multline)\}', latex_content))
        if math_begins != math_ends:
            issues.append(f"Unbalanced math environments: {math_begins} begin, {math_ends} end")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": self._check_latex_warnings(latex_content)
        }
    
    def _check_latex_warnings(self, latex_content: str) -> List[str]:
        """Check for potential LaTeX warnings"""
        warnings = []
        
        # Check for common issues
        if "\\usepackage{graphicx}" not in latex_content and "\\includegraphics" in latex_content:
            warnings.append("Using includegraphics without graphicx package")
        
        if "\\usepackage{amsmath}" not in latex_content and any(env in latex_content for env in self.config["math_environments"]):
            warnings.append("Using math environments without amsmath package")
        
        return warnings
    
    def _determine_math_environment(self, expression: str) -> str:
        """Determine appropriate math environment for expression"""
        if len(expression) < 20 and '=' not in expression:
            return "inline"
        elif '&' in expression or '\\\\' in expression:
            return "align"
        elif len(expression.split('\n')) > 1:
            return "gather"
        else:
            return "equation"

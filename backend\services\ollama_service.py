"""
ASCAES Ollama Service
Handles communication with Ollama for local LLM inference
"""

import httpx
import asyncio
import json
import time
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime

from core.config import settings
from core.logging_config import get_logger
from core.database import SessionLocal, ModelUsage

logger = get_logger(__name__)

class OllamaService:
    """Service for interacting with Ollama API"""
    
    def __init__(self):
        self.base_url = settings.OLLAMA_HOST
        self.timeout = settings.OLLAMA_TIMEOUT
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def list_models(self) -> Dict[str, Any]:
        """Get list of available models"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            raise
    
    async def pull_model(self, model_name: str, stream: bool = True) -> Dict[str, Any]:
        """Pull/download a model"""
        try:
            payload = {"name": model_name, "stream": stream}
            
            if stream:
                return await self._stream_pull(model_name, payload)
            else:
                response = await self.client.post(
                    f"{self.base_url}/api/pull",
                    json=payload,
                    timeout=3600  # Extended timeout for model downloads
                )
                response.raise_for_status()
                return response.json()
                
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
            raise
    
    async def _stream_pull(self, model_name: str, payload: Dict) -> Dict[str, Any]:
        """Handle streaming model pull"""
        try:
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/pull",
                json=payload,
                timeout=3600
            ) as response:
                response.raise_for_status()
                
                progress_info = {}
                async for line in response.aiter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            progress_info.update(data)
                            
                            # Log progress
                            if "status" in data:
                                logger.info(f"Pulling {model_name}: {data['status']}")
                            
                        except json.JSONDecodeError:
                            continue
                
                return progress_info
                
        except Exception as e:
            logger.error(f"Error streaming model pull for {model_name}: {e}")
            raise
    
    async def delete_model(self, model_name: str) -> Dict[str, Any]:
        """Delete a model"""
        try:
            payload = {"name": model_name}
            response = await self.client.delete(
                f"{self.base_url}/api/delete",
                json=payload
            )
            response.raise_for_status()
            return {"success": True}
            
        except Exception as e:
            logger.error(f"Error deleting model {model_name}: {e}")
            raise
    
    async def generate(
        self,
        model: str,
        prompt: str,
        system: Optional[str] = None,
        template: Optional[str] = None,
        context: Optional[List[int]] = None,
        stream: bool = False,
        raw: bool = False,
        format: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate text using a model"""
        start_time = time.time()
        
        try:
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": stream,
                "raw": raw
            }
            
            if system:
                payload["system"] = system
            if template:
                payload["template"] = template
            if context:
                payload["context"] = context
            if format:
                payload["format"] = format
            if options:
                payload["options"] = options
            else:
                # Default options optimized for 8GB RAM
                payload["options"] = {
                    "temperature": settings.TEMPERATURE,
                    "top_p": settings.TOP_P,
                    "num_ctx": 8192,  # Increased default context
                    "num_predict": 4096,  # Increased default prediction tokens
                    "repeat_penalty": 1.1
                }
            
            if stream:
                return await self._stream_generate(model, payload, start_time)
            else:
                # Add timeout for generation
                timeout = 300  # 5 minutes timeout
                response = await self.client.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=timeout
                )
                response.raise_for_status()
                result = response.json()
                
                # Log usage
                await self._log_usage(
                    model=model,
                    task_type="generate",
                    response_time=time.time() - start_time,
                    success=True,
                    tokens_used=result.get("eval_count", 0)
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error generating with model {model}: {e}")
            
            # Log failed usage
            await self._log_usage(
                model=model,
                task_type="generate",
                response_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
            
            raise
    
    async def _stream_generate(self, model: str, payload: Dict, start_time: float) -> AsyncGenerator[Dict, None]:
        """Handle streaming generation"""
        try:
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                response.raise_for_status()
                
                total_tokens = 0
                async for line in response.aiter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if "response" in data:
                                total_tokens += 1
                            yield data
                            
                            if data.get("done", False):
                                # Log usage for completed stream
                                await self._log_usage(
                                    model=model,
                                    task_type="generate_stream",
                                    response_time=time.time() - start_time,
                                    success=True,
                                    tokens_used=data.get("eval_count", total_tokens)
                                )
                                
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Error streaming generation with model {model}: {e}")
            
            # Log failed usage
            await self._log_usage(
                model=model,
                task_type="generate_stream",
                response_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
            
            raise
    
    async def chat(
        self,
        model: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        format: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Chat with a model using conversation format"""
        start_time = time.time()
        
        try:
            payload = {
                "model": model,
                "messages": messages,
                "stream": stream
            }
            
            if format:
                payload["format"] = format
            if options:
                payload["options"] = options
            else:
                # Default options optimized for 8GB RAM
                payload["options"] = {
                    "temperature": settings.TEMPERATURE,
                    "top_p": settings.TOP_P,
                    "num_ctx": min(settings.MAX_TOKENS, 4096),
                    "num_predict": min(settings.MAX_TOKENS, 2048),
                    "repeat_penalty": 1.1
                }
            
            response = await self.client.post(
                f"{self.base_url}/api/chat",
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            
            # Log usage
            await self._log_usage(
                model=model,
                task_type="chat",
                response_time=time.time() - start_time,
                success=True,
                tokens_used=result.get("eval_count", 0)
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error chatting with model {model}: {e}")
            
            # Log failed usage
            await self._log_usage(
                model=model,
                task_type="chat",
                response_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
            
            raise
    
    async def embeddings(self, model: str, prompt: str) -> Dict[str, Any]:
        """Generate embeddings using a model"""
        start_time = time.time()
        
        try:
            payload = {
                "model": model,
                "prompt": prompt
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/embeddings",
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            
            # Log usage
            await self._log_usage(
                model=model,
                task_type="embeddings",
                response_time=time.time() - start_time,
                success=True
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating embeddings with model {model}: {e}")
            
            # Log failed usage
            await self._log_usage(
                model=model,
                task_type="embeddings",
                response_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )
            
            raise
    
    async def test_model(self, model_name: str) -> Dict[str, Any]:
        """Test if a model is working correctly"""
        try:
            test_prompt = "Hello, please respond with 'Model is working correctly.'"
            
            result = await self.generate(
                model=model_name,
                prompt=test_prompt,
                options={"num_predict": 50}
            )
            
            return {
                "success": True,
                "model": model_name,
                "response": result.get("response", ""),
                "response_time": result.get("total_duration", 0) / 1e9  # Convert to seconds
            }
            
        except Exception as e:
            return {
                "success": False,
                "model": model_name,
                "error": str(e)
            }
    
    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get Ollama memory usage information"""
        try:
            # This is a placeholder - Ollama doesn't provide direct memory usage API
            # In a real implementation, you might use system monitoring
            import psutil
            
            # Find Ollama process
            ollama_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                if 'ollama' in proc.info['name'].lower():
                    ollama_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': proc.info['memory_info'].rss / (1024 * 1024)
                    })
            
            return {
                "processes": ollama_processes,
                "total_memory_mb": sum(p['memory_mb'] for p in ollama_processes)
            }
            
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {"error": str(e)}
    
    async def _log_usage(
        self,
        model: str,
        task_type: str,
        response_time: float,
        success: bool,
        tokens_used: Optional[int] = None,
        error_message: Optional[str] = None
    ):
        """Log model usage to database"""
        try:
            db = SessionLocal()
            
            usage_record = ModelUsage(
                model_name=model,
                task_type=task_type,
                tokens_used=tokens_used,
                response_time=response_time,
                success=success,
                error_message=error_message
            )
            
            db.add(usage_record)
            db.commit()
            db.close()
            
        except Exception as e:
            logger.error(f"Error logging model usage: {e}")

# Global service instance
ollama_service = OllamaService()

import React from 'react'
import { 
  Setting<PERSON>, 
  Palette, 
  <PERSON>, 
  Shield, 
  Database,
  Brain,
  FileText,
  Download
} from 'lucide-react'
import { useAppStore } from '../store/appStore'

export const SettingsPage: React.FC = () => {
  const { 
    theme, 
    setTheme, 
    settings, 
    updateSettings,
    currentModel,
    setCurrentModel 
  } = useAppStore()

  const writingStyles = [
    { value: 'analytical', label: 'Analytical', description: 'Critical examination and data interpretation' },
    { value: 'instructional', label: 'Instructional', description: 'Teaching and step-by-step guidance' },
    { value: 'reporting', label: 'Reporting', description: 'Factual documentation and findings' },
    { value: 'argumentative', label: 'Argumentative', description: 'Position papers and debates' },
    { value: 'exploratory', label: 'Exploratory', description: 'Investigation and hypothesis development' },
    { value: 'descriptive', label: 'Descriptive', description: 'Technical specifications and details' },
    { value: 'narrative', label: 'Narrative', description: 'Case studies and story-driven explanations' },
    { value: 'schematic', label: 'Schematic', description: 'Reference materials and systematic documentation' }
  ]

  const outputFormats = [
    { value: 'pdf', label: 'PDF', description: 'Portable Document Format' },
    { value: 'latex', label: 'LaTeX', description: 'LaTeX source code' },
    { value: 'rtf', label: 'RTF', description: 'Rich Text Format' },
    { value: 'txt', label: 'TXT', description: 'Plain text' }
  ]

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">
            Configure ASCAES preferences and behavior
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          
          {/* Appearance */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Palette className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900">Appearance</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Theme
                </label>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setTheme('light')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      theme === 'light'
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Light
                  </button>
                  <button
                    onClick={() => setTheme('dark')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      theme === 'dark'
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Dark
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Document Generation */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <FileText className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900">Document Generation</h2>
            </div>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Writing Style
                </label>
                <select
                  value={settings.writingStyle}
                  onChange={(e) => updateSettings({ writingStyle: e.target.value })}
                  className="input-primary"
                >
                  {writingStyles.map((style) => (
                    <option key={style.value} value={style.value}>
                      {style.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {writingStyles.find(s => s.value === settings.writingStyle)?.description}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Output Format
                </label>
                <select
                  value={settings.outputFormat}
                  onChange={(e) => updateSettings({ outputFormat: e.target.value })}
                  className="input-primary"
                >
                  {outputFormats.map((format) => (
                    <option key={format.value} value={format.value}>
                      {format.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {outputFormats.find(f => f.value === settings.outputFormat)?.description}
                </p>
              </div>
            </div>
          </div>

          {/* AI Model Settings */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Brain className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900">AI Model Settings</h2>
            </div>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Tokens: {settings.maxTokens}
                </label>
                <input
                  type="range"
                  min="1000"
                  max="8000"
                  step="500"
                  value={settings.maxTokens}
                  onChange={(e) => updateSettings({ maxTokens: parseInt(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1K (Shorter)</span>
                  <span>8K (Longer)</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Temperature: {settings.temperature}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.temperature}
                  onChange={(e) => updateSettings({ temperature: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.0 (Conservative)</span>
                  <span>1.0 (Creative)</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Top P: {settings.topP}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={settings.topP}
                  onChange={(e) => updateSettings({ topP: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.1 (Focused)</span>
                  <span>1.0 (Diverse)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Bell className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900">Notifications</h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Enable Notifications
                  </label>
                  <p className="text-xs text-gray-500">
                    Get notified about document generation progress
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => updateSettings({ notifications: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Auto Save
                  </label>
                  <p className="text-xs text-gray-500">
                    Automatically save generated documents
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.autoSave}
                  onChange={(e) => updateSettings({ autoSave: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="w-5 h-5 text-primary-600" />
              <h2 className="text-lg font-semibold text-gray-900">Data & Privacy</h2>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-success-600" />
                  <span className="text-sm font-medium text-success-800">
                    All processing is done locally
                  </span>
                </div>
                <p className="text-xs text-success-700 mt-1">
                  Your documents and data never leave your computer. ASCAES operates completely offline.
                </p>
              </div>

              <div className="space-y-3">
                <button className="btn-secondary w-full justify-center">
                  <Database className="w-4 h-4 mr-2" />
                  Clear All Data
                </button>
                <button className="btn-secondary w-full justify-center">
                  <Download className="w-4 h-4 mr-2" />
                  Export Settings
                </button>
              </div>
            </div>
          </div>

          {/* About */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">About ASCAES</h2>
            <div className="space-y-2 text-sm text-gray-600">
              <p><strong>Version:</strong> 1.0.0</p>
              <p><strong>Build:</strong> Production</p>
              <p><strong>Current Model:</strong> {currentModel}</p>
              <p><strong>License:</strong> MIT</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

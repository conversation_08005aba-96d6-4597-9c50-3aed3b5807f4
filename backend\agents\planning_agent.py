"""
Planning Agent
Responsible for document structure planning and organization
"""

import json
from typing import Dict, Any, List
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class PlanningAgent(BaseAgent):
    """Agent specialized in document planning and structure creation"""
    
    def __init__(self):
        super().__init__(
            agent_id="planning_agent",
            name="Planning Agent",
            description="Creates document structure, outlines, and planning strategies for academic documents"
        )
        
        # Planning-specific configuration
        self.config.update({
            "planning_depth": "detailed",  # basic, detailed, comprehensive
            "structure_templates": self._load_structure_templates(),
            "citation_styles": ["APA", "MLA", "Chicago", "IEEE", "Harvard"],
            "document_types": [
                "research_paper", "thesis", "dissertation", "report", 
                "review", "proposal", "case_study", "technical_doc"
            ]
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define planning agent capabilities"""
        return [
            AgentCapability(
                name="document_structure_planning",
                description="Create detailed document structure and outline",
                input_types=["document_requirements", "user_specifications"],
                output_types=["document_plan", "structure_outline"]
            ),
            AgentCapability(
                name="section_planning",
                description="Plan individual sections with content guidelines",
                input_types=["document_plan", "section_requirements"],
                output_types=["section_plans", "content_guidelines"]
            ),
            AgentCapability(
                name="research_planning",
                description="Identify research requirements and sources needed",
                input_types=["document_plan", "topic_analysis"],
                output_types=["research_plan", "source_requirements"]
            ),
            AgentCapability(
                name="timeline_planning",
                description="Create realistic timeline for document generation",
                input_types=["document_plan", "complexity_analysis"],
                output_types=["timeline", "milestone_plan"]
            )
        ]
    
    def _load_structure_templates(self) -> Dict[str, Dict]:
        """Load document structure templates"""
        return {
            "research_paper": {
                "sections": [
                    {"name": "Abstract", "word_count": "150-250", "required": True},
                    {"name": "Introduction", "word_count": "500-800", "required": True},
                    {"name": "Literature Review", "word_count": "1000-1500", "required": True},
                    {"name": "Methodology", "word_count": "800-1200", "required": True},
                    {"name": "Results", "word_count": "1000-1500", "required": True},
                    {"name": "Discussion", "word_count": "800-1200", "required": True},
                    {"name": "Conclusion", "word_count": "300-500", "required": True},
                    {"name": "References", "word_count": "N/A", "required": True}
                ],
                "total_word_range": "4500-7000",
                "citation_requirements": "15-30 sources"
            },
            "thesis": {
                "sections": [
                    {"name": "Abstract", "word_count": "300-500", "required": True},
                    {"name": "Introduction", "word_count": "2000-3000", "required": True},
                    {"name": "Literature Review", "word_count": "5000-8000", "required": True},
                    {"name": "Methodology", "word_count": "3000-5000", "required": True},
                    {"name": "Results", "word_count": "5000-8000", "required": True},
                    {"name": "Discussion", "word_count": "3000-5000", "required": True},
                    {"name": "Conclusion", "word_count": "1000-2000", "required": True},
                    {"name": "References", "word_count": "N/A", "required": True}
                ],
                "total_word_range": "20000-35000",
                "citation_requirements": "50-100 sources"
            },
            "report": {
                "sections": [
                    {"name": "Executive Summary", "word_count": "200-400", "required": True},
                    {"name": "Introduction", "word_count": "300-500", "required": True},
                    {"name": "Background", "word_count": "500-800", "required": False},
                    {"name": "Analysis", "word_count": "1500-2500", "required": True},
                    {"name": "Findings", "word_count": "1000-1500", "required": True},
                    {"name": "Recommendations", "word_count": "500-800", "required": True},
                    {"name": "Conclusion", "word_count": "200-400", "required": True}
                ],
                "total_word_range": "3000-6000",
                "citation_requirements": "10-25 sources"
            }
        }
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute planning task"""
        task_type = task.get("type", "document_planning")
        
        if task_type == "document_planning":
            return await self._create_document_plan(task)
        elif task_type == "section_planning":
            return await self._create_section_plans(task)
        elif task_type == "research_planning":
            return await self._create_research_plan(task)
        elif task_type == "timeline_planning":
            return await self._create_timeline_plan(task)
        else:
            raise ValueError(f"Unknown planning task type: {task_type}")
    
    async def _create_document_plan(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive document plan"""
        requirements = task.get("requirements", {})
        
        # Extract key requirements
        document_type = requirements.get("document_type", "research_paper")
        title = requirements.get("title", "Untitled Document")
        writing_style = requirements.get("writing_style", "analytical")
        target_length = requirements.get("target_length", 5000)
        citation_style = requirements.get("citation_style", "APA")
        
        # Get structure template
        template = self.config["structure_templates"].get(document_type, 
                   self.config["structure_templates"]["research_paper"])
        
        # Create planning prompt
        planning_prompt = f"""Create a detailed document plan for the following academic document:

Title: {title}
Document Type: {document_type}
Writing Style: {writing_style}
Target Length: {target_length} words
Citation Style: {citation_style}

Based on the document type, create a comprehensive plan that includes:

1. Document Structure:
   - Main sections and subsections
   - Word count allocation per section
   - Content focus for each section

2. Research Requirements:
   - Types of sources needed
   - Estimated number of citations
   - Key topics to research

3. Writing Guidelines:
   - Tone and style specifications
   - Technical depth requirements
   - Audience considerations

4. Quality Standards:
   - Academic rigor requirements
   - Citation and formatting standards
   - Review criteria

Please provide a detailed, structured plan in JSON format."""

        system_prompt = f"""You are an expert academic planning assistant specializing in {document_type} planning. 
Create detailed, realistic plans that ensure high-quality academic output. Consider the specific requirements 
of {writing_style} writing style and {citation_style} citation format."""

        # Generate plan using LLM
        plan_response = await self._call_llm(planning_prompt, system_prompt)
        
        # Parse and structure the plan
        try:
            # Try to extract JSON from response
            plan_data = self._extract_json_from_response(plan_response)
        except:
            # Fallback to structured parsing
            plan_data = self._create_fallback_plan(requirements, template)
        
        # Enhance plan with template data
        enhanced_plan = self._enhance_plan_with_template(plan_data, template, requirements)
        
        return {
            "success": True,
            "plan": enhanced_plan,
            "metadata": {
                "document_type": document_type,
                "planning_timestamp": datetime.now().isoformat(),
                "template_used": document_type,
                "estimated_completion_time": self._estimate_completion_time(enhanced_plan)
            }
        }
    
    async def _create_section_plans(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed plans for individual sections"""
        document_plan = task.get("document_plan", {})
        sections = document_plan.get("structure", {}).get("sections", [])
        
        section_plans = []
        
        for section in sections:
            section_prompt = f"""Create a detailed content plan for the following document section:

Section: {section.get('name', 'Unknown')}
Word Count: {section.get('word_count', 'Not specified')}
Document Type: {document_plan.get('document_type', 'academic')}
Writing Style: {document_plan.get('writing_style', 'analytical')}

Provide:
1. Content outline with key points
2. Subsection breakdown
3. Research requirements for this section
4. Writing guidelines and tone
5. Integration points with other sections

Format as detailed structured content."""

            section_plan = await self._call_llm(section_prompt)
            
            section_plans.append({
                "section_name": section.get("name"),
                "plan": section_plan,
                "word_count": section.get("word_count"),
                "priority": section.get("priority", "medium")
            })
        
        return {
            "success": True,
            "section_plans": section_plans,
            "total_sections": len(section_plans)
        }
    
    async def _create_research_plan(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create research strategy and requirements"""
        document_plan = task.get("document_plan", {})
        
        research_prompt = f"""Create a comprehensive research plan for this academic document:

Document: {document_plan.get('title', 'Academic Document')}
Type: {document_plan.get('document_type', 'research_paper')}
Field: {document_plan.get('field', 'general')}

Provide:
1. Research strategy and methodology
2. Types of sources needed (primary, secondary, etc.)
3. Recommended databases and search terms
4. Citation requirements and standards
5. Quality criteria for source selection
6. Timeline for research activities

Focus on creating a systematic approach to gathering high-quality academic sources."""

        research_plan = await self._call_llm(research_prompt)
        
        return {
            "success": True,
            "research_plan": research_plan,
            "estimated_sources": document_plan.get("citation_requirements", "15-25"),
            "research_timeline": "2-3 days for comprehensive research"
        }
    
    async def _create_timeline_plan(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create realistic timeline for document completion"""
        document_plan = task.get("document_plan", {})
        complexity = task.get("complexity", "medium")
        
        # Calculate timeline based on document length and complexity
        target_length = document_plan.get("target_length", 5000)
        base_time = target_length / 500  # 500 words per hour baseline
        
        complexity_multipliers = {"low": 1.0, "medium": 1.5, "high": 2.0}
        total_hours = base_time * complexity_multipliers.get(complexity, 1.5)
        
        timeline = {
            "total_estimated_hours": round(total_hours, 1),
            "phases": [
                {"phase": "Planning", "hours": round(total_hours * 0.15, 1)},
                {"phase": "Research", "hours": round(total_hours * 0.25, 1)},
                {"phase": "Writing", "hours": round(total_hours * 0.45, 1)},
                {"phase": "Review & Revision", "hours": round(total_hours * 0.15, 1)}
            ],
            "milestones": [
                "Document plan completion",
                "Research phase completion", 
                "First draft completion",
                "Final document ready"
            ]
        }
        
        return {
            "success": True,
            "timeline": timeline,
            "complexity_level": complexity
        }
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """Extract JSON data from LLM response"""
        # Try to find JSON in the response
        import re
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        else:
            raise ValueError("No JSON found in response")
    
    def _create_fallback_plan(self, requirements: Dict[str, Any], template: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback plan when LLM parsing fails"""
        return {
            "structure": {
                "sections": template["sections"],
                "total_word_count": requirements.get("target_length", 5000)
            },
            "research_requirements": {
                "citation_count": template.get("citation_requirements", "15-25"),
                "source_types": ["academic journals", "books", "conference papers"]
            },
            "quality_standards": {
                "academic_rigor": "high",
                "citation_style": requirements.get("citation_style", "APA")
            }
        }
    
    def _enhance_plan_with_template(self, plan_data: Dict[str, Any], template: Dict[str, Any], requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance plan with template and requirement data"""
        enhanced_plan = plan_data.copy()
        
        # Add template structure if missing
        if "structure" not in enhanced_plan:
            enhanced_plan["structure"] = {"sections": template["sections"]}
        
        # Add metadata
        enhanced_plan["metadata"] = {
            "document_type": requirements.get("document_type"),
            "writing_style": requirements.get("writing_style"),
            "target_length": requirements.get("target_length"),
            "citation_style": requirements.get("citation_style"),
            "template_version": "1.0"
        }
        
        return enhanced_plan
    
    def _estimate_completion_time(self, plan: Dict[str, Any]) -> str:
        """Estimate completion time based on plan complexity"""
        target_length = plan.get("metadata", {}).get("target_length", 5000)
        
        if target_length < 2000:
            return "2-4 hours"
        elif target_length < 5000:
            return "4-8 hours"
        elif target_length < 10000:
            return "8-16 hours"
        else:
            return "16+ hours"

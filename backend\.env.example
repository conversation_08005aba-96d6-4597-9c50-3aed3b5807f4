# ASCAES Environment Configuration
# Copy this file to .env and modify values as needed

# Application Settings
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# CORS Settings (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000

# Database
DATABASE_URL=sqlite:///./ascaes.db

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_TIMEOUT=300

# Model Configuration (8GB RAM Optimized)
DEFAULT_MODEL=qwen2:7b-instruct
CODING_MODEL=deepseek-coder:6.7b
REASONING_MODEL=phi3:3.8b

# Model Generation Settings
MAX_TOKENS=4096
TEMPERATURE=0.7
TOP_P=0.9

# Vector Database
EMBEDDING_MODEL=all-MiniLM-L6-v2
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Document Processing
MAX_FILE_SIZE=52428800  # 50MB in bytes
OCR_LANGUAGE=eng

# LaTeX Configuration
LATEX_TIMEOUT=60
LATEX_COMPILER=pdflatex

# Security
SECRET_KEY=your-secret-key-change-in-production-please
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging
LOG_LEVEL=INFO
LOG_FILE=ascaes.log

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# Agent Configuration
MAX_AGENT_ITERATIONS=10
AGENT_TIMEOUT=600

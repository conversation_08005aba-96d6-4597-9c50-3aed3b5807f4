# ASCAES Installation Guide

## Prerequisites

Before installing ASCAES, ensure you have the following software installed:

### Required Software
- **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
- **Python** (v3.11 or higher) - [Download](https://python.org/downloads/)
- **Ollama** (v0.9 or higher) - [Download](https://ollama.ai/download)
- **Git** - [Download](https://git-scm.com/downloads)

### System Requirements
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 20GB free space for models and documents
- **OS**: Windows 10+, macOS 10.15+, or Linux

## Installation Steps

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/ascaes.git
cd ascaes
```

### 2. Install Dependencies
```bash
# Install all dependencies (frontend + backend)
npm run install-all
```

This command will:
- Install Node.js dependencies for the frontend
- Install Python dependencies for the backend
- Set up the development environment

### 3. Configure Environment
```bash
# Copy environment template
cp backend/.env.example backend/.env

# Edit configuration (optional)
# nano backend/.env
```

### 4. Set Up AI Models
```bash
# Download and configure recommended models
npm run setup-models
```

This will download:
- `qwen2:7b-instruct` (4.4GB) - General purpose model
- `phi3:3.8b` (2.2GB) - Efficient reasoning model
- `deepseek-coder:6.7b` (3.8GB) - Code generation model

**Note**: Model download may take 10-30 minutes depending on your internet connection.

### 5. Start the Application

#### Development Mode
```bash
# Start both frontend and backend
npm run dev
```

This will start:
- Frontend development server on http://localhost:3000
- Backend API server on http://localhost:8000

#### Production Mode
```bash
# Build and start production servers
npm run build
npm run start
```

## Verification

### 1. Check Application Status
Open your browser and navigate to:
- **Frontend**: http://localhost:3000
- **API Documentation**: http://localhost:8000/api/docs

### 2. Test Model Connection
1. Go to the Models page in the application
2. Verify that models are listed and marked as "Ready"
3. Try generating a simple document to test functionality

### 3. Health Check
Visit http://localhost:8000/api/health/detailed for a comprehensive system status.

## Troubleshooting

### Common Issues

#### Ollama Connection Failed
```bash
# Check if Ollama is running
ollama --version

# Start Ollama service (if not running)
ollama serve
```

#### Port Already in Use
```bash
# Change ports in package.json or .env file
# Frontend: modify vite.config.ts
# Backend: modify .env PORT variable
```

#### Model Download Failed
```bash
# Manually download models
ollama pull qwen2:7b-instruct
ollama pull phi3:3.8b
ollama pull deepseek-coder:6.7b
```

#### Python Dependencies Error
```bash
# Create virtual environment
cd backend
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### Node.js Dependencies Error
```bash
# Clear npm cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### Performance Optimization

#### For 8GB RAM Systems
- Use only the recommended lightweight models
- Close other memory-intensive applications
- Monitor system resources during operation

#### For 16GB+ RAM Systems
- Consider upgrading to larger models for better performance
- Enable concurrent processing in settings
- Increase model context length limits

## Next Steps

After successful installation:

1. **Read the User Guide**: [docs/user-guide.md](user-guide.md)
2. **Explore API Documentation**: http://localhost:8000/api/docs
3. **Configure Writing Styles**: Customize the 8 academic writing styles
4. **Set Up Templates**: Create LaTeX and document templates
5. **Import Documents**: Upload reference materials for analysis

## Support

If you encounter issues:

1. Check the [Troubleshooting](#troubleshooting) section above
2. Review application logs in `backend/logs/`
3. Visit the project repository for issues and discussions
4. Check system requirements and compatibility

## Uninstallation

To completely remove ASCAES:

```bash
# Stop all services
npm run stop

# Remove application files
cd ..
rm -rf ascaes

# Remove Ollama models (optional)
ollama rm qwen2:7b-instruct
ollama rm phi3:3.8b
ollama rm deepseek-coder:6.7b
```

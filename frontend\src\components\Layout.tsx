import React from 'react'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { DocumentProgress } from './DocumentProgress'
import { useAppStore } from '../store/appStore'
import { toast } from 'react-hot-toast'

interface LayoutProps {
  children: React.ReactNode
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { sidebarCollapsed, documentProgress, setDocumentProgress } = useAppStore()

  const stopDocumentGeneration = async (sessionId: string) => {
    try {
      const response = await fetch(`http://localhost:8001/api/agents/stop/${sessionId}`, {
        method: 'POST'
      })

      if (response.ok) {
        setDocumentProgress({
          isGenerating: false,
          progress: 0,
          message: 'Document generation stopped',
          sessionId
        })
        toast.success('Document generation stopped')
      } else {
        toast.error('Failed to stop document generation')
      }
    } catch (error) {
      console.error('Error stopping generation:', error)
      toast.error('Failed to stop document generation')
    }
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ease-in-out`}>
        <Sidebar />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />

        {/* Global Progress Indicator */}
        {documentProgress.isGenerating && (
          <div className="border-b border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
            <div className="max-w-7xl mx-auto px-4 py-2">
              <DocumentProgress
                progress={documentProgress.progress}
                status="processing"
                message={documentProgress.message}
                estimatedTime="7.6 minutes"
                sessionId={documentProgress.sessionId}
                onStop={stopDocumentGeneration}
              />
            </div>
          </div>
        )}

        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  )
}

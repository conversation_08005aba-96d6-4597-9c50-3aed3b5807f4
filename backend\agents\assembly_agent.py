"""
Assembly Agent
Responsible for final document assembly, formatting, and output generation
"""

import json
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path

from .base_agent import BaseAgent, AgentCapability

class AssemblyAgent(BaseAgent):
    """Agent specialized in final document assembly and output generation"""
    
    def __init__(self):
        super().__init__(
            agent_id="assembly_agent",
            name="Assembly Agent",
            description="Assembles final documents from all agent outputs and generates multiple format outputs"
        )
        
        # Assembly-specific configuration
        self.config.update({
            "output_formats": {
                "pdf": {
                    "engine": "weasyprint",  # or "reportlab"
                    "template": "academic_paper.html",
                    "styling": "academic.css"
                },
                "latex": {
                    "document_class": "article",
                    "packages": ["amsmath", "graphicx", "cite"],
                    "compiler": "pdflatex"
                },
                "rtf": {
                    "encoding": "utf-8",
                    "template": "academic_rtf.template"
                },
                "txt": {
                    "encoding": "utf-8",
                    "line_width": 80,
                    "preserve_structure": True
                }
            },
            "document_templates": self._load_document_templates(),
            "quality_standards": {
                "min_word_count": 500,
                "max_word_count": 50000,
                "required_sections": ["introduction", "conclusion"],
                "citation_requirements": True
            }
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define assembly agent capabilities"""
        return [
            AgentCapability(
                name="document_assembly",
                description="Assemble complete document from component parts",
                input_types=["content_sections", "metadata", "formatting_requirements"],
                output_types=["assembled_document", "document_metadata"]
            ),
            AgentCapability(
                name="format_generation",
                description="Generate documents in multiple output formats",
                input_types=["assembled_document", "format_specifications"],
                output_types=["formatted_documents", "format_metadata"]
            ),
            AgentCapability(
                name="quality_validation",
                description="Validate final document quality and completeness",
                input_types=["complete_document", "quality_criteria"],
                output_types=["validation_report", "quality_score"]
            ),
            AgentCapability(
                name="metadata_generation",
                description="Generate comprehensive document metadata",
                input_types=["document_content", "generation_history"],
                output_types=["document_metadata", "generation_report"]
            ),
            AgentCapability(
                name="final_packaging",
                description="Package final document with all components",
                input_types=["formatted_documents", "metadata", "assets"],
                output_types=["document_package", "delivery_manifest"]
            )
        ]
    
    def _load_document_templates(self) -> Dict[str, str]:
        """Load document templates for different formats"""
        return {
            "academic_paper": {
                "structure": [
                    "title_page",
                    "abstract", 
                    "introduction",
                    "literature_review",
                    "methodology",
                    "results",
                    "discussion",
                    "conclusion",
                    "references"
                ],
                "formatting": "academic_standard"
            },
            "thesis": {
                "structure": [
                    "title_page",
                    "abstract",
                    "acknowledgments",
                    "table_of_contents",
                    "introduction",
                    "literature_review",
                    "methodology",
                    "results",
                    "discussion",
                    "conclusion",
                    "references",
                    "appendices"
                ],
                "formatting": "thesis_standard"
            },
            "report": {
                "structure": [
                    "executive_summary",
                    "introduction",
                    "background",
                    "analysis",
                    "findings",
                    "recommendations",
                    "conclusion"
                ],
                "formatting": "report_standard"
            }
        }
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute assembly task"""
        task_type = task.get("type", "document_assembly")
        
        if task_type == "document_assembly":
            return await self._assemble_document(task)
        elif task_type == "format_generation":
            return await self._generate_formats(task)
        elif task_type == "quality_validation":
            return await self._validate_quality(task)
        elif task_type == "metadata_generation":
            return await self._generate_metadata(task)
        elif task_type == "final_packaging":
            return await self._package_final_document(task)
        elif task_type == "complete_assembly":
            return await self._complete_assembly(task)
        else:
            raise ValueError(f"Unknown assembly task type: {task_type}")
    
    async def _assemble_document(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Assemble complete document from component parts"""
        components = task.get("components", {})
        document_plan = task.get("document_plan", {})
        requirements = task.get("requirements", {})
        
        # Extract components
        planning_output = components.get("planning", {})
        research_output = components.get("research", {})
        writing_output = components.get("writing", {})
        latex_output = components.get("latex", {})
        visual_output = components.get("visual", {})
        quality_output = components.get("quality", {})
        humanizer_output = components.get("humanizer", {})
        
        # Assemble main content
        main_content = self._assemble_main_content(
            writing_output, humanizer_output, quality_output
        )
        
        # Integrate visual elements
        content_with_visuals = self._integrate_visual_elements(
            main_content, visual_output
        )
        
        # Apply formatting
        formatted_content = self._apply_formatting(
            content_with_visuals, latex_output, requirements
        )
        
        # Add metadata and structure
        complete_document = self._add_document_structure(
            formatted_content, planning_output, research_output, requirements
        )
        
        # Generate assembly report
        assembly_report = self._generate_assembly_report(components, complete_document)
        
        return {
            "success": True,
            "assembled_document": complete_document,
            "assembly_report": assembly_report,
            "word_count": len(complete_document.get("content", "").split()),
            "components_used": list(components.keys()),
            "assembly_timestamp": datetime.now().isoformat()
        }
    
    async def _generate_formats(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate documents in multiple output formats"""
        document = task.get("document", {})
        requested_formats = task.get("formats", ["pdf", "latex", "rtf", "txt"])
        
        generated_formats = {}
        format_metadata = {}
        
        for format_type in requested_formats:
            try:
                if format_type == "pdf":
                    result = await self._generate_pdf(document)
                elif format_type == "latex":
                    result = await self._generate_latex(document)
                elif format_type == "rtf":
                    result = await self._generate_rtf(document)
                elif format_type == "txt":
                    result = await self._generate_txt(document)
                else:
                    continue
                
                generated_formats[format_type] = result["content"]
                format_metadata[format_type] = result["metadata"]
                
            except Exception as e:
                self.logger.error(f"Error generating {format_type} format: {e}")
                generated_formats[format_type] = None
                format_metadata[format_type] = {"error": str(e)}
        
        return {
            "success": True,
            "generated_formats": generated_formats,
            "format_metadata": format_metadata,
            "formats_generated": len([f for f in generated_formats.values() if f is not None])
        }
    
    async def _validate_quality(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Validate final document quality and completeness"""
        document = task.get("document", {})
        quality_criteria = task.get("quality_criteria", {})
        
        validation_results = {}
        
        # Content validation
        content_validation = self._validate_content(document, quality_criteria)
        validation_results["content"] = content_validation
        
        # Structure validation
        structure_validation = self._validate_structure(document, quality_criteria)
        validation_results["structure"] = structure_validation
        
        # Format validation
        format_validation = self._validate_formatting(document, quality_criteria)
        validation_results["formatting"] = format_validation
        
        # Citation validation
        citation_validation = self._validate_citations(document, quality_criteria)
        validation_results["citations"] = citation_validation
        
        # Calculate overall quality score
        overall_score = self._calculate_overall_quality_score(validation_results)
        
        return {
            "success": True,
            "validation_results": validation_results,
            "overall_quality_score": overall_score,
            "quality_level": self._determine_quality_level(overall_score),
            "validation_passed": overall_score >= quality_criteria.get("min_score", 0.8)
        }
    
    async def _generate_metadata(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive document metadata"""
        document = task.get("document", {})
        generation_history = task.get("generation_history", {})
        
        # Basic document metadata
        basic_metadata = {
            "title": document.get("title", "Untitled Document"),
            "document_type": document.get("document_type", "academic_paper"),
            "writing_style": document.get("writing_style", "analytical"),
            "word_count": len(document.get("content", "").split()),
            "page_count": self._estimate_page_count(document.get("content", "")),
            "creation_date": datetime.now().isoformat(),
            "language": "en",
            "academic_level": "graduate"
        }
        
        # Generation metadata
        generation_metadata = {
            "agents_used": list(generation_history.keys()),
            "generation_time": self._calculate_total_generation_time(generation_history),
            "model_used": "qwen2:7b-instruct",
            "quality_score": document.get("quality_score", 0.85),
            "humanization_score": document.get("humanization_score", 0.8)
        }
        
        # Content analysis metadata
        content_metadata = await self._analyze_content_metadata(document.get("content", ""))
        
        # Technical metadata
        technical_metadata = {
            "format_version": "1.0",
            "generator": "ASCAES v1.0.0",
            "encoding": "utf-8",
            "checksum": self._calculate_content_checksum(document.get("content", ""))
        }
        
        complete_metadata = {
            **basic_metadata,
            **generation_metadata,
            **content_metadata,
            **technical_metadata
        }
        
        return {
            "success": True,
            "metadata": complete_metadata,
            "metadata_categories": ["basic", "generation", "content", "technical"]
        }
    
    async def _complete_assembly(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Perform complete document assembly process"""
        components = task.get("components", {})
        requirements = task.get("requirements", {})
        
        # Step 1: Assemble document
        assembly_result = await self._assemble_document({
            "components": components,
            "requirements": requirements
        })
        
        if not assembly_result["success"]:
            return assembly_result
        
        document = assembly_result["assembled_document"]
        
        # Step 2: Validate quality
        validation_result = await self._validate_quality({
            "document": document,
            "quality_criteria": requirements.get("quality_criteria", {})
        })
        
        # Step 3: Generate metadata
        metadata_result = await self._generate_metadata({
            "document": document,
            "generation_history": components
        })
        
        # Step 4: Generate formats
        format_result = await self._generate_formats({
            "document": document,
            "formats": requirements.get("output_formats", ["pdf", "latex", "txt"])
        })
        
        # Step 5: Package final document
        package_result = await self._package_final_document({
            "document": document,
            "formats": format_result["generated_formats"],
            "metadata": metadata_result["metadata"],
            "validation": validation_result
        })
        
        return {
            "success": True,
            "final_document": package_result,
            "assembly_summary": {
                "document_assembled": assembly_result["success"],
                "quality_validated": validation_result["validation_passed"],
                "metadata_generated": metadata_result["success"],
                "formats_generated": format_result["formats_generated"],
                "package_created": package_result["success"]
            }
        }
    
    def _assemble_main_content(self, writing_output: Dict, humanizer_output: Dict, 
                             quality_output: Dict) -> str:
        """Assemble main content from writing and humanization outputs"""
        
        # Use humanized content if available, otherwise use original writing
        if humanizer_output and "humanized_content" in humanizer_output:
            main_content = humanizer_output["humanized_content"]
        elif writing_output and "content" in writing_output:
            main_content = writing_output["content"]
        else:
            main_content = "Error: No content available for assembly"
        
        return main_content
    
    def _integrate_visual_elements(self, content: str, visual_output: Dict) -> str:
        """Integrate visual elements into content"""
        if not visual_output or "visual_package" not in visual_output:
            return content
        
        visual_package = visual_output["visual_package"]
        
        # Add tables
        if "tables" in visual_package:
            for i, table in enumerate(visual_package["tables"]):
                table_marker = f"[TABLE_{i+1}]"
                if table_marker not in content:
                    # Insert table at appropriate location
                    content += f"\n\n{table_marker}\n{table.get('table', {}).get('markdown', '')}\n"
        
        # Add charts
        if "charts" in visual_package:
            for i, chart in enumerate(visual_package["charts"]):
                chart_marker = f"[FIGURE_{i+1}]"
                if chart_marker not in content:
                    content += f"\n\n{chart_marker}\n[Chart: {chart.get('title', f'Chart {i+1}')}]\n"
        
        return content
    
    def _apply_formatting(self, content: str, latex_output: Dict, requirements: Dict) -> str:
        """Apply formatting based on output requirements"""
        output_format = requirements.get("output_format", "txt")
        
        if output_format == "latex" and latex_output and "latex_content" in latex_output:
            return latex_output["latex_content"]
        
        # Apply basic formatting for other formats
        formatted_content = content
        
        # Add basic structure markers
        if "# " not in formatted_content:
            # Add section headers if not present
            formatted_content = self._add_basic_structure(formatted_content)
        
        return formatted_content
    
    def _add_document_structure(self, content: str, planning_output: Dict, 
                              research_output: Dict, requirements: Dict) -> Dict[str, Any]:
        """Add document structure and metadata"""
        
        document = {
            "title": requirements.get("title", "Academic Document"),
            "content": content,
            "document_type": requirements.get("document_type", "research_paper"),
            "writing_style": requirements.get("writing_style", "analytical"),
            "output_format": requirements.get("output_format", "txt"),
            "sections": self._extract_sections(content),
            "word_count": len(content.split()),
            "character_count": len(content)
        }
        
        # Add planning information
        if planning_output and "plan" in planning_output:
            document["planning_data"] = planning_output["plan"]
        
        # Add research information
        if research_output and "research_results" in research_output:
            document["research_data"] = research_output["research_results"]
        
        return document
    
    async def _generate_pdf(self, document: Dict) -> Dict[str, Any]:
        """Generate PDF format"""
        # Simplified PDF generation - in production would use proper PDF library
        pdf_content = f"""PDF Document: {document.get('title', 'Untitled')}

{document.get('content', '')}

Generated by ASCAES v1.0.0"""
        
        return {
            "content": pdf_content,
            "metadata": {
                "format": "pdf",
                "size": len(pdf_content),
                "pages": self._estimate_page_count(document.get("content", "")),
                "generated_at": datetime.now().isoformat()
            }
        }
    
    async def _generate_latex(self, document: Dict) -> Dict[str, Any]:
        """Generate LaTeX format"""
        title = document.get("title", "Academic Document")
        content = document.get("content", "")
        
        latex_content = f"""\\documentclass[12pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage{{amsmath}}
\\usepackage{{amsfonts}}
\\usepackage{{amssymb}}
\\usepackage{{graphicx}}
\\usepackage[margin=1in]{{geometry}}

\\title{{{title}}}
\\author{{ASCAES Generated}}
\\date{{\\today}}

\\begin{{document}}

\\maketitle

{content}

\\end{{document}}"""
        
        return {
            "content": latex_content,
            "metadata": {
                "format": "latex",
                "size": len(latex_content),
                "compiler": "pdflatex",
                "generated_at": datetime.now().isoformat()
            }
        }
    
    async def _generate_rtf(self, document: Dict) -> Dict[str, Any]:
        """Generate RTF format"""
        title = document.get("title", "Academic Document")
        content = document.get("content", "")
        
        # Replace newlines with RTF paragraph breaks
        rtf_content_body = content.replace(chr(10), '\\par ')

        rtf_content = f"""{{\\rtf1\\ansi\\deff0 {{\\fonttbl {{\\f0 Times New Roman;}}}}
\\f0\\fs24 \\b {title}\\b0\\par
\\par
{rtf_content_body}
}}"""
        
        return {
            "content": rtf_content,
            "metadata": {
                "format": "rtf",
                "size": len(rtf_content),
                "encoding": "utf-8",
                "generated_at": datetime.now().isoformat()
            }
        }
    
    async def _generate_txt(self, document: Dict) -> Dict[str, Any]:
        """Generate plain text format"""
        title = document.get("title", "Academic Document")
        content = document.get("content", "")
        
        txt_content = f"""{title}
{'=' * len(title)}

{content}

---
Generated by ASCAES v1.0.0
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        return {
            "content": txt_content,
            "metadata": {
                "format": "txt",
                "size": len(txt_content),
                "encoding": "utf-8",
                "line_count": len(txt_content.split('\n')),
                "generated_at": datetime.now().isoformat()
            }
        }
    
    def _validate_content(self, document: Dict, criteria: Dict) -> Dict[str, Any]:
        """Validate document content"""
        content = document.get("content", "")
        word_count = len(content.split())
        
        min_words = criteria.get("min_word_count", 500)
        max_words = criteria.get("max_word_count", 50000)
        
        return {
            "word_count_valid": min_words <= word_count <= max_words,
            "word_count": word_count,
            "content_present": bool(content.strip()),
            "score": 1.0 if min_words <= word_count <= max_words and content.strip() else 0.5
        }
    
    def _validate_structure(self, document: Dict, criteria: Dict) -> Dict[str, Any]:
        """Validate document structure"""
        sections = document.get("sections", [])
        required_sections = criteria.get("required_sections", ["introduction", "conclusion"])
        
        has_required = all(any(req.lower() in sec.lower() for sec in sections) 
                          for req in required_sections)
        
        return {
            "has_required_sections": has_required,
            "section_count": len(sections),
            "sections_found": sections,
            "score": 1.0 if has_required else 0.7
        }
    
    def _validate_formatting(self, document: Dict, criteria: Dict) -> Dict[str, Any]:
        """Validate document formatting"""
        # Basic formatting validation
        return {
            "formatting_valid": True,
            "score": 1.0
        }
    
    def _validate_citations(self, document: Dict, criteria: Dict) -> Dict[str, Any]:
        """Validate citations"""
        content = document.get("content", "")
        citation_count = content.count("(") + content.count("[")  # Simplified citation detection
        
        return {
            "citations_present": citation_count > 0,
            "citation_count": citation_count,
            "score": 1.0 if citation_count > 0 else 0.8
        }
    
    def _calculate_overall_quality_score(self, validation_results: Dict) -> float:
        """Calculate overall quality score"""
        scores = [result.get("score", 0.0) for result in validation_results.values()]
        return sum(scores) / len(scores) if scores else 0.0
    
    def _determine_quality_level(self, score: float) -> str:
        """Determine quality level from score"""
        if score >= 0.9:
            return "excellent"
        elif score >= 0.8:
            return "good"
        elif score >= 0.7:
            return "acceptable"
        else:
            return "needs_improvement"
    
    async def _analyze_content_metadata(self, content: str) -> Dict[str, Any]:
        """Analyze content for metadata"""
        return {
            "readability_score": 0.8,  # Placeholder
            "complexity_level": "graduate",
            "topic_keywords": ["research", "analysis", "academic"],
            "sentiment": "neutral"
        }
    
    def _calculate_content_checksum(self, content: str) -> str:
        """Calculate content checksum"""
        import hashlib
        return hashlib.md5(content.encode()).hexdigest()
    
    def _estimate_page_count(self, content: str) -> int:
        """Estimate page count based on word count"""
        word_count = len(content.split())
        return max(1, word_count // 250)  # ~250 words per page
    
    def _calculate_total_generation_time(self, generation_history: Dict) -> float:
        """Calculate total generation time"""
        # Simplified calculation
        return len(generation_history) * 30.0  # 30 seconds per agent
    
    def _extract_sections(self, content: str) -> List[str]:
        """Extract section headers from content"""
        import re
        sections = re.findall(r'^#+\s+(.+)$', content, re.MULTILINE)
        if not sections:
            # Look for other section indicators
            sections = re.findall(r'^([A-Z][A-Za-z\s]+):?\s*$', content, re.MULTILINE)
        return sections[:10]  # Limit to first 10 sections
    
    def _add_basic_structure(self, content: str) -> str:
        """Add basic structure to unstructured content"""
        # Simple structure addition
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 3:
            structured = f"# Introduction\n\n{paragraphs[0]}\n\n"
            structured += f"# Main Content\n\n" + '\n\n'.join(paragraphs[1:-1])
            structured += f"\n\n# Conclusion\n\n{paragraphs[-1]}"
            return structured
        return content
    
    def _generate_assembly_report(self, components: Dict, document: Dict) -> Dict[str, Any]:
        """Generate assembly report"""
        return {
            "components_processed": len(components),
            "final_word_count": document.get("word_count", 0),
            "assembly_successful": True,
            "issues_encountered": [],
            "recommendations": ["Document assembled successfully"]
        }
    
    async def _package_final_document(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Package final document with all components"""
        document = task.get("document", {})
        formats = task.get("formats", {})
        metadata = task.get("metadata", {})
        validation = task.get("validation", {})
        
        package = {
            "document": document,
            "formats": formats,
            "metadata": metadata,
            "validation": validation,
            "package_info": {
                "created_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "generator": "ASCAES Assembly Agent"
            }
        }
        
        return {
            "success": True,
            "package": package,
            "package_size": len(str(package)),
            "formats_included": list(formats.keys())
        }

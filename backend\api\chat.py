"""
Chat API endpoints for ASCAES
Handles natural language document generation requests
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uuid
from datetime import datetime

from services.chat_handler import chat_handler
from services.large_document_service import LargeDocumentService
from agents.agent_coordinator import AgentCoordinator
from core.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()

# Global instances
large_doc_service = LargeDocumentService()
coordinator = AgentCoordinator()

# Active chat sessions
active_sessions: Dict[str, Dict[str, Any]] = {}

class ChatMessage(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    type: str
    session_id: str
    conversation_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    action: Optional[str] = None

class DocumentConfirmation(BaseModel):
    session_id: str
    confirmed: bool
    modifications: Optional[Dict[str, Any]] = None

@router.post("/message", response_model=ChatResponse)
async def send_chat_message(
    message: ChatMessage,
    background_tasks: BackgroundTasks
):
    """Send a chat message and get response"""
    try:
        # Generate session ID if not provided
        session_id = message.session_id or str(uuid.uuid4())
        conversation_id = message.conversation_id or str(uuid.uuid4())
        
        logger.info(f"Processing chat message: {message.message[:100]}...")
        
        # Process the message
        response = await chat_handler.process_message(
            message=message.message,
            session_id=session_id
        )
        
        # Store session info
        active_sessions[session_id] = {
            'conversation_id': conversation_id,
            'last_message': message.message,
            'last_response': response,
            'timestamp': datetime.now(),
            'status': 'active'
        }
        
        # Handle different response types
        response_type = response.get('type', 'general')
        response_message = response.get('message', 'I understand your request.')
        
        if response_type == 'large_document_request':
            # Store the request for confirmation
            active_sessions[session_id]['pending_request'] = response.get('request')
            active_sessions[session_id]['request_type'] = 'large_document'
            
            # Add confirmation prompt
            response_message += "\n\n🚀 **Ready to start generation?**\nReply with 'yes' to begin, or 'modify' to change parameters."
        
        elif response_type == 'document_request':
            # Store the request for confirmation
            active_sessions[session_id]['pending_request'] = response.get('request')
            active_sessions[session_id]['request_type'] = 'regular_document'
            
            # Add confirmation prompt
            response_message += "\n\n🚀 **Ready to start generation?**\nReply with 'yes' to begin, or 'modify' to change parameters."
        
        return ChatResponse(
            response=response_message,
            type=response_type,
            session_id=session_id,
            conversation_id=conversation_id,
            metadata=response.get('details'),
            action=response.get('action')
        )
        
    except Exception as e:
        logger.error(f"Error processing chat message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/confirm", response_model=ChatResponse)
async def confirm_document_generation(
    confirmation: DocumentConfirmation,
    background_tasks: BackgroundTasks
):
    """Confirm and start document generation"""
    try:
        session_id = confirmation.session_id
        
        if session_id not in active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session = active_sessions[session_id]
        pending_request = session.get('pending_request')
        request_type = session.get('request_type')
        
        if not pending_request:
            raise HTTPException(status_code=400, detail="No pending request found")
        
        if not confirmation.confirmed:
            return ChatResponse(
                response="Document generation cancelled. Feel free to make a new request!",
                type="cancelled",
                session_id=session_id
            )
        
        # Apply modifications if provided
        if confirmation.modifications:
            pending_request.update(confirmation.modifications)
        
        # Start generation based on type
        if request_type == 'large_document':
            # Start large document generation
            background_tasks.add_task(
                _run_large_document_generation,
                session_id,
                pending_request
            )
            
            target_pages = pending_request.get('target_pages', 100)
            estimate = large_doc_service.get_estimated_time(target_pages)
            
            response_message = f"""
🚀 **Large Document Generation Started!**

📄 **Document Details:**
• Title: {pending_request.get('title', 'Academic Document')}
• Pages: {target_pages}
• Type: {pending_request.get('document_type', 'research_paper').replace('_', ' ').title()}
• Style: {pending_request.get('writing_style', 'analytical').title()}

⏱️ **Estimated Time:** {estimate['estimated_minutes']:.1f} minutes

✨ **Features:**
• AI detection avoidance
• Multiple humanization passes  
• Quality assurance checks
• Multiple output formats

📊 **Progress:** You can check progress anytime by asking "What's the status?"
            """.strip()
            
        else:
            # Start regular document generation
            background_tasks.add_task(
                _run_regular_document_generation,
                session_id,
                pending_request
            )
            
            target_pages = pending_request.get('target_pages', 8)
            response_message = f"""
🚀 **Document Generation Started!**

📄 **Document Details:**
• Title: {pending_request.get('title', 'Academic Document')}
• Pages: {target_pages}
• Type: {pending_request.get('document_type', 'research_paper').replace('_', ' ').title()}

⏱️ **Estimated Time:** {target_pages * 2} minutes

📊 **Progress:** You can check progress anytime by asking "What's the status?"
            """.strip()
        
        # Update session status
        session['status'] = 'generating'
        session['generation_started'] = datetime.now()
        
        return ChatResponse(
            response=response_message,
            type="generation_started",
            session_id=session_id,
            metadata={
                'request': pending_request,
                'estimated_time': estimate.get('estimated_minutes', target_pages * 2) if request_type == 'large_document' else target_pages * 2
            }
        )
        
    except Exception as e:
        logger.error(f"Error confirming document generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{session_id}")
async def get_chat_session_status(session_id: str):
    """Get status of a chat session"""
    try:
        if session_id not in active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session = active_sessions[session_id]
        
        # Check if there's an active generation
        from api.agents import active_generations
        
        generation_status = None
        if session_id in active_generations:
            generation_status = active_generations[session_id]
        
        return {
            'session_id': session_id,
            'status': session.get('status', 'active'),
            'last_activity': session.get('timestamp'),
            'generation_status': generation_status,
            'conversation_id': session.get('conversation_id')
        }
        
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions")
async def get_active_sessions():
    """Get all active chat sessions"""
    try:
        sessions = []
        for session_id, session_data in active_sessions.items():
            sessions.append({
                'session_id': session_id,
                'status': session_data.get('status', 'active'),
                'last_activity': session_data.get('timestamp'),
                'conversation_id': session_data.get('conversation_id')
            })
        
        return {
            'active_sessions': len(sessions),
            'sessions': sessions
        }
        
    except Exception as e:
        logger.error(f"Error getting active sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background task functions
async def _run_large_document_generation(session_id: str, request: Dict[str, Any]):
    """Run large document generation in background"""
    try:
        from api.agents import active_generations
        
        # Initialize generation tracking
        active_generations[session_id] = {
            "status": "running",
            "progress": 0,
            "phase": "initialization",
            "request": request,
            "result": None,
            "error": None,
            "document_type": "large_document",
            "target_pages": request.get('target_pages', 100)
        }
        
        # Progress callback
        async def progress_callback(progress_data):
            if session_id in active_generations:
                active_generations[session_id].update({
                    "progress": progress_data.get("progress", 0),
                    "phase": progress_data.get("phase", "unknown"),
                    "message": progress_data.get("message", "")
                })
        
        # Run generation
        result = await large_doc_service.generate_large_document(
            request=request,
            session_id=session_id,
            progress_callback=progress_callback
        )
        
        # Update results
        active_generations[session_id].update({
            "status": "completed",
            "progress": 100,
            "phase": "completed",
            "result": result
        })
        
        # Update session
        if session_id in active_sessions:
            active_sessions[session_id]['status'] = 'completed'
            active_sessions[session_id]['result'] = result
        
        logger.info(f"Large document generation completed: {session_id}")
        
    except Exception as e:
        # Update with error
        if session_id in active_generations:
            active_generations[session_id].update({
                "status": "failed",
                "error": str(e)
            })
        
        if session_id in active_sessions:
            active_sessions[session_id]['status'] = 'failed'
            active_sessions[session_id]['error'] = str(e)
        
        logger.error(f"Large document generation failed: {session_id} - {e}")

async def _run_regular_document_generation(session_id: str, request: Dict[str, Any]):
    """Run regular document generation in background"""
    try:
        from api.agents import active_generations
        
        # Initialize generation tracking
        active_generations[session_id] = {
            "status": "running",
            "progress": 0,
            "phase": "generation",
            "request": request,
            "result": None,
            "error": None,
            "document_type": "regular_document"
        }
        
        # Run generation
        result = await coordinator.generate_document(
            request=request,
            session_id=session_id
        )
        
        # Update results
        active_generations[session_id].update({
            "status": "completed",
            "progress": 100,
            "phase": "completed",
            "result": result
        })
        
        # Update session
        if session_id in active_sessions:
            active_sessions[session_id]['status'] = 'completed'
            active_sessions[session_id]['result'] = result
        
        logger.info(f"Regular document generation completed: {session_id}")
        
    except Exception as e:
        # Update with error
        if session_id in active_generations:
            active_generations[session_id].update({
                "status": "failed",
                "error": str(e)
            })
        
        if session_id in active_sessions:
            active_sessions[session_id]['status'] = 'failed'
            active_sessions[session_id]['error'] = str(e)
        
        logger.error(f"Regular document generation failed: {session_id} - {e}")

#!/usr/bin/env python3
"""
Test script to isolate backend startup issues
"""

import sys
import traceback

def test_imports():
    """Test all imports step by step"""
    print("=== Testing Imports ===")
    
    try:
        print("1. Testing FastAPI...")
        from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
        print("✅ FastAPI imported successfully")
        
        print("2. Testing CORS...")
        from fastapi.middleware.cors import CORSMiddleware
        print("✅ CORS imported successfully")
        
        print("3. Testing core config...")
        from core.config import settings
        print("✅ Core config imported successfully")
        
        print("4. Testing database...")
        from core.database import init_db
        print("✅ Database imported successfully")
        
        print("5. Testing routes...")
        from api.routes import chat, documents, models, health
        print("✅ Routes imported successfully")
        
        print("6. Testing agents...")
        from api import agents
        print("✅ Agents imported successfully")
        
        print("7. Testing WebSocket manager...")
        from core.websocket_manager import WebSocketManager
        print("✅ WebSocket manager imported successfully")
        
        print("8. Testing logging...")
        from core.logging_config import setup_logging
        print("✅ Logging imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test FastAPI app creation"""
    print("\n=== Testing App Creation ===")
    
    try:
        from fastapi import FastAPI
        app = FastAPI(title="ASCAES Test")
        print("✅ FastAPI app created successfully")
        return app
        
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        traceback.print_exc()
        return None

def test_uvicorn():
    """Test uvicorn import"""
    print("\n=== Testing Uvicorn ===")
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Uvicorn import failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("ASCAES Backend Startup Test")
    print("=" * 40)
    
    # Test imports
    imports_ok = test_imports()
    
    if imports_ok:
        # Test app creation
        app = test_app_creation()
        
        if app:
            # Test uvicorn
            uvicorn_ok = test_uvicorn()
            
            if uvicorn_ok:
                print("\n✅ All tests passed! Backend should start normally.")
                
                # Try to start the server
                print("\n=== Starting Test Server ===")
                try:
                    import uvicorn
                    uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
                except Exception as e:
                    print(f"❌ Server startup failed: {e}")
                    traceback.print_exc()
            else:
                print("\n❌ Uvicorn test failed")
        else:
            print("\n❌ App creation failed")
    else:
        print("\n❌ Import tests failed")

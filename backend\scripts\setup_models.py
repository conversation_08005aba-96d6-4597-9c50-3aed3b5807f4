#!/usr/bin/env python3
"""
ASCAES Model Setup Script
Downloads and configures recommended models for 8GB RAM systems
"""

import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).parent.parent))

from services.ollama_service import ollama_service
from core.logging_config import setup_logging

logger = setup_logging()

# Recommended models for 8GB RAM
RECOMMENDED_MODELS_8GB = [
    {
        "name": "qwen2:7b-instruct",
        "description": "General purpose model, excellent for academic writing",
        "size": "4.4GB",
        "priority": 1
    },
    {
        "name": "phi3:3.8b", 
        "description": "Efficient reasoning model, good for analysis",
        "size": "2.2GB",
        "priority": 2
    },
    {
        "name": "deepseek-coder:6.7b",
        "description": "Specialized for code generation and technical docs",
        "size": "3.8GB", 
        "priority": 3
    }
]

async def check_ollama_connection():
    """Check if Ollama is running and accessible"""
    try:
        models = await ollama_service.list_models()
        logger.info("✅ Ollama connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Cannot connect to Ollama: {e}")
        logger.error("Please ensure Ollama is installed and running:")
        logger.error("1. Install Ollama from https://ollama.ai/download")
        logger.error("2. Start Ollama service")
        logger.error("3. Run this script again")
        return False

async def check_existing_models():
    """Check which models are already installed"""
    try:
        models_response = await ollama_service.list_models()
        installed_models = [model["name"] for model in models_response.get("models", [])]
        
        logger.info(f"📋 Found {len(installed_models)} installed models:")
        for model in installed_models:
            logger.info(f"  - {model}")
        
        return installed_models
    except Exception as e:
        logger.error(f"Error checking existing models: {e}")
        return []

async def install_model(model_info):
    """Install a single model"""
    model_name = model_info["name"]
    
    try:
        logger.info(f"📥 Installing {model_name} ({model_info['size']})...")
        logger.info(f"   Description: {model_info['description']}")
        
        # Pull the model
        result = await ollama_service.pull_model(model_name, stream=False)
        
        # Test the model
        logger.info(f"🧪 Testing {model_name}...")
        test_result = await ollama_service.test_model(model_name)
        
        if test_result["success"]:
            logger.info(f"✅ {model_name} installed and tested successfully!")
            return True
        else:
            logger.error(f"❌ {model_name} installation failed: {test_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error installing {model_name}: {e}")
        return False

async def setup_models():
    """Main setup function"""
    logger.info("🚀 ASCAES Model Setup")
    logger.info("=" * 50)
    
    # Check Ollama connection
    if not await check_ollama_connection():
        return False
    
    # Check existing models
    installed_models = await check_existing_models()
    
    # Determine which models to install
    models_to_install = []
    for model_info in RECOMMENDED_MODELS_8GB:
        if model_info["name"] not in installed_models:
            models_to_install.append(model_info)
        else:
            logger.info(f"⏭️  {model_info['name']} already installed, skipping")
    
    if not models_to_install:
        logger.info("✅ All recommended models are already installed!")
        return True
    
    # Calculate total download size
    total_size = sum(float(model["size"].replace("GB", "")) for model in models_to_install)
    logger.info(f"📊 Will download {len(models_to_install)} models (~{total_size:.1f}GB total)")
    
    # Confirm installation
    try:
        response = input("\nProceed with installation? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            logger.info("Installation cancelled by user")
            return False
    except KeyboardInterrupt:
        logger.info("\nInstallation cancelled by user")
        return False
    
    # Install models in priority order
    successful_installs = 0
    for model_info in sorted(models_to_install, key=lambda x: x["priority"]):
        if await install_model(model_info):
            successful_installs += 1
        else:
            logger.warning(f"⚠️  Failed to install {model_info['name']}, continuing...")
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info(f"📈 Installation Summary:")
    logger.info(f"   Successfully installed: {successful_installs}/{len(models_to_install)} models")
    
    if successful_installs > 0:
        logger.info("✅ ASCAES is ready to use!")
        logger.info("   You can now start the application with: npm run dev")
    else:
        logger.error("❌ No models were successfully installed")
        logger.error("   Please check the logs above and try again")
    
    return successful_installs > 0

async def verify_installation():
    """Verify the installation is working"""
    logger.info("\n🔍 Verifying installation...")
    
    try:
        # Check if default model is available
        models = await ollama_service.list_models()
        model_names = [model["name"] for model in models.get("models", [])]
        
        default_model = "qwen2:7b-instruct"
        if default_model in model_names:
            logger.info(f"✅ Default model ({default_model}) is available")
            
            # Test generation
            test_result = await ollama_service.test_model(default_model)
            if test_result["success"]:
                logger.info("✅ Model generation test passed")
                return True
            else:
                logger.error(f"❌ Model generation test failed: {test_result.get('error')}")
                return False
        else:
            logger.error(f"❌ Default model ({default_model}) not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main entry point"""
    try:
        # Run setup
        success = asyncio.run(setup_models())
        
        if success:
            # Verify installation
            verification_success = asyncio.run(verify_installation())
            
            if verification_success:
                logger.info("\n🎉 Setup completed successfully!")
                logger.info("   ASCAES is ready for academic document generation")
                sys.exit(0)
            else:
                logger.error("\n❌ Setup verification failed")
                sys.exit(1)
        else:
            logger.error("\n❌ Setup failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

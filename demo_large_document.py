#!/usr/bin/env python3
"""
ASCAES Large Document Generation Demo
Demonstrates generating a 100-page AI-detection-free academic document
"""

import asyncio
import json
import time
from datetime import datetime
import sys
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

async def demo_large_document_generation():
    """Demonstrate large document generation capabilities"""
    
    print("🎓 ASCAES Large Document Generation Demo")
    print("=" * 60)
    print()
    
    # Import services
    from services.large_document_service import LargeDocumentService
    
    # Initialize service
    large_doc_service = LargeDocumentService()
    
    # Demo request for 100-page document
    demo_request = {
        "title": "Artificial Intelligence in Healthcare: A Comprehensive Analysis of Current Applications, Challenges, and Future Prospects",
        "document_type": "research_paper",
        "writing_style": "analytical",
        "target_pages": 100,
        "citation_style": "APA",
        "output_formats": ["pdf", "latex", "txt"],
        "keywords": [
            "artificial intelligence", "healthcare", "machine learning", 
            "medical diagnosis", "patient care", "healthcare technology",
            "clinical decision support", "medical imaging", "drug discovery",
            "personalized medicine", "healthcare automation", "AI ethics"
        ],
        "field": "healthcare_technology",
        "author": "ASCAES Research Team",
        "include_math": True,
        "references": [],
        "humanization_level": "extensive",
        "ai_detection_avoidance": True,
        "quality_threshold": 0.85
    }
    
    print("📋 Document Specifications:")
    print(f"   Title: {demo_request['title']}")
    print(f"   Target Pages: {demo_request['target_pages']}")
    print(f"   Writing Style: {demo_request['writing_style']}")
    print(f"   Field: {demo_request['field']}")
    print(f"   Humanization: {demo_request['humanization_level']}")
    print(f"   AI Detection Avoidance: {demo_request['ai_detection_avoidance']}")
    print(f"   Output Formats: {', '.join(demo_request['output_formats'])}")
    print()
    
    # Get time estimate
    print("⏱️  Estimating Generation Time...")
    estimate = large_doc_service.get_estimated_time(demo_request["target_pages"])
    
    print(f"   Estimated Total Time: {estimate['estimated_minutes']:.1f} minutes ({estimate['estimated_hours']:.1f} hours)")
    print(f"   Time per Page: {estimate['estimated_total_seconds'] / demo_request['target_pages']:.1f} seconds")
    print()
    print("   Time Breakdown:")
    print(f"     Content Generation: {estimate['breakdown']['content_generation'] / 60:.1f} minutes")
    print(f"     Humanization: {estimate['breakdown']['humanization'] / 60:.1f} minutes")
    print(f"     Quality Checks: {estimate['breakdown']['quality_checks'] / 60:.1f} minutes")
    print()
    
    # Ask user if they want to proceed
    response = input("🚀 Would you like to start the generation? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Demo cancelled.")
        return
    
    print("\n🎯 Starting Large Document Generation...")
    print("=" * 60)
    
    # Progress tracking
    progress_history = []
    
    async def progress_callback(progress_data):
        progress_history.append(progress_data)
        progress = progress_data.get("progress", 0)
        phase = progress_data.get("phase", "unknown")
        message = progress_data.get("message", "")
        
        # Create progress bar
        bar_length = 40
        filled_length = int(bar_length * progress / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        print(f"\r[{bar}] {progress:3.0f}% | {phase.upper()}: {message}", end="", flush=True)
        
        if progress >= 100:
            print()  # New line when complete
    
    # Start generation
    session_id = f"demo_{int(time.time())}"
    start_time = datetime.now()
    
    try:
        result = await large_doc_service.generate_large_document(
            request=demo_request,
            session_id=session_id,
            progress_callback=progress_callback
        )
        
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        print("\n" + "=" * 60)
        
        if result.get("success", False):
            print("✅ Large Document Generation Completed Successfully!")
            print()
            
            # Display results
            metadata = result.get("generation_metadata", {})
            document = result.get("document", {})
            
            print("📊 Generation Results:")
            print(f"   Target Pages: {metadata.get('target_pages', 0)}")
            print(f"   Actual Pages: {metadata.get('actual_pages', 0)}")
            print(f"   Target Words: {metadata.get('target_words', 0):,}")
            print(f"   Actual Words: {metadata.get('actual_words', 0):,}")
            print(f"   Chunks Generated: {metadata.get('chunks_generated', 0)}")
            print(f"   Generation Time: {generation_time:.1f} seconds ({generation_time/60:.1f} minutes)")
            print(f"   Time per Page: {generation_time / metadata.get('actual_pages', 1):.1f} seconds")
            print()
            
            print("🎯 Quality Metrics:")
            print(f"   Overall Quality Score: {metadata.get('quality_score', 0):.3f}")
            print(f"   AI Detection Score: {metadata.get('ai_detection_score', 1):.3f} (lower is better)")
            print(f"   Humanization Passes: {metadata.get('humanization_passes', 0)}")
            print()
            
            print("📁 Available Formats:")
            formats = result.get("formats_available", [])
            for fmt in formats:
                print(f"   ✓ {fmt.upper()}")
            print()
            
            print("📝 Chunk Details:")
            chunk_details = result.get("chunk_details", [])
            for chunk in chunk_details:
                chunk_id = chunk.get("chunk_id", 0)
                pages = chunk.get("pages", 0)
                words = chunk.get("words", 0)
                quality = chunk.get("quality_score", 0)
                humanization = chunk.get("humanization_score", 0)
                print(f"   Chunk {chunk_id + 1}: {pages} pages, {words:,} words, Q:{quality:.2f}, H:{humanization:.2f}")
            print()
            
            # Show sample content
            content = document.get("content", "")
            if content:
                print("📄 Sample Content (First 500 characters):")
                print("-" * 50)
                print(content[:500] + "..." if len(content) > 500 else content)
                print("-" * 50)
                print()
            
            print("💾 Document Ready for Download!")
            print("   The document has been generated with:")
            print("   ✓ AI detection avoidance")
            print("   ✓ Multiple humanization passes")
            print("   ✓ Quality assurance checks")
            print("   ✓ Academic standards compliance")
            print("   ✓ Multiple output formats (PDF, LaTeX, TXT)")
            print()
            
            # Save demo results
            demo_results = {
                "request": demo_request,
                "result": result,
                "generation_time": generation_time,
                "progress_history": progress_history,
                "demo_timestamp": datetime.now().isoformat()
            }
            
            results_file = f"demo_results_{session_id}.json"
            with open(results_file, 'w') as f:
                json.dump(demo_results, f, indent=2, default=str)
            
            print(f"📋 Demo results saved to: {results_file}")
            
        else:
            print("❌ Large Document Generation Failed!")
            error = result.get("error", "Unknown error")
            print(f"   Error: {error}")
            
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

async def demo_chunking_strategy():
    """Demonstrate the chunking strategy for large documents"""
    
    print("\n🧩 Chunking Strategy Demonstration")
    print("=" * 50)
    
    from services.large_document_service import LargeDocumentService
    
    service = LargeDocumentService()
    
    # Test different document sizes
    test_sizes = [50, 100, 200, 300]
    
    for pages in test_sizes:
        print(f"\n📄 {pages}-Page Document:")
        
        # Create a mock master plan
        mock_plan = {
            "structure": {
                "sections": [
                    {"name": "Introduction", "start_page": 1, "end_page": pages // 10},
                    {"name": "Literature Review", "start_page": pages // 10 + 1, "end_page": pages // 3},
                    {"name": "Methodology", "start_page": pages // 3 + 1, "end_page": pages // 2},
                    {"name": "Results", "start_page": pages // 2 + 1, "end_page": int(pages * 0.8)},
                    {"name": "Discussion", "start_page": int(pages * 0.8) + 1, "end_page": int(pages * 0.95)},
                    {"name": "Conclusion", "start_page": int(pages * 0.95) + 1, "end_page": pages}
                ]
            }
        }
        
        chunks = service._create_chunks(mock_plan, pages)
        
        print(f"   Total Chunks: {len(chunks)}")
        print(f"   Chunk Details:")
        
        for chunk in chunks:
            chunk_id = chunk["chunk_id"]
            start_page = chunk["start_page"]
            end_page = chunk["end_page"]
            chunk_pages = chunk["pages"]
            target_words = chunk["target_words"]
            
            print(f"     Chunk {chunk_id + 1}: Pages {start_page}-{end_page} ({chunk_pages} pages, ~{target_words:,} words)")

def main():
    """Main demo function"""
    print("🎓 ASCAES Large Document Generation System")
    print("=" * 60)
    print()
    print("This demo showcases ASCAES's ability to generate large academic documents")
    print("with the following features:")
    print()
    print("✓ Intelligent chunking for documents up to 500 pages")
    print("✓ Multiple humanization passes to avoid AI detection")
    print("✓ Comprehensive quality assurance")
    print("✓ Academic standards compliance")
    print("✓ Multiple output formats (PDF, LaTeX, TXT)")
    print("✓ Real-time progress tracking")
    print("✓ Chunk-by-chunk processing for memory efficiency")
    print()
    
    print("Demo Options:")
    print("1. Full 100-page document generation demo")
    print("2. Chunking strategy demonstration")
    print("3. Time estimation demo")
    print("0. Exit")
    
    try:
        choice = input("\nSelect demo option (0-3): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            return
        elif choice == "1":
            asyncio.run(demo_large_document_generation())
        elif choice == "2":
            asyncio.run(demo_chunking_strategy())
        elif choice == "3":
            from services.large_document_service import LargeDocumentService
            service = LargeDocumentService()
            
            pages = int(input("Enter target pages (10-500): "))
            estimate = service.get_estimated_time(pages)
            
            print(f"\n⏱️  Time Estimate for {pages} pages:")
            print(f"   Total Time: {estimate['estimated_minutes']:.1f} minutes")
            print(f"   Per Page: {estimate['estimated_total_seconds'] / pages:.1f} seconds")
            print(f"   Content Generation: {estimate['breakdown']['content_generation'] / 60:.1f} min")
            print(f"   Humanization: {estimate['breakdown']['humanization'] / 60:.1f} min")
            print(f"   Quality Checks: {estimate['breakdown']['quality_checks'] / 60:.1f} min")
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")

if __name__ == "__main__":
    main()

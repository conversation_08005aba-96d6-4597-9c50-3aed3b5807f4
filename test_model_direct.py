#!/usr/bin/env python3
"""
Test script to directly test model generation
"""

import requests
import json

def test_model_direct():
    """Test model generation directly"""
    
    # Test prompt
    prompt = """You are an expert academic writer. Generate a comprehensive, detailed academic research_paper about "Bangladesh economy".

CRITICAL REQUIREMENTS:
- Target Length: EXACTLY 1000 words (this is mandatory)
- Writing Style: analytical (critical examination and data interpretation with evidence-based conclusions)
- Document Type: research_paper
- Topic Focus: Provide extensive, detailed information specifically about "Bangladesh economy"

CONTENT REQUIREMENTS:
1. DEPTH: Provide comprehensive coverage of the topic with specific details, examples, and analysis
2. STRUCTURE: Use proper academic structure with multiple detailed sections
3. SPECIFICITY: Focus extensively on "Bangladesh economy" - provide specific facts, data, examples, and detailed explanations
4. LENGTH: Generate approximately 1000 words of substantive content
5. ACADEMIC RIGOR: Include proper citations, references, and scholarly analysis

Begin writing the complete academic document now:"""

    print("🚀 Testing DeepSeek R1 model directly...")
    print(f"Prompt length: {len(prompt)} characters")
    
    try:
        # Test with Ollama API directly
        response = requests.post("http://localhost:11434/api/generate", json={
            "model": "deepseek-r1:7b",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "num_ctx": 8192,
                "num_predict": 1500,  # Allow for 1000+ words
                "repeat_penalty": 1.1
            }
        })
        
        response.raise_for_status()
        result = response.json()
        
        generated_text = result.get("response", "")
        word_count = len(generated_text.split())
        
        print(f"✅ Generation successful!")
        print(f"📊 Word count: {word_count}")
        print(f"📝 Content preview (first 500 chars):")
        print("-" * 50)
        print(generated_text[:500])
        print("-" * 50)
        
        if word_count < 100:
            print("❌ WARNING: Very low word count - model may not be generating properly")
        elif "Abstract" in generated_text and "Introduction" in generated_text:
            print("✅ Content appears to be properly structured")
        else:
            print("⚠️ Content may be generic or incomplete")
            
        return generated_text
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    test_model_direct()

"""
ASCAES Database Configuration
SQLAlchemy setup for metadata storage and ChromaDB for vector storage
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from datetime import datetime
from typing import Generator
import chromadb
from chromadb.config import Settings as ChromaSettings

from core.config import settings

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database Models
class Conversation(Base):
    """Chat conversation metadata"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    user_id = Column(String(255), nullable=True)  # For future user management

class Message(Base):
    """Chat messages"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, nullable=False)
    role = Column(String(50), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=func.now())
    message_type = Column(String(50), default="text")  # text, document, image
    message_metadata = Column(Text, nullable=True)  # JSON metadata

class Document(Base):
    """Document metadata"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    mime_type = Column(String(100), nullable=False)
    upload_date = Column(DateTime, default=func.now())
    processed = Column(Boolean, default=False)
    conversation_id = Column(Integer, nullable=True)
    checksum = Column(String(64), nullable=True)

class GeneratedDocument(Base):
    """Generated academic documents"""
    __tablename__ = "generated_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    document_type = Column(String(100), nullable=False)  # paper, thesis, report, etc.
    writing_style = Column(String(100), nullable=False)  # analytical, instructional, etc.
    content = Column(Text, nullable=False)
    latex_content = Column(Text, nullable=True)
    output_format = Column(String(50), nullable=False)  # pdf, latex, rtf, txt
    file_path = Column(String(500), nullable=True)
    conversation_id = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    word_count = Column(Integer, nullable=True)
    page_count = Column(Integer, nullable=True)
    quality_score = Column(Float, nullable=True)

class ModelUsage(Base):
    """Track model usage and performance"""
    __tablename__ = "model_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String(100), nullable=False)
    task_type = Column(String(100), nullable=False)
    tokens_used = Column(Integer, nullable=True)
    response_time = Column(Float, nullable=True)
    timestamp = Column(DateTime, default=func.now())
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)

# ChromaDB setup
class VectorDatabase:
    """ChromaDB vector database manager"""
    
    def __init__(self):
        self.client = chromadb.PersistentClient(
            path=str(settings.VECTOR_DB_PATH),
            settings=ChromaSettings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        self.collections = {}
    
    def get_collection(self, name: str):
        """Get or create a collection"""
        if name not in self.collections:
            self.collections[name] = self.client.get_or_create_collection(
                name=name,
                metadata={"hnsw:space": "cosine"}
            )
        return self.collections[name]
    
    def add_documents(self, collection_name: str, documents: list, metadatas: list, ids: list):
        """Add documents to collection"""
        collection = self.get_collection(collection_name)
        collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )
    
    def query_documents(self, collection_name: str, query_texts: list, n_results: int = 5):
        """Query documents from collection"""
        collection = self.get_collection(collection_name)
        return collection.query(
            query_texts=query_texts,
            n_results=n_results
        )
    
    def delete_collection(self, name: str):
        """Delete a collection"""
        try:
            self.client.delete_collection(name)
            if name in self.collections:
                del self.collections[name]
        except Exception:
            pass  # Collection might not exist

# Database dependency
def get_db() -> Generator[Session, None, None]:
    """Database session dependency"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Global vector database instance
vector_db = VectorDatabase()

async def init_db():
    """Initialize database tables"""
    Base.metadata.create_all(bind=engine)
    
    # Create default collections
    vector_db.get_collection("documents")
    vector_db.get_collection("conversations")
    vector_db.get_collection("knowledge_base")

/**
 * API Service for ASCAES
 * Handles HTTP requests to the backend
 */

const API_BASE_URL = 'http://localhost:8001'

export interface DocumentGenerationRequest {
  title: string
  document_type: string
  writing_style: string
  target_length?: number
  target_pages?: number
  citation_style: string
  output_formats: string[]
  keywords: string[]
  field: string
  author: string
  include_math: boolean
  references: string[]
  humanization_level?: string
  ai_detection_avoidance?: boolean
  quality_threshold?: number
}

export interface GenerationResponse {
  session_id: string
  status: string
  message: string
}

export interface ProgressResponse {
  status: string
  progress: number
  phase: string
  message?: string
  target_pages?: number
  estimated_time?: any
}

export interface DocumentResult {
  success: boolean
  document: any
  generation_metadata: any
  chunk_details?: any[]
  formats_available: string[]
}

class ApiService {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/health`)
    return response.json()
  }

  // Generate regular document
  async generateDocument(request: DocumentGenerationRequest): Promise<GenerationResponse> {
    const response = await fetch(`${this.baseUrl}/api/agents/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Generate large document (50+ pages)
  async generateLargeDocument(request: DocumentGenerationRequest): Promise<GenerationResponse> {
    const response = await fetch(`${this.baseUrl}/api/agents/generate/large`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Get time estimate for large document
  async getTimeEstimate(targetPages: number): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/agents/estimate/large/${targetPages}`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Get generation progress
  async getGenerationProgress(sessionId: string): Promise<ProgressResponse> {
    const response = await fetch(`${this.baseUrl}/api/agents/generate/${sessionId}`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Get generation result
  async getGenerationResult(sessionId: string): Promise<DocumentResult> {
    const response = await fetch(`${this.baseUrl}/api/agents/generate/${sessionId}/result`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Cancel generation
  async cancelGeneration(sessionId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/agents/generate/${sessionId}`, {
      method: 'DELETE',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Get system status
  async getSystemStatus(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/agents/status`)
    return response.json()
  }

  // Get agents list
  async getAgents(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/agents/agents`)
    return response.json()
  }

  // Chat with the system
  async sendChatMessage(message: string, conversationId?: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/chat/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        conversation_id: conversationId,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Get generated documents
  async getGeneratedDocuments(): Promise<any[]> {
    const response = await fetch(`${this.baseUrl}/api/documents/generated/`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Download generated document
  async downloadDocument(documentId: number): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/api/documents/generated/${documentId}/download`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.blob()
  }

  // Stop document generation
  async stopGeneration(sessionId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/agents/stop/${sessionId}`, {
      method: 'POST',
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  // Parse document generation request from natural language
  parseDocumentRequest(message: string): DocumentGenerationRequest | null {
    const lowerMessage = message.toLowerCase()
    
    // Check if it's a document generation request
    const isDocRequest = 
      lowerMessage.includes('generate') ||
      lowerMessage.includes('create') ||
      lowerMessage.includes('write') ||
      lowerMessage.includes('document') ||
      lowerMessage.includes('paper') ||
      lowerMessage.includes('report') ||
      lowerMessage.includes('thesis')

    if (!isDocRequest) return null

    // Extract document details
    const request: DocumentGenerationRequest = {
      title: this.extractTitle(message),
      document_type: this.extractDocumentType(message),
      writing_style: this.extractWritingStyle(message),
      citation_style: this.extractCitationStyle(message),
      output_formats: this.extractOutputFormats(message),
      keywords: this.extractKeywords(message),
      field: this.extractField(message),
      author: 'ASCAES Generated',
      include_math: lowerMessage.includes('math') || lowerMessage.includes('equation'),
      references: [],
    }

    // Check if it's a large document
    const pages = this.extractPages(message)
    const words = this.extractWords(message)

    if (pages && pages >= 50) {
      request.target_pages = pages
      request.humanization_level = 'extensive'
      request.ai_detection_avoidance = true
      request.quality_threshold = 0.85
    } else if (words) {
      request.target_length = words
    } else if (pages) {
      request.target_length = pages * 250 // ~250 words per page
    } else {
      request.target_length = 2000 // Default
    }

    return request
  }

  private extractTitle(message: string): string {
    // Look for patterns like "about X", "on X", "titled X"
    const patterns = [
      /(?:about|on|titled?|called)\s+["']([^"']+)["']/i,
      /(?:about|on)\s+([^.!?]+?)(?:\s+(?:in|with|using|for)|\.|$)/i,
    ]

    for (const pattern of patterns) {
      const match = message.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    return 'Academic Document'
  }

  private extractDocumentType(message: string): string {
    const types = {
      'research paper': /research\s+paper/i,
      'thesis': /thesis/i,
      'dissertation': /dissertation/i,
      'report': /report/i,
      'essay': /essay/i,
      'article': /article/i,
      'review': /review/i,
    }

    for (const [type, pattern] of Object.entries(types)) {
      if (pattern.test(message)) {
        return type.replace(' ', '_')
      }
    }

    return 'research_paper'
  }

  private extractWritingStyle(message: string): string {
    const styles = {
      'analytical': /analytical|analysis|analyze/i,
      'argumentative': /argument|persuasive|debate/i,
      'descriptive': /descriptive|describe/i,
      'narrative': /narrative|story|case study/i,
      'instructional': /instructional|tutorial|guide|how.?to/i,
      'reporting': /report|findings|results/i,
      'exploratory': /exploratory|explore|investigate/i,
      'schematic': /reference|manual|documentation/i,
    }

    for (const [style, pattern] of Object.entries(styles)) {
      if (pattern.test(message)) {
        return style
      }
    }

    return 'analytical'
  }

  private extractCitationStyle(message: string): string {
    const styles = {
      'APA': /apa/i,
      'MLA': /mla/i,
      'Chicago': /chicago/i,
      'IEEE': /ieee/i,
      'Harvard': /harvard/i,
    }

    for (const [style, pattern] of Object.entries(styles)) {
      if (pattern.test(message)) {
        return style
      }
    }

    return 'APA'
  }

  private extractOutputFormats(message: string): string[] {
    const formats = []
    
    if (/pdf/i.test(message)) formats.push('pdf')
    if (/latex|tex/i.test(message)) formats.push('latex')
    if (/word|docx/i.test(message)) formats.push('rtf')
    if (/text|txt/i.test(message)) formats.push('txt')

    return formats.length > 0 ? formats : ['pdf', 'latex', 'txt']
  }

  private extractKeywords(message: string): string[] {
    // Simple keyword extraction - in production, this would be more sophisticated
    const words = message.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['generate', 'create', 'write', 'document', 'paper', 'about', 'with', 'using', 'pages', 'words'].includes(word))

    return words.slice(0, 10) // Limit to 10 keywords
  }

  private extractField(message: string): string {
    const fields = {
      'computer_science': /computer science|programming|software|ai|artificial intelligence|machine learning/i,
      'healthcare': /healthcare|medical|medicine|health|clinical/i,
      'business': /business|management|marketing|finance|economics/i,
      'education': /education|teaching|learning|pedagogy/i,
      'psychology': /psychology|mental health|behavior/i,
      'engineering': /engineering|technical|technology/i,
      'science': /science|research|scientific|biology|chemistry|physics/i,
    }

    for (const [field, pattern] of Object.entries(fields)) {
      if (pattern.test(message)) {
        return field
      }
    }

    return 'general'
  }

  private extractPages(message: string): number | null {
    const pagePatterns = [
      /(\d+)\s*pages?/i,
      /(\d+)\s*page/i,
    ]

    for (const pattern of pagePatterns) {
      const match = message.match(pattern)
      if (match) {
        return parseInt(match[1])
      }
    }

    return null
  }

  private extractWords(message: string): number | null {
    const wordPatterns = [
      /(\d+)\s*words?/i,
      /(\d+)\s*word/i,
    ]

    for (const pattern of wordPatterns) {
      const match = message.match(pattern)
      if (match) {
        return parseInt(match[1])
      }
    }

    return null
  }
}

export const apiService = new ApiService()
export default apiService

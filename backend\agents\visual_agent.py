"""
Visual Agent
Responsible for creating visual elements, figures, tables, and diagrams
"""

import json
from typing import Dict, Any, List
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class VisualAgent(BaseAgent):
    """Agent specialized in creating visual elements for academic documents"""
    
    def __init__(self):
        super().__init__(
            agent_id="visual_agent",
            name="Visual Agent",
            description="Creates visual elements including figures, tables, diagrams, and charts for academic documents"
        )
        
        # Visual-specific configuration
        self.config.update({
            "supported_formats": ["png", "pdf", "svg", "eps"],
            "chart_types": [
                "bar_chart", "line_chart", "scatter_plot", "histogram",
                "pie_chart", "box_plot", "heatmap", "flowchart", "diagram"
            ],
            "table_styles": {
                "academic": {"borders": True, "header_style": "bold", "alignment": "center"},
                "minimal": {"borders": False, "header_style": "underline", "alignment": "left"},
                "formal": {"borders": "booktabs", "header_style": "bold", "alignment": "justified"}
            },
            "color_schemes": {
                "academic": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D"],
                "grayscale": ["#000000", "#404040", "#808080", "#C0C0C0"],
                "colorblind_friendly": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
            },
            "figure_templates": self._load_figure_templates()
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define visual agent capabilities"""
        return [
            AgentCapability(
                name="table_creation",
                description="Create formatted tables from data",
                input_types=["tabular_data", "csv_data", "structured_data"],
                output_types=["formatted_table", "table_markup"]
            ),
            AgentCapability(
                name="chart_generation",
                description="Generate charts and graphs from data",
                input_types=["numerical_data", "chart_specifications"],
                output_types=["chart_code", "visualization_markup"]
            ),
            AgentCapability(
                name="diagram_creation",
                description="Create diagrams and flowcharts",
                input_types=["process_description", "relationship_data"],
                output_types=["diagram_code", "flowchart_markup"]
            ),
            AgentCapability(
                name="figure_integration",
                description="Integrate figures into document structure",
                input_types=["figure_specifications", "document_context"],
                output_types=["integrated_figures", "caption_text"]
            ),
            AgentCapability(
                name="visual_layout",
                description="Design visual layout and positioning",
                input_types=["content_structure", "layout_requirements"],
                output_types=["layout_design", "positioning_code"]
            )
        ]
    
    def _load_figure_templates(self) -> Dict[str, str]:
        """Load figure templates for different types"""
        return {
            "research_process": """
            Research Question → Literature Review → Methodology → Data Collection → Analysis → Results → Discussion → Conclusion
            """,
            "data_flow": """
            Input Data → Processing → Analysis → Validation → Output → Interpretation
            """,
            "comparison_table": """
            | Aspect | Option A | Option B | Option C |
            |--------|----------|----------|----------|
            | Criterion 1 | Value | Value | Value |
            | Criterion 2 | Value | Value | Value |
            """,
            "results_summary": """
            | Metric | Value | Confidence Interval | Significance |
            |--------|-------|---------------------|--------------|
            | Result 1 | X.XX | [X.XX, X.XX] | p < 0.05 |
            """
        }
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute visual creation task"""
        task_type = task.get("type", "table_creation")
        
        if task_type == "table_creation":
            return await self._create_table(task)
        elif task_type == "chart_generation":
            return await self._generate_chart(task)
        elif task_type == "diagram_creation":
            return await self._create_diagram(task)
        elif task_type == "figure_integration":
            return await self._integrate_figures(task)
        elif task_type == "comprehensive_visual":
            return await self._create_comprehensive_visuals(task)
        else:
            raise ValueError(f"Unknown visual task type: {task_type}")
    
    async def _create_table(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create formatted table from data"""
        data = task.get("data", [])
        table_type = task.get("table_type", "academic")
        caption = task.get("caption", "")
        headers = task.get("headers", [])
        
        # Generate table creation prompt
        table_prompt = f"""Create a well-formatted academic table from the following data:

Data: {data}
Headers: {headers}
Table Type: {table_type}
Caption: {caption}

Generate:
1. HTML table markup for web display
2. LaTeX table code for academic documents
3. Markdown table format
4. CSV format for data export

Ensure proper formatting, alignment, and academic presentation standards."""

        system_prompt = """You are an expert in academic table formatting. Create clear, 
        professional tables that follow academic publishing standards with proper alignment, 
        spacing, and formatting."""

        table_result = await self._call_llm(table_prompt, system_prompt)
        
        # Parse and structure the result
        formatted_table = self._parse_table_result(table_result, data, headers, caption)
        
        return {
            "success": True,
            "table": formatted_table,
            "table_type": table_type,
            "caption": caption,
            "row_count": len(data),
            "column_count": len(headers) if headers else 0
        }
    
    async def _generate_chart(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Generate chart or graph from data"""
        data = task.get("data", [])
        chart_type = task.get("chart_type", "bar_chart")
        title = task.get("title", "")
        x_label = task.get("x_label", "")
        y_label = task.get("y_label", "")
        color_scheme = task.get("color_scheme", "academic")
        
        chart_prompt = f"""Create a {chart_type} visualization from the following data:

Data: {data}
Title: {title}
X-axis label: {x_label}
Y-axis label: {y_label}
Color scheme: {color_scheme}

Generate:
1. Python matplotlib code for the chart
2. TikZ/PGFPlots code for LaTeX integration
3. Description of the chart for accessibility
4. Data interpretation notes

Ensure the chart follows academic visualization standards with clear labels, 
appropriate scaling, and professional appearance."""

        chart_result = await self._call_llm(chart_prompt)
        
        return {
            "success": True,
            "chart_code": chart_result,
            "chart_type": chart_type,
            "title": title,
            "data_points": len(data),
            "accessibility_description": self._generate_chart_description(chart_type, data, title)
        }
    
    async def _create_diagram(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create diagram or flowchart"""
        description = task.get("description", "")
        diagram_type = task.get("diagram_type", "flowchart")
        elements = task.get("elements", [])
        relationships = task.get("relationships", [])
        
        diagram_prompt = f"""Create a {diagram_type} based on the following description:

Description: {description}
Elements: {elements}
Relationships: {relationships}

Generate:
1. TikZ code for LaTeX integration
2. Mermaid diagram syntax
3. ASCII art representation
4. Detailed description for text-based formats

Ensure the diagram is clear, professional, and follows academic standards."""

        diagram_result = await self._call_llm(diagram_prompt)
        
        return {
            "success": True,
            "diagram_code": diagram_result,
            "diagram_type": diagram_type,
            "elements_count": len(elements),
            "relationships_count": len(relationships)
        }
    
    async def _integrate_figures(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate figures into document structure"""
        figures = task.get("figures", [])
        document_context = task.get("document_context", {})
        placement_strategy = task.get("placement_strategy", "optimal")
        
        integrated_figures = []
        
        for i, figure in enumerate(figures):
            integration_prompt = f"""Integrate the following figure into an academic document:

Figure: {figure}
Document context: {document_context}
Placement strategy: {placement_strategy}

Generate:
1. Appropriate caption text
2. Figure placement recommendations
3. Cross-reference suggestions
4. Integration notes for the document

Ensure proper academic figure presentation and integration."""

            integration_result = await self._call_llm(integration_prompt)
            
            integrated_figures.append({
                "figure_id": f"fig_{i+1}",
                "original": figure,
                "integration": integration_result,
                "placement": self._determine_figure_placement(figure, document_context)
            })
        
        return {
            "success": True,
            "integrated_figures": integrated_figures,
            "total_figures": len(figures),
            "placement_strategy": placement_strategy
        }
    
    async def _create_comprehensive_visuals(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive visual package for document"""
        document_plan = task.get("document_plan", {})
        content_analysis = task.get("content_analysis", {})
        visual_requirements = task.get("visual_requirements", {})
        
        # Identify visual needs
        visual_needs = self._analyze_visual_needs(document_plan, content_analysis)
        
        # Create tables
        tables = []
        if visual_needs.get("tables_needed", 0) > 0:
            for i in range(visual_needs["tables_needed"]):
                table_task = {
                    "data": visual_requirements.get("table_data", []),
                    "table_type": "academic",
                    "caption": f"Table {i+1}: Data Summary"
                }
                table_result = await self._create_table(table_task)
                tables.append(table_result)
        
        # Create charts
        charts = []
        if visual_needs.get("charts_needed", 0) > 0:
            for i in range(visual_needs["charts_needed"]):
                chart_task = {
                    "data": visual_requirements.get("chart_data", []),
                    "chart_type": "bar_chart",
                    "title": f"Figure {i+1}: Data Visualization"
                }
                chart_result = await self._generate_chart(chart_task)
                charts.append(chart_result)
        
        # Create diagrams
        diagrams = []
        if visual_needs.get("diagrams_needed", 0) > 0:
            for i in range(visual_needs["diagrams_needed"]):
                diagram_task = {
                    "description": f"Process diagram {i+1}",
                    "diagram_type": "flowchart"
                }
                diagram_result = await self._create_diagram(diagram_task)
                diagrams.append(diagram_result)
        
        return {
            "success": True,
            "visual_package": {
                "tables": tables,
                "charts": charts,
                "diagrams": diagrams,
                "visual_summary": visual_needs
            },
            "total_visuals": len(tables) + len(charts) + len(diagrams)
        }
    
    def _parse_table_result(self, table_result: str, data: List, headers: List, caption: str) -> Dict[str, Any]:
        """Parse table generation result into structured format"""
        
        # Generate different table formats
        html_table = self._generate_html_table(data, headers, caption)
        latex_table = self._generate_latex_table(data, headers, caption)
        markdown_table = self._generate_markdown_table(data, headers, caption)
        
        return {
            "html": html_table,
            "latex": latex_table,
            "markdown": markdown_table,
            "csv": self._generate_csv_table(data, headers),
            "caption": caption,
            "accessibility_description": self._generate_table_description(data, headers, caption)
        }
    
    def _generate_html_table(self, data: List, headers: List, caption: str) -> str:
        """Generate HTML table"""
        html = f'<table class="academic-table">\n'
        
        if caption:
            html += f'  <caption>{caption}</caption>\n'
        
        if headers:
            html += '  <thead>\n    <tr>\n'
            for header in headers:
                html += f'      <th>{header}</th>\n'
            html += '    </tr>\n  </thead>\n'
        
        html += '  <tbody>\n'
        for row in data:
            html += '    <tr>\n'
            for cell in row:
                html += f'      <td>{cell}</td>\n'
            html += '    </tr>\n'
        html += '  </tbody>\n</table>'
        
        return html
    
    def _generate_latex_table(self, data: List, headers: List, caption: str) -> str:
        """Generate LaTeX table"""
        if not data:
            return ""
        
        num_cols = len(data[0]) if data else len(headers)
        col_spec = 'c' * num_cols
        
        latex = f'\\begin{{table}}[htbp]\n'
        latex += f'  \\centering\n'
        if caption:
            latex += f'  \\caption{{{caption}}}\n'
        latex += f'  \\begin{{tabular}}{{{col_spec}}}\n'
        latex += f'    \\toprule\n'
        
        if headers:
            latex += '    ' + ' & '.join(headers) + ' \\\\\n'
            latex += '    \\midrule\n'
        
        for row in data:
            latex += '    ' + ' & '.join(str(cell) for cell in row) + ' \\\\\n'
        
        latex += '    \\bottomrule\n'
        latex += '  \\end{tabular}\n'
        latex += '\\end{table}'
        
        return latex
    
    def _generate_markdown_table(self, data: List, headers: List, caption: str) -> str:
        """Generate Markdown table"""
        if not data and not headers:
            return ""
        
        markdown = ""
        if caption:
            markdown += f"*{caption}*\n\n"
        
        if headers:
            markdown += "| " + " | ".join(headers) + " |\n"
            markdown += "|" + "---|" * len(headers) + "\n"
        
        for row in data:
            markdown += "| " + " | ".join(str(cell) for cell in row) + " |\n"
        
        return markdown
    
    def _generate_csv_table(self, data: List, headers: List) -> str:
        """Generate CSV format"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        if headers:
            writer.writerow(headers)
        
        for row in data:
            writer.writerow(row)
        
        return output.getvalue()
    
    def _generate_chart_description(self, chart_type: str, data: List, title: str) -> str:
        """Generate accessibility description for chart"""
        data_points = len(data) if data else 0
        
        description = f"A {chart_type.replace('_', ' ')} titled '{title}' showing {data_points} data points. "
        
        if chart_type == "bar_chart":
            description += "The chart displays categorical data with bars representing different values."
        elif chart_type == "line_chart":
            description += "The chart shows trends over time or continuous data with connected points."
        elif chart_type == "pie_chart":
            description += "The chart displays proportional data as slices of a circular pie."
        
        return description
    
    def _generate_table_description(self, data: List, headers: List, caption: str) -> str:
        """Generate accessibility description for table"""
        rows = len(data)
        cols = len(headers) if headers else (len(data[0]) if data else 0)
        
        description = f"A table with {rows} rows and {cols} columns"
        if caption:
            description += f" titled '{caption}'"
        description += ". "
        
        if headers:
            description += f"Column headers are: {', '.join(headers)}."
        
        return description
    
    def _analyze_visual_needs(self, document_plan: Dict, content_analysis: Dict) -> Dict[str, Any]:
        """Analyze document to determine visual needs"""
        
        # Simple heuristic-based analysis
        word_count = content_analysis.get("word_count", 0)
        document_type = document_plan.get("document_type", "article")
        
        # Base visual needs on document type and length
        if document_type == "thesis":
            tables_needed = max(3, word_count // 5000)
            charts_needed = max(2, word_count // 7000)
            diagrams_needed = max(1, word_count // 10000)
        elif document_type == "research_paper":
            tables_needed = max(2, word_count // 3000)
            charts_needed = max(1, word_count // 4000)
            diagrams_needed = max(1, word_count // 8000)
        else:
            tables_needed = max(1, word_count // 4000)
            charts_needed = max(1, word_count // 5000)
            diagrams_needed = word_count // 10000
        
        return {
            "tables_needed": min(tables_needed, 5),  # Cap at reasonable numbers
            "charts_needed": min(charts_needed, 3),
            "diagrams_needed": min(diagrams_needed, 2),
            "total_visuals": min(tables_needed + charts_needed + diagrams_needed, 8)
        }
    
    def _determine_figure_placement(self, figure: Dict, document_context: Dict) -> str:
        """Determine optimal figure placement"""
        figure_type = figure.get("type", "unknown")
        
        if figure_type in ["table", "data_table"]:
            return "after_methodology"
        elif figure_type in ["chart", "graph", "plot"]:
            return "in_results"
        elif figure_type in ["diagram", "flowchart"]:
            return "in_methodology"
        else:
            return "contextual"

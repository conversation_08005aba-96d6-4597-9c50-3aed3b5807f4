#!/usr/bin/env python3
"""
Simple WebSocket test to isolate the issue
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
import uvicorn
import asyncio

# Create a minimal FastAPI app with WebSocket
app = Fast<PERSON>I(title="WebSocket Test")

@app.get("/")
async def root():
    return {"message": "WebSocket Test Server", "websocket": "/ws/test"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """Simple WebSocket endpoint"""
    print(f"WebSocket connection attempt from client: {client_id}")
    await websocket.accept()
    print(f"WebSocket connected: {client_id}")
    
    try:
        while True:
            data = await websocket.receive_json()
            print(f"Received from {client_id}: {data}")
            
            # Echo the message back
            response = {
                "type": "echo",
                "original": data,
                "client_id": client_id,
                "message": f"Echo from server: {data.get('message', 'No message')}"
            }
            await websocket.send_json(response)
            
    except WebSocketDisconnect:
        print(f"WebSocket disconnected: {client_id}")
    except Exception as e:
        print(f"WebSocket error for {client_id}: {e}")

if __name__ == "__main__":
    print("Starting simple WebSocket test server on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")

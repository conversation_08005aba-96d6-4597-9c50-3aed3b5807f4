"""
ASCAES Chat API Routes
Handles chat conversations and message management
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import uuid

from core.database import get_db, Conversation, Message
from core.logging_config import get_logger
from services.chat_service import ChatService
from services.chat_handler import chat_handler
from services.large_document_service import LargeDocumentService

logger = get_logger(__name__)
router = APIRouter()
chat_service = ChatService()
large_doc_service = LargeDocumentService()

# Pydantic models
class ConversationCreate(BaseModel):
    title: str
    user_id: Optional[str] = None

class ConversationResponse(BaseModel):
    id: int
    title: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    message_count: int = 0

class MessageCreate(BaseModel):
    conversation_id: int
    content: str
    role: str = "user"
    message_type: str = "text"

class MessageResponse(BaseModel):
    id: int
    conversation_id: int
    role: str
    content: str
    timestamp: datetime
    message_type: str

# New models for natural language document generation
class NaturalLanguageMessage(BaseModel):
    message: str
    conversation_id: Optional[int] = None

class DocumentGenerationResponse(BaseModel):
    response: str
    type: str
    session_id: str
    conversation_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None
    action: Optional[str] = None

@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get list of conversations"""
    try:
        conversations = db.query(Conversation)\
            .filter(Conversation.is_active == True)\
            .order_by(Conversation.updated_at.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
        
        # Add message count for each conversation
        result = []
        for conv in conversations:
            message_count = db.query(Message)\
                .filter(Message.conversation_id == conv.id)\
                .count()
            
            result.append(ConversationResponse(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                is_active=conv.is_active,
                message_count=message_count
            ))
        
        return result
        
    except Exception as e:
        logger.error(f"Error fetching conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversations")

@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(
    conversation: ConversationCreate,
    db: Session = Depends(get_db)
):
    """Create new conversation"""
    try:
        db_conversation = Conversation(
            title=conversation.title,
            user_id=conversation.user_id
        )
        db.add(db_conversation)
        db.commit()
        db.refresh(db_conversation)
        
        return ConversationResponse(
            id=db_conversation.id,
            title=db_conversation.title,
            created_at=db_conversation.created_at,
            updated_at=db_conversation.updated_at,
            is_active=db_conversation.is_active,
            message_count=0
        )
        
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create conversation")

@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: int,
    db: Session = Depends(get_db)
):
    """Get specific conversation"""
    try:
        conversation = db.query(Conversation)\
            .filter(Conversation.id == conversation_id)\
            .first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        message_count = db.query(Message)\
            .filter(Message.conversation_id == conversation_id)\
            .count()
        
        return ConversationResponse(
            id=conversation.id,
            title=conversation.title,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at,
            is_active=conversation.is_active,
            message_count=message_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversation")

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    db: Session = Depends(get_db)
):
    """Delete conversation (soft delete)"""
    try:
        conversation = db.query(Conversation)\
            .filter(Conversation.id == conversation_id)\
            .first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        conversation.is_active = False
        db.commit()
        
        return {"message": "Conversation deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete conversation")

@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_messages(
    conversation_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    db: Session = Depends(get_db)
):
    """Get messages for a conversation"""
    try:
        # Verify conversation exists
        conversation = db.query(Conversation)\
            .filter(Conversation.id == conversation_id)\
            .first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        messages = db.query(Message)\
            .filter(Message.conversation_id == conversation_id)\
            .order_by(Message.timestamp.asc())\
            .offset(skip)\
            .limit(limit)\
            .all()
        
        return [MessageResponse(
            id=msg.id,
            conversation_id=msg.conversation_id,
            role=msg.role,
            content=msg.content,
            timestamp=msg.timestamp,
            message_type=msg.message_type
        ) for msg in messages]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching messages for conversation {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch messages")

@router.post("/messages", response_model=MessageResponse)
async def create_message(
    message: MessageCreate,
    db: Session = Depends(get_db)
):
    """Create new message"""
    try:
        # Verify conversation exists
        conversation = db.query(Conversation)\
            .filter(Conversation.id == message.conversation_id)\
            .first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        db_message = Message(
            conversation_id=message.conversation_id,
            role=message.role,
            content=message.content,
            message_type=message.message_type
        )
        db.add(db_message)
        
        # Update conversation timestamp
        conversation.updated_at = datetime.now()
        
        db.commit()
        db.refresh(db_message)
        
        return MessageResponse(
            id=db_message.id,
            conversation_id=db_message.conversation_id,
            role=db_message.role,
            content=db_message.content,
            timestamp=db_message.timestamp,
            message_type=db_message.message_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create message")

# Natural Language Document Generation Endpoints
@router.post("/generate", response_model=DocumentGenerationResponse)
async def generate_document_from_message(
    message: NaturalLanguageMessage,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Generate document from natural language message"""
    try:
        # Process the message with chat handler
        session_id = str(uuid.uuid4())

        response = await chat_handler.process_message(
            message=message.message,
            session_id=session_id
        )

        # Handle conversation creation/update
        conversation_id = message.conversation_id
        if not conversation_id:
            # Create new conversation
            title = response.get('details', {}).get('title', 'Document Generation')[:100]
            db_conversation = Conversation(title=title)
            db.add(db_conversation)
            db.commit()
            db.refresh(db_conversation)
            conversation_id = db_conversation.id

        # Save user message
        user_message = Message(
            conversation_id=conversation_id,
            role="user",
            content=message.message,
            message_type="text"
        )
        db.add(user_message)

        # Save assistant response
        assistant_message = Message(
            conversation_id=conversation_id,
            role="assistant",
            content=response.get('message', 'I understand your request.'),
            message_type="document_generation"
        )
        db.add(assistant_message)
        db.commit()

        # Handle document generation if confirmed
        response_type = response.get('type', 'general')
        if response_type in ['large_document_request', 'document_request']:
            # Auto-confirm for demo purposes - in production, you'd wait for user confirmation
            request = response.get('request', {})

            if response_type == 'large_document_request':
                # Start large document generation
                background_tasks.add_task(
                    _run_large_document_generation,
                    session_id,
                    request,
                    conversation_id,
                    db
                )

                target_pages = request.get('target_pages', 100)
                estimate = large_doc_service.get_estimated_time(target_pages)

                response_message = f"""
🚀 **Large Document Generation Started!**

📄 **Document Details:**
• Title: {request.get('title', 'Academic Document')}
• Pages: {target_pages}
• Type: {request.get('document_type', 'research_paper').replace('_', ' ').title()}
• Style: {request.get('writing_style', 'analytical').title()}

⏱️ **Estimated Time:** {estimate['estimated_minutes']:.1f} minutes

✨ **Features:**
• AI detection avoidance
• Multiple humanization passes
• Quality assurance checks
• Multiple output formats

📊 **Progress:** Check back in a few minutes for your completed document!
                """.strip()

                # Update assistant message with generation details
                assistant_message.content = response_message
                db.commit()

        return DocumentGenerationResponse(
            response=response.get('message', 'I understand your request.'),
            type=response_type,
            session_id=session_id,
            conversation_id=conversation_id,
            metadata=response.get('details'),
            action=response.get('action')
        )

    except Exception as e:
        logger.error(f"Error processing document generation message: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/generation/{session_id}")
async def get_generation_status(session_id: str):
    """Get status of document generation"""
    try:
        # Check active generations from agents module
        from api.agents import active_generations

        if session_id not in active_generations:
            raise HTTPException(status_code=404, detail="Generation session not found")

        generation = active_generations[session_id]

        return {
            'session_id': session_id,
            'status': generation.get('status', 'unknown'),
            'progress': generation.get('progress', 0),
            'phase': generation.get('phase', 'unknown'),
            'message': generation.get('message', ''),
            'target_pages': generation.get('target_pages', 0),
            'error': generation.get('error')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting generation status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background task for large document generation
async def _run_large_document_generation(session_id: str, request: Dict[str, Any],
                                       conversation_id: int, db: Session):
    """Run large document generation and update conversation"""
    try:
        from api.agents import active_generations

        # Initialize tracking
        active_generations[session_id] = {
            "status": "running",
            "progress": 0,
            "phase": "initialization",
            "request": request,
            "result": None,
            "error": None,
            "document_type": "large_document",
            "target_pages": request.get('target_pages', 100)
        }

        # Progress callback
        async def progress_callback(progress_data):
            if session_id in active_generations:
                active_generations[session_id].update({
                    "progress": progress_data.get("progress", 0),
                    "phase": progress_data.get("phase", "unknown"),
                    "message": progress_data.get("message", "")
                })

        # Run generation
        result = await large_doc_service.generate_large_document(
            request=request,
            session_id=session_id,
            progress_callback=progress_callback
        )

        # Update tracking
        active_generations[session_id].update({
            "status": "completed",
            "progress": 100,
            "phase": "completed",
            "result": result
        })

        # Add completion message to conversation
        if result.get('success', False):
            metadata = result.get('generation_metadata', {})
            completion_message = f"""
✅ **Document Generation Completed!**

📊 **Results:**
• Pages Generated: {metadata.get('actual_pages', 0)}
• Words: {metadata.get('actual_words', 0):,}
• Quality Score: {metadata.get('quality_score', 0):.3f}
• AI Detection Score: {metadata.get('ai_detection_score', 1):.3f}
• Generation Time: {metadata.get('generation_time', 0):.1f} seconds

📁 **Available Formats:** {', '.join(result.get('formats_available', []))}

Your document is ready for download!
            """.strip()
        else:
            completion_message = f"❌ Document generation failed: {result.get('error', 'Unknown error')}"

        # Save completion message to database
        completion_msg = Message(
            conversation_id=conversation_id,
            role="assistant",
            content=completion_message,
            message_type="generation_complete"
        )
        db.add(completion_msg)
        db.commit()

        logger.info(f"Large document generation completed: {session_id}")

    except Exception as e:
        # Update with error
        if session_id in active_generations:
            active_generations[session_id].update({
                "status": "failed",
                "error": str(e)
            })

        # Save error message to database
        error_message = Message(
            conversation_id=conversation_id,
            role="assistant",
            content=f"❌ Document generation failed: {str(e)}",
            message_type="generation_error"
        )
        db.add(error_message)
        db.commit()

        logger.error(f"Large document generation failed: {session_id} - {e}")

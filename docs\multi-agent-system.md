# ASCAES Multi-Agent System

## Overview

ASCAES employs a sophisticated multi-agent system with 8 specialized agents working in coordination to generate high-quality academic documents. Each agent has specific capabilities and responsibilities, working together through a centralized coordinator.

## Agent Architecture

### Base Agent Framework

All agents inherit from the `BaseAgent` class which provides:

- **State Management**: Tracks agent execution states (IDLE, TH<PERSON><PERSON><PERSON>, WOR<PERSON><PERSON>, <PERSON><PERSON><PERSON>, CO<PERSON><PERSON><PERSON>, ERROR)
- **Performance Metrics**: Monitors task completion, execution time, and success rates
- **Health Monitoring**: Provides health checks and status reporting
- **Inter-Agent Communication**: Message passing and coordination capabilities
- **Error Handling**: Robust error handling and recovery mechanisms

### Agent Capabilities System

Each agent defines its capabilities using the `AgentCapability` model:

```python
AgentCapability(
    name="capability_name",
    description="What this capability does",
    input_types=["input_type1", "input_type2"],
    output_types=["output_type1", "output_type2"],
    dependencies=["required_agent1", "required_agent2"]
)
```

## The 8 Specialized Agents

### 1. Planning Agent (`planning_agent`)

**Purpose**: Creates document structure, outlines, and planning strategies

**Capabilities**:
- Document structure planning
- Section planning with content guidelines
- Research requirement identification
- Timeline estimation

**Key Features**:
- Supports multiple document types (research papers, theses, reports)
- Template-based structure generation
- Word count allocation per section
- Citation requirement planning

**Example Usage**:
```python
task = {
    "type": "document_planning",
    "requirements": {
        "title": "AI Ethics in Healthcare",
        "document_type": "research_paper",
        "writing_style": "analytical",
        "target_length": 5000,
        "citation_style": "APA"
    }
}
```

### 2. Research Agent (`research_agent`)

**Purpose**: Information gathering, source analysis, and citation management

**Capabilities**:
- Source discovery from uploaded documents
- Content analysis and summarization
- Citation generation in multiple formats
- Fact verification and cross-referencing

**Key Features**:
- Vector database integration for document search
- Support for APA, MLA, Chicago, IEEE, Harvard citation styles
- Quality scoring for sources
- Comprehensive research synthesis

**Example Usage**:
```python
task = {
    "type": "comprehensive_research",
    "topic": "Machine Learning Ethics",
    "keywords": ["AI", "ethics", "bias", "fairness"],
    "max_sources": 15
}
```

### 3. Writing Agent (`writing_agent`)

**Purpose**: Content generation across 8 distinct academic writing styles

**Capabilities**:
- Content generation in specific writing styles
- Section-by-section writing
- Style adaptation and conversion
- Content refinement and improvement

**Writing Styles Supported**:
1. **Analytical**: Critical examination and data interpretation
2. **Instructional**: Teaching and step-by-step guidance
3. **Reporting**: Factual documentation and findings
4. **Argumentative**: Persuasive reasoning and position papers
5. **Exploratory**: Investigation and hypothesis development
6. **Descriptive**: Technical specifications and details
7. **Narrative**: Case studies and story-driven explanations
8. **Schematic**: Reference materials and systematic documentation

**Example Usage**:
```python
task = {
    "type": "content_generation",
    "writing_style": "analytical",
    "plan": document_plan,
    "research_data": research_results,
    "requirements": {
        "target_length": 3000,
        "citation_style": "APA",
        "academic_level": "graduate"
    }
}
```

### 4. LaTeX Agent (`latex_agent`)

**Purpose**: LaTeX formatting, mathematical notation, and academic document structure

**Capabilities**:
- Complete document formatting in LaTeX
- Mathematical notation conversion
- Table and figure formatting
- Bibliography management
- Multi-format output generation

**Key Features**:
- Support for multiple document classes (article, report, book, thesis)
- Advanced mathematical environments
- Professional academic formatting
- Citation style integration

**Example Usage**:
```python
task = {
    "type": "complete_latex_generation",
    "content": "Document content...",
    "requirements": {
        "document_type": "article",
        "citation_style": "IEEE",
        "include_math": True,
        "include_tables": True
    }
}
```

### 5. Visual Agent (`visual_agent`)

**Purpose**: Creating visual elements, figures, tables, and diagrams

**Capabilities**:
- Table creation from data
- Chart and graph generation
- Diagram and flowchart creation
- Figure integration and positioning
- Visual layout design

**Key Features**:
- Multiple table styles (academic, minimal, formal)
- Chart types: bar, line, scatter, pie, box plot, heatmap
- TikZ/PGFPlots integration for LaTeX
- Accessibility descriptions for visual elements

**Example Usage**:
```python
task = {
    "type": "comprehensive_visual",
    "document_plan": plan,
    "visual_requirements": {
        "table_data": [[1, 2, 3], [4, 5, 6]],
        "chart_data": [10, 20, 30, 40],
        "chart_type": "bar_chart"
    }
}
```

### 6. Quality Agent (`quality_agent`)

**Purpose**: Quality assurance, grammar checking, and academic standards validation

**Capabilities**:
- Grammar and language checking
- Academic standards validation
- Citation verification
- Coherence and flow analysis
- Comprehensive quality assessment

**Quality Metrics**:
- Readability score
- Academic tone assessment
- Citation quality and consistency
- Structure and organization
- Overall compliance score

**Example Usage**:
```python
task = {
    "type": "comprehensive_quality_check",
    "content": "Document content...",
    "requirements": {
        "writing_style": "analytical",
        "citation_style": "APA",
        "quality_criteria": {"min_score": 0.8}
    }
}
```

### 7. Humanizer Agent (`humanizer_agent`)

**Purpose**: Making AI-generated content more natural and human-like

**Capabilities**:
- Sentence variation and structure improvement
- Vocabulary enhancement
- Flow optimization
- AI pattern removal
- Natural language refinement

**Humanization Strategies**:
- Sentence length and structure variation
- Synonym replacement and vocabulary enhancement
- Transition improvement
- Removal of repetitive AI patterns
- Tone refinement while maintaining formality

**Example Usage**:
```python
task = {
    "type": "comprehensive_humanization",
    "content": "AI-generated content...",
    "requirements": {
        "target_variety": 0.8,
        "enhancement_level": "moderate",
        "maintain_academic_tone": True
    }
}
```

### 8. Assembly Agent (`assembly_agent`)

**Purpose**: Final document assembly, formatting, and output generation

**Capabilities**:
- Document assembly from components
- Multi-format generation (PDF, LaTeX, RTF, TXT)
- Quality validation
- Metadata generation
- Final packaging

**Output Formats**:
- **PDF**: Professional formatting for submission
- **LaTeX**: Source code for academic publishing
- **RTF**: Compatible with word processors
- **TXT**: Plain text for universal compatibility

**Example Usage**:
```python
task = {
    "type": "complete_assembly",
    "components": all_agent_outputs,
    "requirements": {
        "output_formats": ["pdf", "latex", "txt"],
        "quality_criteria": {"min_score": 0.8}
    }
}
```

## Agent Coordinator

The `AgentCoordinator` orchestrates the entire multi-agent system:

### Coordination Process

1. **Planning Phase** (10%): Document structure and requirements
2. **Research Phase** (25%): Information gathering and source analysis
3. **Writing Phase** (50%): Content generation in specified style
4. **Visual Phase** (60%): Visual elements creation
5. **LaTeX Phase** (70%): Formatting and mathematical notation
6. **Quality Phase** (80%): Quality assurance and validation
7. **Humanization Phase** (90%): Natural language refinement
8. **Assembly Phase** (100%): Final document compilation

### Key Features

- **Progress Tracking**: Real-time progress updates with callbacks
- **Error Handling**: Robust error recovery and partial result preservation
- **Performance Monitoring**: Comprehensive metrics and health monitoring
- **Session Management**: Multiple concurrent generation sessions
- **Inter-Agent Communication**: Message passing and coordination

## API Integration

### Document Generation Endpoint

```http
POST /api/agents/generate
```

**Request Body**:
```json
{
    "title": "AI Ethics in Healthcare",
    "document_type": "research_paper",
    "writing_style": "analytical",
    "target_length": 5000,
    "citation_style": "APA",
    "output_formats": ["pdf", "latex", "txt"],
    "keywords": ["AI", "ethics", "healthcare"],
    "field": "computer_science"
}
```

**Response**:
```json
{
    "session_id": "uuid-string",
    "status": "started",
    "message": "Document generation started successfully"
}
```

### Progress Monitoring

```http
GET /api/agents/generate/{session_id}
```

**Response**:
```json
{
    "status": "running",
    "progress": 65,
    "phase": "formatting",
    "coordinator_progress": 65,
    "coordinator_phase": "formatting"
}
```

### System Status

```http
GET /api/agents/status
```

**Response**:
```json
{
    "coordinator_status": "operational",
    "active_sessions": 2,
    "agent_statuses": {
        "planning": {"state": "idle", "healthy": true},
        "writing": {"state": "working", "healthy": true}
    },
    "coordination_metrics": {
        "sessions_completed": 15,
        "average_generation_time": 45.2,
        "success_rate": 0.95
    }
}
```

## Performance Optimization

### Memory Management

- **8GB RAM Configuration**: Optimized for lightweight models
- **Agent State Management**: Efficient memory usage during coordination
- **Garbage Collection**: Automatic cleanup of completed sessions

### Concurrent Processing

- **Parallel Agent Execution**: Where dependencies allow
- **Background Processing**: Non-blocking document generation
- **Queue Management**: Efficient task scheduling

### Caching and Optimization

- **Result Caching**: Cache intermediate results for efficiency
- **Model Optimization**: Optimized prompts and context management
- **Resource Pooling**: Efficient resource utilization

## Error Handling and Recovery

### Agent-Level Error Handling

- **Graceful Degradation**: Continue with partial results
- **Retry Mechanisms**: Automatic retry for transient failures
- **Fallback Strategies**: Alternative approaches when primary methods fail

### System-Level Recovery

- **Session Recovery**: Resume interrupted generations
- **Health Monitoring**: Continuous health checks
- **Automatic Restart**: Recovery from agent failures

## Testing and Validation

### Unit Tests

- Individual agent functionality
- Capability validation
- Error handling scenarios

### Integration Tests

- Multi-agent coordination
- End-to-end document generation
- Performance benchmarks

### Quality Assurance

- Output quality validation
- Academic standard compliance
- User acceptance testing

## Future Enhancements

### Planned Improvements

1. **Advanced AI Models**: Integration with larger models (32B+)
2. **Specialized Agents**: Domain-specific agents for different fields
3. **Learning Capabilities**: Adaptive improvement based on feedback
4. **Collaborative Features**: Multi-user document collaboration
5. **Advanced Analytics**: Detailed performance and quality analytics

### Scalability Considerations

- **Distributed Processing**: Multi-machine coordination
- **Cloud Integration**: Hybrid local/cloud processing
- **Model Optimization**: Efficient model serving and caching

This multi-agent system represents a sophisticated approach to academic document generation, providing high-quality, consistent output while maintaining the flexibility to adapt to different requirements and writing styles.

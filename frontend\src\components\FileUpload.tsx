import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface FileUploadProps {
  onUpload: (files: File[]) => void
  maxFiles?: number
  maxSize?: number
  acceptedTypes?: string[]
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  maxFiles = 5,
  maxSize = 50 * 1024 * 1024, // 50MB
  acceptedTypes = ['.pdf', '.txt', '.docx', '.md', '.tex']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach((file) => {
      const { errors } = file
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          toast.error(`File ${file.file.name} is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`)
        } else if (error.code === 'file-invalid-type') {
          toast.error(`File ${file.file.name} has invalid type. Accepted types: ${acceptedTypes.join(', ')}`)
        } else {
          toast.error(`Error with file ${file.file.name}: ${error.message}`)
        }
      })
    })

    // Handle accepted files
    if (acceptedFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...acceptedFiles])
      
      // Simulate upload progress
      acceptedFiles.forEach((file) => {
        const fileId = `${file.name}-${file.size}-${file.lastModified}`
        let progress = 0
        
        const interval = setInterval(() => {
          progress += Math.random() * 30
          if (progress >= 100) {
            progress = 100
            clearInterval(interval)
          }
          
          setUploadProgress(prev => ({
            ...prev,
            [fileId]: progress
          }))
        }, 200)
      })
      
      toast.success(`${acceptedFiles.length} file(s) uploaded successfully`)
    }
  }, [maxSize, acceptedTypes])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles,
    maxSize,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>)
  })

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleUpload = () => {
    if (uploadedFiles.length > 0) {
      onUpload(uploadedFiles)
      setUploadedFiles([])
      setUploadProgress({})
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'pdf':
        return '📄'
      case 'txt':
      case 'md':
        return '📝'
      case 'docx':
        return '📘'
      case 'tex':
        return '📊'
      default:
        return '📎'
    }
  }

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`upload-area ${isDragActive ? 'dragover' : ''}`}
      >
        <input {...getInputProps()} />
        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        {isDragActive ? (
          <p className="text-primary-600 font-medium">Drop files here...</p>
        ) : (
          <div className="text-center">
            <p className="text-gray-600 font-medium mb-1">
              Drag & drop files here, or click to select
            </p>
            <p className="text-sm text-gray-500">
              Supports: {acceptedTypes.join(', ')} (max {maxSize / (1024 * 1024)}MB each)
            </p>
          </div>
        )}
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Uploaded Files:</h4>
          {uploadedFiles.map((file, index) => {
            const fileId = `${file.name}-${file.size}-${file.lastModified}`
            const progress = uploadProgress[fileId] || 0
            const isComplete = progress >= 100

            return (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl">{getFileIcon(file.name)}</div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <div className="flex items-center space-x-2">
                      {isComplete ? (
                        <CheckCircle className="w-4 h-4 text-success-600" />
                      ) : (
                        <div className="w-4 h-4 spinner" />
                      )}
                      <button
                        onClick={() => removeFile(index)}
                        className="p-1 rounded hover:bg-gray-200 transition-colors"
                      >
                        <X className="w-4 h-4 text-gray-500" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {Math.round(progress)}%
                    </p>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="progress-bar mt-2">
                    <div 
                      className="progress-fill"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
          
          {/* Upload Button */}
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              className="btn-primary"
              disabled={uploadedFiles.length === 0}
            >
              Process {uploadedFiles.length} File{uploadedFiles.length !== 1 ? 's' : ''}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

import React from 'react'
import { Bo<PERSON> } from 'lucide-react'

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex justify-start">
      <div className="max-w-4xl w-full">
        <div className="flex items-start space-x-3">
          {/* Avatar */}
          <div className="flex-shrink-0 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white">
            <Bot className="w-5 h-5" />
          </div>

          {/* Typing Animation */}
          <div className="message-bubble assistant">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">ASCAES is thinking</span>
              <div className="typing-indicator">
                <div 
                  className="typing-dot" 
                  style={{ '--delay': '0s' } as React.CSSProperties}
                />
                <div 
                  className="typing-dot" 
                  style={{ '--delay': '0.2s' } as React.CSSProperties}
                />
                <div 
                  className="typing-dot" 
                  style={{ '--delay': '0.4s' } as React.CSSProperties}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

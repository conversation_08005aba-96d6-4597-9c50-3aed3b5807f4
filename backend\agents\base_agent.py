"""
Base Agent Class
Foundation for all specialized agents in the ASCAES system
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from pydantic import BaseModel

from core.logging_config import get_logger
from services.ollama_service import ollama_service

class AgentState(Enum):
    """Agent execution states"""
    IDLE = "idle"
    THINKING = "thinking"
    WORKING = "working"
    WAITING = "waiting"
    COMPLETE = "complete"
    ERROR = "error"

@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender: str = ""
    recipient: str = ""
    message_type: str = "info"
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = 1  # 1=low, 5=high

class AgentCapability(BaseModel):
    """Agent capability definition"""
    name: str
    description: str
    input_types: List[str]
    output_types: List[str]
    dependencies: List[str] = []

class BaseAgent(ABC):
    """Base class for all ASCAES agents"""
    
    def __init__(self, agent_id: str, name: str, description: str):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.state = AgentState.IDLE
        self.logger = get_logger(f"agent.{agent_id}")
        
        # Communication
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.subscribers: List[Callable] = []
        
        # Execution tracking
        self.current_task: Optional[Dict[str, Any]] = None
        self.execution_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = {
            "tasks_completed": 0,
            "average_execution_time": 0.0,
            "success_rate": 1.0,
            "last_execution": None
        }
        
        # Configuration
        self.config: Dict[str, Any] = self._get_default_config()
        self.capabilities: List[AgentCapability] = self._define_capabilities()
        
        self.logger.info(f"Agent {self.name} ({self.agent_id}) initialized")
    
    @abstractmethod
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define agent capabilities"""
        pass
    
    @abstractmethod
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the main agent task"""
        pass
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default agent configuration"""
        return {
            "max_retries": 3,
            "timeout": 300,  # 5 minutes
            "model": "qwen2:7b-instruct",
            "temperature": 0.7,
            "max_tokens": 4096
        }
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task with error handling and metrics tracking"""
        task_id = task.get("id", str(uuid.uuid4()))
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Starting task execution: {task_id}")
            self.state = AgentState.WORKING
            self.current_task = task
            
            # Execute the task
            result = await self._execute_task(task)
            
            # Track success
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics(True, execution_time)
            
            self.state = AgentState.COMPLETE
            self.current_task = None
            
            # Add execution metadata
            result["execution_metadata"] = {
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "task_id": task_id,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            self.logger.info(f"Task completed successfully: {task_id} ({execution_time:.2f}s)")
            return result
            
        except Exception as e:
            # Track failure
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics(False, execution_time)
            
            self.state = AgentState.ERROR
            self.current_task = None
            
            error_result = {
                "success": False,
                "error": str(e),
                "execution_metadata": {
                    "agent_id": self.agent_id,
                    "agent_name": self.name,
                    "task_id": task_id,
                    "execution_time": execution_time,
                    "timestamp": datetime.now().isoformat(),
                    "success": False
                }
            }
            
            self.logger.error(f"Task failed: {task_id} - {e}")
            return error_result
    
    async def send_message(self, recipient: str, message_type: str, content: Dict[str, Any], priority: int = 1):
        """Send message to another agent"""
        message = AgentMessage(
            sender=self.agent_id,
            recipient=recipient,
            message_type=message_type,
            content=content,
            priority=priority
        )
        
        # Notify subscribers
        for subscriber in self.subscribers:
            await subscriber(message)
    
    async def receive_message(self, message: AgentMessage):
        """Receive message from another agent"""
        await self.message_queue.put(message)
    
    def subscribe_to_messages(self, callback: Callable):
        """Subscribe to agent messages"""
        self.subscribers.append(callback)
    
    async def _call_llm(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Call LLM with agent-specific configuration"""
        try:
            self.state = AgentState.THINKING
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = await ollama_service.chat(
                model=self.config["model"],
                messages=messages,
                options={
                    "temperature": self.config["temperature"],
                    "num_ctx": self.config["max_tokens"],
                    "num_predict": min(self.config["max_tokens"], 2048)
                }
            )
            
            return response.get("message", {}).get("content", "")
            
        except Exception as e:
            self.logger.error(f"LLM call failed: {e}")
            raise
        finally:
            self.state = AgentState.WORKING
    
    def _update_metrics(self, success: bool, execution_time: float):
        """Update performance metrics"""
        self.performance_metrics["tasks_completed"] += 1
        
        # Update average execution time
        current_avg = self.performance_metrics["average_execution_time"]
        task_count = self.performance_metrics["tasks_completed"]
        new_avg = ((current_avg * (task_count - 1)) + execution_time) / task_count
        self.performance_metrics["average_execution_time"] = new_avg
        
        # Update success rate
        if success:
            current_rate = self.performance_metrics["success_rate"]
            new_rate = ((current_rate * (task_count - 1)) + 1.0) / task_count
            self.performance_metrics["success_rate"] = new_rate
        else:
            current_rate = self.performance_metrics["success_rate"]
            new_rate = (current_rate * (task_count - 1)) / task_count
            self.performance_metrics["success_rate"] = new_rate
        
        self.performance_metrics["last_execution"] = datetime.now().isoformat()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "description": self.description,
            "state": self.state.value,
            "current_task": self.current_task,
            "capabilities": [cap.dict() for cap in self.capabilities],
            "performance_metrics": self.performance_metrics,
            "config": self.config,
            "queue_size": self.message_queue.qsize()
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update agent configuration"""
        self.config.update(new_config)
        self.logger.info(f"Configuration updated: {new_config}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform agent health check"""
        try:
            # Test LLM connectivity
            test_response = await self._call_llm("Test message", "Respond with 'OK'")
            llm_healthy = "ok" in test_response.lower()
            
            return {
                "agent_id": self.agent_id,
                "name": self.name,
                "healthy": llm_healthy and self.state != AgentState.ERROR,
                "state": self.state.value,
                "llm_connectivity": llm_healthy,
                "performance_metrics": self.performance_metrics,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "agent_id": self.agent_id,
                "name": self.name,
                "healthy": False,
                "error": str(e),
                "state": self.state.value,
                "timestamp": datetime.now().isoformat()
            }
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.agent_id}, name={self.name}, state={self.state.value})>"

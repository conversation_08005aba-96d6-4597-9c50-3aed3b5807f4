#!/usr/bin/env python3
"""
ASCAES - Simplified Main Application
Streamlined version with working WebSocket support
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import asyncio
from datetime import datetime
from typing import Dict
import uuid

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        print(f"✅ WebSocket connected: {client_id}")
        
        # Send welcome message
        await self.send_message(client_id, {
            "type": "system",
            "message": "Connected to ASCAES",
            "timestamp": datetime.now().isoformat()
        })
    
    async def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            print(f"🔌 WebSocket disconnected: {client_id}")
    
    async def send_message(self, client_id: str, message: dict):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                print(f"❌ Error sending message to {client_id}: {e}")
                await self.disconnect(client_id)
    
    async def handle_message(self, client_id: str, data: dict):
        message_type = data.get("type", "unknown")
        print(f"📨 Received {message_type} from {client_id}: {data}")
        
        if message_type == "ping":
            await self.send_message(client_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        elif message_type == "chat":
            # Echo the message back for now
            await self.send_message(client_id, {
                "type": "chat_response",
                "message": f"Echo: {data.get('message', '')}",
                "timestamp": datetime.now().isoformat()
            })

# Create FastAPI app
app = FastAPI(
    title="ASCAES - Simplified",
    description="Academic Scholarly Content & Analysis Expert System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager
ws_manager = SimpleWebSocketManager()

@app.get("/")
async def root():
    return {
        "message": "ASCAES Simplified API Server",
        "version": "1.0.0",
        "status": "running",
        "websocket": "/ws/{client_id}"
    }

@app.get("/api/health/")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """Simple WebSocket endpoint"""
    print(f"🔌 WebSocket connection attempt: {client_id}")
    
    try:
        await ws_manager.connect(websocket, client_id)
        
        while True:
            data = await websocket.receive_json()
            await ws_manager.handle_message(client_id, data)
            
    except WebSocketDisconnect:
        await ws_manager.disconnect(client_id)
    except Exception as e:
        print(f"❌ WebSocket error for {client_id}: {e}")
        await ws_manager.disconnect(client_id)

@app.post("/api/chat/generate")
async def generate_document(request: dict):
    """Simple document generation endpoint"""
    session_id = str(uuid.uuid4())
    
    return {
        "session_id": session_id,
        "type": "document_request",
        "response": f"I'll generate a document about '{request.get('message', 'your topic')}' for you.",
        "status": "started"
    }

@app.get("/api/agents/generate/{session_id}")
async def get_generation_status(session_id: str):
    """Simple generation status endpoint"""
    return {
        "session_id": session_id,
        "status": "completed",
        "progress": 100,
        "phase": "completed",
        "message": "Document generation completed successfully!"
    }

if __name__ == "__main__":
    print("🚀 Starting ASCAES Simplified Server...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

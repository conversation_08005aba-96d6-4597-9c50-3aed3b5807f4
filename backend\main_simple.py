#!/usr/bin/env python3
"""
ASCAES - Simplified Main Application
Streamlined version with working WebSocket support
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import asyncio
import re
from datetime import datetime
from typing import Dict, Any
import uuid

from core.logging_config import get_logger
from services.document_service import DocumentService

logger = get_logger(__name__)
document_service = DocumentService()

# Store active generation requests
generation_requests: Dict[str, Dict[str, Any]] = {}

async def process_document_generation(session_id: str):
    """Process document generation in background"""
    try:
        if session_id not in generation_requests:
            return

        request_data = generation_requests[session_id]

        # Update status
        request_data["status"] = "processing"
        request_data["progress"] = 10
        request_data["phase"] = "initializing"
        request_data["message"] = "Starting document generation..."

        # Generate document using document service
        result = await document_service.generate_document(request_data)

        # Update status
        request_data["status"] = "completed"
        request_data["progress"] = 100
        request_data["phase"] = "completed"
        request_data["message"] = "Document generation completed successfully!"
        request_data["document_id"] = result.get("id")
        request_data["download_url"] = f"/api/documents/download/{result.get('id')}"

        logger.info(f"Document generation completed for session {session_id}")

    except Exception as e:
        logger.error(f"Error in background document generation: {e}")
        if session_id in generation_requests:
            generation_requests[session_id].update({
                "status": "error",
                "progress": 0,
                "phase": "error",
                "message": f"Generation failed: {str(e)}"
            })

# Simple WebSocket manager
class SimpleWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        print(f"✅ WebSocket connected: {client_id}")
        
        # Send welcome message
        await self.send_message(client_id, {
            "type": "system",
            "message": "Connected to ASCAES",
            "timestamp": datetime.now().isoformat()
        })
    
    async def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            print(f"🔌 WebSocket disconnected: {client_id}")
    
    async def send_message(self, client_id: str, message: dict):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                print(f"❌ Error sending message to {client_id}: {e}")
                await self.disconnect(client_id)
    
    async def handle_message(self, client_id: str, data: dict):
        message_type = data.get("type", "unknown")
        print(f"📨 Received {message_type} from {client_id}: {data}")
        
        if message_type == "ping":
            await self.send_message(client_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
        elif message_type == "chat":
            # Echo the message back for now
            await self.send_message(client_id, {
                "type": "chat_response",
                "message": f"Echo: {data.get('message', '')}",
                "timestamp": datetime.now().isoformat()
            })

# Create FastAPI app
app = FastAPI(
    title="ASCAES - Simplified",
    description="Academic Scholarly Content & Analysis Expert System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager
ws_manager = SimpleWebSocketManager()

@app.get("/")
async def root():
    return {
        "message": "ASCAES Simplified API Server",
        "version": "1.0.0",
        "status": "running",
        "websocket": "/ws/{client_id}"
    }

@app.get("/api/health/")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """Simple WebSocket endpoint"""
    print(f"🔌 WebSocket connection attempt: {client_id}")
    
    try:
        await ws_manager.connect(websocket, client_id)
        
        while True:
            data = await websocket.receive_json()
            await ws_manager.handle_message(client_id, data)
            
    except WebSocketDisconnect:
        await ws_manager.disconnect(client_id)
    except Exception as e:
        print(f"❌ WebSocket error for {client_id}: {e}")
        await ws_manager.disconnect(client_id)

@app.post("/api/chat/generate")
async def generate_document(request: dict):
    """Real document generation endpoint"""
    try:
        session_id = str(uuid.uuid4())
        message = request.get('message', '')

        # Parse the message to extract document requirements
        import re

        # Extract word count (look for numbers followed by 'words' or 'page')
        word_match = re.search(r'(\d+)\s*(?:words?|page)', message.lower())
        target_words = int(word_match.group(1)) * 250 if word_match else 2000  # Assume 250 words per page

        # Check if user specified exact word count
        exact_word_match = re.search(r'(\d+)\s*words?', message.lower())
        if exact_word_match:
            target_words = int(exact_word_match.group(1))

        # Extract topic (everything after "about" or the main subject)
        topic_match = re.search(r'about\s+(.+?)(?:\.|$)', message, re.IGNORECASE)
        topic = topic_match.group(1).strip() if topic_match else message.replace('Generate', '').strip()

        # Extract style
        style_keywords = {
            'analytical': ['analytical', 'analysis', 'analyze'],
            'reporting': ['report', 'reporting', 'factual'],
            'instructional': ['instruction', 'guide', 'how-to'],
            'argumentative': ['argument', 'persuasive', 'debate']
        }

        writing_style = 'analytical'  # default
        for style, keywords in style_keywords.items():
            if any(keyword in message.lower() for keyword in keywords):
                writing_style = style
                break

        # Get settings from request (passed from frontend)
        settings_data = request.get('settings', {})
        max_tokens = settings_data.get('maxTokens', 5000)
        temperature = settings_data.get('temperature', 0.7)
        top_p = settings_data.get('topP', 0.9)

        # Store generation request for processing
        generation_requests[session_id] = {
            "title": topic,
            "document_type": "research_paper",
            "writing_style": writing_style,
            "target_length": target_words,
            "field": "computer_science",
            "author": "ASCAES Generated",
            "output_format": "pdf",
            "conversation_id": 1,
            "model": request.get('model', 'deepseek-r1:7b'),  # Use selected model
            "max_tokens": max_tokens,  # Pass max tokens setting
            "temperature": temperature,  # Pass temperature setting
            "top_p": top_p,  # Pass top_p setting
            "status": "started",
            "progress": 0
        }

        # Start background generation
        asyncio.create_task(process_document_generation(session_id))

        return {
            "session_id": session_id,
            "type": "document_request",
            "response": f"I'll generate a {target_words}-word document about '{topic}' for you using {writing_style} style.",
            "status": "started"
        }

    except Exception as e:
        logger.error(f"Error in document generation: {e}")
        return {
            "session_id": str(uuid.uuid4()),
            "type": "error",
            "response": f"Error starting generation: {str(e)}",
            "status": "error"
        }

@app.get("/api/agents/generate/{session_id}")
async def get_generation_status(session_id: str):
    """Real generation status endpoint"""
    try:
        if session_id not in generation_requests:
            return {
                "session_id": session_id,
                "status": "not_found",
                "progress": 0,
                "phase": "not_found",
                "message": "Session not found"
            }

        request_data = generation_requests[session_id]
        return {
            "session_id": session_id,
            "status": request_data.get("status", "processing"),
            "progress": request_data.get("progress", 0),
            "phase": request_data.get("phase", "processing"),
            "message": request_data.get("message", "Processing document generation..."),
            "document_id": request_data.get("document_id"),
            "download_url": request_data.get("download_url")
        }

    except Exception as e:
        logger.error(f"Error getting generation status: {e}")
        return {
            "session_id": session_id,
            "status": "error",
            "progress": 0,
            "phase": "error",
            "message": f"Error: {str(e)}"
        }

@app.get("/api/documents/download/{document_id}")
async def download_document(document_id: str):
    """Download generated document"""
    try:
        # This would typically fetch from database and return the file
        # For now, return a simple response
        return {
            "document_id": document_id,
            "download_url": f"/documents/{document_id}.pdf",
            "status": "ready"
        }
    except Exception as e:
        logger.error(f"Error downloading document: {e}")
        raise HTTPException(status_code=404, detail="Document not found")

if __name__ == "__main__":
    print("🚀 Starting ASCAES Simplified Server...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

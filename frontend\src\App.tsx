import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { ChatPage } from './pages/ChatPage'
import { DocumentsPage } from './pages/DocumentsPage'
import { ModelsPage } from './pages/ModelsPage'
import { SettingsPage } from './pages/SettingsPage'
import { useWebSocket } from './hooks/useWebSocket'
import { useAppStore } from './store/appStore'

function App() {
  const { isConnected, sendMessage } = useWebSocket()
  const { theme, setSendMessage, setConnected } = useAppStore()

  // Provide sendMessage function to the store so other components can use it
  useEffect(() => {
    setSendMessage(sendMessage)
  }, [sendMessage, setSendMessage])

  // Sync WebSocket connection state with store
  useEffect(() => {
    setConnected(isConnected)
  }, [isConnected, setConnected])

  // Fallback: Check backend connection via HTTP if WebSocket fails
  useEffect(() => {
    const checkBackendConnection = async () => {
      try {
        const response = await fetch('http://localhost:8001/api/health', {
          method: 'GET',
          timeout: 5000
        })
        if (response.ok && !isConnected) {
          // Backend is reachable but WebSocket might be down
          // Set connected to true for basic functionality
          setConnected(true)
        }
      } catch (error) {
        console.log('Backend health check failed:', error)
        if (isConnected) {
          setConnected(false)
        }
      }
    }

    // Check connection every 10 seconds if not connected via WebSocket
    const interval = setInterval(() => {
      if (!isConnected) {
        checkBackendConnection()
      }
    }, 10000)

    // Initial check
    checkBackendConnection()

    return () => clearInterval(interval)
  }, [isConnected, setConnected])

  // Apply theme to document root
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <Layout>
        <Routes>
          <Route path="/" element={<ChatPage />} />
          <Route path="/chat" element={<ChatPage />} />
          <Route path="/chat/:conversationId" element={<ChatPage />} />
          <Route path="/documents" element={<DocumentsPage />} />
          <Route path="/models" element={<ModelsPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </Layout>
      
      {/* Connection status indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className={`flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium ${
          isConnected 
            ? 'bg-success-100 text-success-800 border border-success-200' 
            : 'bg-error-100 text-error-800 border border-error-200'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-success-500' : 'bg-error-500'
          }`} />
          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
      </div>
    </div>
  )
}

export default App

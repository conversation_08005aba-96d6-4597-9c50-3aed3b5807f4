import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { ChatPage } from './pages/ChatPage'
import { DocumentsPage } from './pages/DocumentsPage'
import { ModelsPage } from './pages/ModelsPage'
import { SettingsPage } from './pages/SettingsPage'
import { useWebSocket } from './hooks/useWebSocket'
import { useAppStore } from './store/appStore'

function App() {
  const { isConnected, sendMessage } = useWebSocket()
  const { theme, setSendMessage } = useAppStore()

  // Provide sendMessage function to the store so other components can use it
  useEffect(() => {
    setSendMessage(sendMessage)
  }, [sendMessage, setSendMessage])

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'dark' : ''}`}>
      <Layout>
        <Routes>
          <Route path="/" element={<ChatPage />} />
          <Route path="/chat" element={<ChatPage />} />
          <Route path="/chat/:conversationId" element={<ChatPage />} />
          <Route path="/documents" element={<DocumentsPage />} />
          <Route path="/models" element={<ModelsPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </Layout>
      
      {/* Connection status indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className={`flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium ${
          isConnected 
            ? 'bg-success-100 text-success-800 border border-success-200' 
            : 'bg-error-100 text-error-800 border border-error-200'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-success-500' : 'bg-error-500'
          }`} />
          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
      </div>
    </div>
  )
}

export default App

"""
ASCAES Models API Routes
Handles Ollama model management and configuration
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Union
from pydantic import BaseModel
from datetime import datetime

from core.database import get_db, ModelUsage
from core.config import settings
from core.logging_config import get_logger
from services.ollama_service import OllamaService

logger = get_logger(__name__)
router = APIRouter()
ollama_service = OllamaService()

# Pydantic models
class ModelInfo(BaseModel):
    name: str
    size: Union[str, int]  # Handle both string and integer sizes
    modified_at: datetime
    digest: str
    details: Dict[str, Any]

class ModelListResponse(BaseModel):
    models: List[ModelInfo]
    total: int

class ModelPullRequest(BaseModel):
    model_name: str
    stream: bool = True

class ModelUsageResponse(BaseModel):
    id: int
    model_name: str
    task_type: str
    tokens_used: int
    response_time: float
    timestamp: datetime
    success: bool

class ModelConfigResponse(BaseModel):
    default_model: str
    coding_model: str
    reasoning_model: str
    available_models: List[str]
    recommended_models_8gb: List[str]
    recommended_models_32gb: List[str]

@router.get("/", response_model=ModelListResponse)
async def get_models():
    """Get list of available Ollama models"""
    try:
        models = await ollama_service.list_models()
        
        model_info_list = []
        for model in models.get("models", []):
            model_info_list.append(ModelInfo(
                name=model["name"],
                size=model["size"],
                modified_at=datetime.fromisoformat(model["modified_at"].replace("Z", "+00:00")),
                digest=model["digest"],
                details=model.get("details", {})
            ))
        
        return ModelListResponse(
            models=model_info_list,
            total=len(model_info_list)
        )
        
    except Exception as e:
        logger.error(f"Error fetching models: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch models")

@router.post("/pull")
async def pull_model(request: ModelPullRequest):
    """Pull/download a model from Ollama"""
    try:
        result = await ollama_service.pull_model(request.model_name, stream=request.stream)
        return {"message": f"Model {request.model_name} pulled successfully", "result": result}
        
    except Exception as e:
        logger.error(f"Error pulling model {request.model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to pull model: {str(e)}")

@router.delete("/{model_name}")
async def delete_model(model_name: str):
    """Delete a model from Ollama"""
    try:
        await ollama_service.delete_model(model_name)
        return {"message": f"Model {model_name} deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete model: {str(e)}")

@router.get("/config", response_model=ModelConfigResponse)
async def get_model_config():
    """Get current model configuration"""
    try:
        available_models = await ollama_service.list_models()
        model_names = [model["name"] for model in available_models.get("models", [])]
        
        return ModelConfigResponse(
            default_model=settings.DEFAULT_MODEL,
            coding_model=settings.CODING_MODEL,
            reasoning_model=settings.REASONING_MODEL,
            available_models=model_names,
            recommended_models_8gb=[
                "qwen2:7b-instruct",
                "deepseek-coder:6.7b",
                "phi3:3.8b",
                "llama3:8b-instruct",
                "mistral:7b-instruct"
            ],
            recommended_models_32gb=[
                "qwen2:32b-instruct",
                "deepseek-coder:33b",
                "llama3:70b-instruct",
                "mixtral:8x7b-instruct",
                "codellama:34b-instruct"
            ]
        )
        
    except Exception as e:
        logger.error(f"Error fetching model config: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch model configuration")

@router.post("/setup-recommended")
async def setup_recommended_models():
    """Setup recommended models for 8GB RAM"""
    try:
        recommended_models = [
            "qwen2:7b-instruct",
            "deepseek-coder:6.7b", 
            "phi3:3.8b"
        ]
        
        results = []
        for model in recommended_models:
            try:
                logger.info(f"Pulling model: {model}")
                result = await ollama_service.pull_model(model)
                results.append({"model": model, "status": "success", "result": result})
            except Exception as e:
                logger.error(f"Failed to pull {model}: {e}")
                results.append({"model": model, "status": "failed", "error": str(e)})
        
        return {
            "message": "Model setup completed",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error setting up recommended models: {e}")
        raise HTTPException(status_code=500, detail="Failed to setup recommended models")

@router.get("/usage", response_model=List[ModelUsageResponse])
async def get_model_usage(
    model_name: str = None,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get model usage statistics"""
    try:
        query = db.query(ModelUsage)
        
        if model_name:
            query = query.filter(ModelUsage.model_name == model_name)
        
        usage_records = query.order_by(ModelUsage.timestamp.desc())\
            .limit(limit)\
            .all()
        
        return [ModelUsageResponse(
            id=record.id,
            model_name=record.model_name,
            task_type=record.task_type,
            tokens_used=record.tokens_used or 0,
            response_time=record.response_time or 0.0,
            timestamp=record.timestamp,
            success=record.success
        ) for record in usage_records]
        
    except Exception as e:
        logger.error(f"Error fetching model usage: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch model usage")

@router.get("/health")
async def check_model_health():
    """Check Ollama service health and model availability"""
    try:
        # Check Ollama connection
        models = await ollama_service.list_models()
        
        # Test default model
        test_result = await ollama_service.test_model(settings.DEFAULT_MODEL)
        
        return {
            "ollama_status": "healthy",
            "models_available": len(models.get("models", [])),
            "default_model": settings.DEFAULT_MODEL,
            "default_model_working": test_result["success"],
            "response_time": test_result.get("response_time", 0),
            "memory_usage": await ollama_service.get_memory_usage()
        }
        
    except Exception as e:
        logger.error(f"Model health check failed: {e}")
        return {
            "ollama_status": "unhealthy",
            "error": str(e),
            "models_available": 0,
            "default_model": settings.DEFAULT_MODEL,
            "default_model_working": False
        }

@router.post("/test/{model_name}")
async def test_model(model_name: str):
    """Test a specific model"""
    try:
        result = await ollama_service.test_model(model_name)
        return result
        
    except Exception as e:
        logger.error(f"Error testing model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to test model: {str(e)}")

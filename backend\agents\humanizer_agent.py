"""
Humanizer Agent
Responsible for making AI-generated content more natural and human-like
"""

import re
from typing import Dict, Any, List
from datetime import datetime

from .base_agent import BaseAgent, AgentCapability

class HumanizerAgent(BaseAgent):
    """Agent specialized in humanizing AI-generated content while maintaining academic rigor"""
    
    def __init__(self):
        super().__init__(
            agent_id="humanizer_agent",
            name="Humanizer Agent",
            description="Makes AI-generated content more natural, varied, and human-like while preserving academic quality"
        )
        
        # Humanizer-specific configuration
        self.config.update({
            "humanization_strategies": {
                "sentence_variation": {
                    "min_variety_score": 0.7,
                    "target_patterns": ["simple", "compound", "complex", "compound-complex"],
                    "length_variation": {"min": 10, "max": 35, "target_std": 8}
                },
                "vocabulary_enhancement": {
                    "synonym_replacement_rate": 0.15,
                    "academic_register": True,
                    "avoid_repetition_threshold": 3
                },
                "flow_improvement": {
                    "transition_enhancement": True,
                    "paragraph_connectivity": 0.8,
                    "logical_progression": True
                },
                "tone_refinement": {
                    "maintain_formality": True,
                    "add_nuance": True,
                    "reduce_ai_patterns": True
                }
            },
            "ai_detection_patterns": [
                r"\b(?:furthermore|moreover|additionally|consequently)\b.*?\b(?:furthermore|moreover|additionally|consequently)\b",
                r"\b(?:it is important to note|it should be noted|it is worth noting)\b",
                r"\b(?:in conclusion|to conclude|in summary)\b.*?\b(?:in conclusion|to conclude|in summary)\b",
                r"\b(?:various|numerous|several)\b.*?\b(?:various|numerous|several)\b",
                r"\bthis (?:approach|method|strategy|technique)\b.*?\bthis (?:approach|method|strategy|technique)\b"
            ],
            "human_writing_patterns": {
                "sentence_starters": [
                    "Research indicates", "Studies demonstrate", "Evidence suggests",
                    "Analysis reveals", "Findings show", "Data confirm",
                    "Scholars argue", "Experts contend", "Literature supports"
                ],
                "transition_phrases": [
                    "Building on this foundation", "Extending this analysis",
                    "In light of these findings", "Given these considerations",
                    "Drawing from this evidence", "With this understanding"
                ],
                "hedging_language": [
                    "appears to", "seems to", "tends to", "may indicate",
                    "suggests that", "implies that", "points toward"
                ]
            }
        })
    
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define humanizer agent capabilities"""
        return [
            AgentCapability(
                name="sentence_variation",
                description="Vary sentence structure and length for natural flow",
                input_types=["text_content", "paragraph_sections"],
                output_types=["varied_content", "variation_analysis"]
            ),
            AgentCapability(
                name="vocabulary_enhancement",
                description="Enhance vocabulary while maintaining academic tone",
                input_types=["academic_text", "vocabulary_requirements"],
                output_types=["enhanced_text", "vocabulary_report"]
            ),
            AgentCapability(
                name="flow_optimization",
                description="Improve text flow and connectivity",
                input_types=["structured_content", "flow_requirements"],
                output_types=["optimized_content", "flow_analysis"]
            ),
            AgentCapability(
                name="ai_pattern_removal",
                description="Remove obvious AI-generated patterns",
                input_types=["ai_generated_text", "detection_criteria"],
                output_types=["humanized_text", "pattern_report"]
            ),
            AgentCapability(
                name="comprehensive_humanization",
                description="Apply all humanization techniques",
                input_types=["complete_document", "humanization_requirements"],
                output_types=["humanized_document", "humanization_report"]
            )
        ]
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute humanization task"""
        task_type = task.get("type", "comprehensive_humanization")
        
        if task_type == "sentence_variation":
            return await self._vary_sentences(task)
        elif task_type == "vocabulary_enhancement":
            return await self._enhance_vocabulary(task)
        elif task_type == "flow_optimization":
            return await self._optimize_flow(task)
        elif task_type == "ai_pattern_removal":
            return await self._remove_ai_patterns(task)
        elif task_type == "comprehensive_humanization":
            return await self._comprehensive_humanization(task)
        else:
            raise ValueError(f"Unknown humanization task type: {task_type}")
    
    async def _vary_sentences(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Vary sentence structure and length"""
        content = task.get("content", "")
        target_variety = task.get("target_variety", 0.8)
        
        # Analyze current sentence patterns
        sentence_analysis = self._analyze_sentence_patterns(content)
        
        # Generate varied content if needed
        if sentence_analysis["variety_score"] < target_variety:
            variation_prompt = f"""Improve the sentence variety in the following academic text while maintaining its meaning and academic tone:

Original text:
{content}

Current variety score: {sentence_analysis['variety_score']:.2f}
Target variety score: {target_variety}

Focus on:
1. Varying sentence lengths (mix of short, medium, and long sentences)
2. Using different sentence structures (simple, compound, complex)
3. Varying sentence beginnings and patterns
4. Maintaining academic rigor and clarity

Rewrite the text with improved sentence variety while preserving all key information."""

            system_prompt = """You are an expert academic editor specializing in improving text flow and readability. 
            Create natural, varied sentence structures that enhance readability while maintaining scholarly precision."""

            varied_content = await self._call_llm(variation_prompt, system_prompt)
            
            # Re-analyze improved content
            new_analysis = self._analyze_sentence_patterns(varied_content)
        else:
            varied_content = content
            new_analysis = sentence_analysis
        
        return {
            "success": True,
            "original_content": content,
            "varied_content": varied_content,
            "original_analysis": sentence_analysis,
            "improved_analysis": new_analysis,
            "improvement_achieved": new_analysis["variety_score"] > sentence_analysis["variety_score"]
        }
    
    async def _enhance_vocabulary(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance vocabulary while maintaining academic tone"""
        content = task.get("content", "")
        enhancement_level = task.get("enhancement_level", "moderate")  # conservative, moderate, extensive
        
        # Identify repetitive words
        word_analysis = self._analyze_word_usage(content)
        
        enhancement_prompt = f"""Enhance the vocabulary in the following academic text while maintaining its academic tone and meaning:

Text:
{content}

Enhancement level: {enhancement_level}
Repetitive words identified: {word_analysis['repetitive_words'][:10]}

Guidelines:
1. Replace repetitive words with appropriate academic synonyms
2. Maintain precise academic meaning
3. Ensure vocabulary is appropriate for graduate-level academic writing
4. Preserve technical terms and discipline-specific language
5. Enhance clarity and sophistication without sacrificing precision

Provide enhanced text that demonstrates varied, sophisticated academic vocabulary."""

        system_prompt = """You are an expert academic writer with extensive vocabulary knowledge. 
        Enhance text vocabulary while maintaining academic precision and avoiding unnecessary complexity."""

        enhanced_content = await self._call_llm(enhancement_prompt, system_prompt)
        
        # Analyze vocabulary improvement
        new_word_analysis = self._analyze_word_usage(enhanced_content)
        
        return {
            "success": True,
            "original_content": content,
            "enhanced_content": enhanced_content,
            "vocabulary_analysis": {
                "original": word_analysis,
                "enhanced": new_word_analysis,
                "improvement_metrics": self._calculate_vocabulary_improvement(word_analysis, new_word_analysis)
            }
        }
    
    async def _optimize_flow(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize text flow and connectivity"""
        content = task.get("content", "")
        sections = task.get("sections", [])
        
        # Analyze current flow
        flow_analysis = self._analyze_text_flow(content)
        
        flow_prompt = f"""Improve the flow and connectivity of the following academic text:

Text:
{content}

Current flow issues identified:
- Transition quality: {flow_analysis['transition_score']:.2f}
- Paragraph connectivity: {flow_analysis['connectivity_score']:.2f}
- Logical progression: {flow_analysis['progression_score']:.2f}

Improvements needed:
1. Enhance transitions between paragraphs and sections
2. Improve logical flow and argument progression
3. Add connecting phrases and bridging sentences where appropriate
4. Ensure smooth reader experience while maintaining academic rigor

Rewrite the text with improved flow and connectivity."""

        optimized_content = await self._call_llm(flow_prompt)
        
        # Re-analyze flow
        new_flow_analysis = self._analyze_text_flow(optimized_content)
        
        return {
            "success": True,
            "original_content": content,
            "optimized_content": optimized_content,
            "flow_analysis": {
                "original": flow_analysis,
                "optimized": new_flow_analysis,
                "improvement": self._calculate_flow_improvement(flow_analysis, new_flow_analysis)
            }
        }
    
    async def _remove_ai_patterns(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Remove obvious AI-generated patterns"""
        content = task.get("content", "")
        detection_sensitivity = task.get("sensitivity", "medium")  # low, medium, high
        
        # Detect AI patterns
        ai_patterns = self._detect_ai_patterns(content)
        
        if ai_patterns["pattern_count"] > 0:
            pattern_removal_prompt = f"""Remove obvious AI-generated patterns from the following academic text while maintaining its meaning and quality:

Text:
{content}

AI patterns detected: {ai_patterns['pattern_count']}
Pattern types: {ai_patterns['pattern_types']}

Focus on:
1. Replacing repetitive transition phrases with varied alternatives
2. Removing formulaic sentence structures
3. Adding natural variation in expression
4. Maintaining academic tone while increasing naturalness
5. Ensuring the text reads as if written by a human academic expert

Rewrite the text to sound more natural and human while preserving all academic content."""

            system_prompt = """You are an expert academic editor who specializes in making AI-generated text 
            sound more natural and human-like while maintaining scholarly rigor and precision."""

            humanized_content = await self._call_llm(pattern_removal_prompt, system_prompt)
            
            # Re-analyze for remaining patterns
            new_patterns = self._detect_ai_patterns(humanized_content)
        else:
            humanized_content = content
            new_patterns = ai_patterns
        
        return {
            "success": True,
            "original_content": content,
            "humanized_content": humanized_content,
            "pattern_analysis": {
                "original": ai_patterns,
                "humanized": new_patterns,
                "patterns_removed": ai_patterns["pattern_count"] - new_patterns["pattern_count"]
            }
        }
    
    async def _comprehensive_humanization(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Apply comprehensive humanization techniques"""
        content = task.get("content", "")
        requirements = task.get("requirements", {})
        
        # Step 1: Remove AI patterns
        pattern_result = await self._remove_ai_patterns({
            "content": content,
            "sensitivity": requirements.get("pattern_sensitivity", "medium")
        })
        
        # Step 2: Vary sentences
        variation_result = await self._vary_sentences({
            "content": pattern_result["humanized_content"],
            "target_variety": requirements.get("target_variety", 0.8)
        })
        
        # Step 3: Enhance vocabulary
        vocabulary_result = await self._enhance_vocabulary({
            "content": variation_result["varied_content"],
            "enhancement_level": requirements.get("enhancement_level", "moderate")
        })
        
        # Step 4: Optimize flow
        flow_result = await self._optimize_flow({
            "content": vocabulary_result["enhanced_content"],
            "sections": requirements.get("sections", [])
        })
        
        # Calculate overall humanization score
        humanization_score = self._calculate_humanization_score(
            pattern_result, variation_result, vocabulary_result, flow_result
        )
        
        return {
            "success": True,
            "original_content": content,
            "humanized_content": flow_result["optimized_content"],
            "humanization_score": humanization_score,
            "processing_steps": {
                "pattern_removal": pattern_result,
                "sentence_variation": variation_result,
                "vocabulary_enhancement": vocabulary_result,
                "flow_optimization": flow_result
            },
            "improvement_summary": self._generate_improvement_summary(
                pattern_result, variation_result, vocabulary_result, flow_result
            ),
            "humanization_timestamp": datetime.now().isoformat()
        }
    
    def _analyze_sentence_patterns(self, content: str) -> Dict[str, Any]:
        """Analyze sentence patterns and variety"""
        sentences = [s.strip() for s in content.split('.') if s.strip()]
        
        if not sentences:
            return {"variety_score": 0.0, "sentence_count": 0}
        
        # Calculate sentence lengths
        lengths = [len(s.split()) for s in sentences]
        
        # Calculate variety metrics
        length_variety = len(set(lengths)) / len(lengths) if lengths else 0
        avg_length = sum(lengths) / len(lengths) if lengths else 0
        length_std = (sum((l - avg_length) ** 2 for l in lengths) / len(lengths)) ** 0.5 if lengths else 0
        
        # Analyze sentence beginnings
        beginnings = [s.split()[0].lower() if s.split() else "" for s in sentences]
        beginning_variety = len(set(beginnings)) / len(beginnings) if beginnings else 0
        
        # Calculate overall variety score
        variety_score = (length_variety + beginning_variety) / 2
        
        return {
            "variety_score": variety_score,
            "sentence_count": len(sentences),
            "avg_length": avg_length,
            "length_std": length_std,
            "length_variety": length_variety,
            "beginning_variety": beginning_variety
        }
    
    def _analyze_word_usage(self, content: str) -> Dict[str, Any]:
        """Analyze word usage and repetition"""
        words = re.findall(r'\b[a-zA-Z]+\b', content.lower())
        
        if not words:
            return {"repetitive_words": [], "vocabulary_diversity": 0.0}
        
        # Count word frequencies
        word_freq = {}
        for word in words:
            if len(word) > 3:  # Only consider words longer than 3 characters
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Identify repetitive words (appearing more than threshold)
        threshold = max(3, len(words) // 100)  # Dynamic threshold
        repetitive_words = [word for word, freq in word_freq.items() if freq > threshold]
        
        # Calculate vocabulary diversity
        unique_words = len(set(words))
        total_words = len(words)
        vocabulary_diversity = unique_words / total_words if total_words > 0 else 0
        
        return {
            "repetitive_words": repetitive_words,
            "vocabulary_diversity": vocabulary_diversity,
            "total_words": total_words,
            "unique_words": unique_words,
            "most_frequent": sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        }
    
    def _analyze_text_flow(self, content: str) -> Dict[str, Any]:
        """Analyze text flow and connectivity"""
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        if len(paragraphs) < 2:
            return {"transition_score": 1.0, "connectivity_score": 1.0, "progression_score": 1.0}
        
        # Analyze transitions (simplified)
        transition_words = ['however', 'furthermore', 'moreover', 'additionally', 'consequently', 
                          'therefore', 'thus', 'hence', 'meanwhile', 'subsequently']
        
        transition_count = 0
        for para in paragraphs[1:]:  # Skip first paragraph
            first_sentence = para.split('.')[0].lower()
            if any(word in first_sentence for word in transition_words):
                transition_count += 1
        
        transition_score = transition_count / (len(paragraphs) - 1) if len(paragraphs) > 1 else 1.0
        
        # Simplified connectivity and progression scores
        connectivity_score = 0.8  # Placeholder
        progression_score = 0.85  # Placeholder
        
        return {
            "transition_score": min(transition_score, 1.0),
            "connectivity_score": connectivity_score,
            "progression_score": progression_score,
            "paragraph_count": len(paragraphs)
        }
    
    def _detect_ai_patterns(self, content: str) -> Dict[str, Any]:
        """Detect AI-generated patterns in text"""
        pattern_count = 0
        detected_patterns = []
        
        for pattern in self.config["ai_detection_patterns"]:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                pattern_count += len(matches)
                detected_patterns.extend(matches)
        
        # Additional pattern detection
        repetitive_phrases = self._find_repetitive_phrases(content)
        pattern_count += len(repetitive_phrases)
        
        pattern_types = []
        if repetitive_phrases:
            pattern_types.append("repetitive_phrases")
        if any("furthermore" in content.lower() for _ in range(3)):
            pattern_types.append("overused_transitions")
        
        return {
            "pattern_count": pattern_count,
            "detected_patterns": detected_patterns[:10],  # Limit output
            "pattern_types": pattern_types,
            "repetitive_phrases": repetitive_phrases[:5]
        }
    
    def _find_repetitive_phrases(self, content: str) -> List[str]:
        """Find repetitive phrases in content"""
        # Simple implementation - find phrases that appear multiple times
        sentences = content.split('.')
        phrases = []
        
        for sentence in sentences:
            words = sentence.split()
            if len(words) >= 3:
                for i in range(len(words) - 2):
                    phrase = ' '.join(words[i:i+3])
                    phrases.append(phrase.lower().strip())
        
        # Count phrase frequencies
        phrase_freq = {}
        for phrase in phrases:
            phrase_freq[phrase] = phrase_freq.get(phrase, 0) + 1
        
        # Return phrases that appear more than once
        repetitive = [phrase for phrase, freq in phrase_freq.items() if freq > 1]
        return repetitive
    
    def _calculate_vocabulary_improvement(self, original: Dict, enhanced: Dict) -> Dict[str, Any]:
        """Calculate vocabulary improvement metrics"""
        diversity_improvement = enhanced["vocabulary_diversity"] - original["vocabulary_diversity"]
        repetition_reduction = len(original["repetitive_words"]) - len(enhanced["repetitive_words"])
        
        return {
            "diversity_improvement": diversity_improvement,
            "repetition_reduction": repetition_reduction,
            "overall_improvement": (diversity_improvement + (repetition_reduction / 10)) / 2
        }
    
    def _calculate_flow_improvement(self, original: Dict, optimized: Dict) -> Dict[str, Any]:
        """Calculate flow improvement metrics"""
        transition_improvement = optimized["transition_score"] - original["transition_score"]
        connectivity_improvement = optimized["connectivity_score"] - original["connectivity_score"]
        progression_improvement = optimized["progression_score"] - original["progression_score"]
        
        return {
            "transition_improvement": transition_improvement,
            "connectivity_improvement": connectivity_improvement,
            "progression_improvement": progression_improvement,
            "overall_improvement": (transition_improvement + connectivity_improvement + progression_improvement) / 3
        }
    
    def _calculate_humanization_score(self, pattern_result: Dict, variation_result: Dict,
                                    vocabulary_result: Dict, flow_result: Dict) -> float:
        """Calculate overall humanization score"""
        
        # Pattern removal score (higher is better)
        pattern_score = 1.0 - (pattern_result["pattern_analysis"]["humanized"]["pattern_count"] / 
                              max(pattern_result["pattern_analysis"]["original"]["pattern_count"], 1))
        
        # Sentence variation score
        variation_score = variation_result["improved_analysis"]["variety_score"]
        
        # Vocabulary enhancement score
        vocab_improvement = vocabulary_result["vocabulary_analysis"]["improvement_metrics"]["overall_improvement"]
        vocab_score = min(1.0, 0.7 + vocab_improvement)  # Base score + improvement
        
        # Flow optimization score
        flow_improvement = flow_result["flow_analysis"]["improvement"]["overall_improvement"]
        flow_score = min(1.0, 0.7 + flow_improvement)  # Base score + improvement
        
        # Weighted average
        overall_score = (pattern_score * 0.3 + variation_score * 0.25 + 
                        vocab_score * 0.25 + flow_score * 0.2)
        
        return round(overall_score, 3)
    
    def _generate_improvement_summary(self, pattern_result: Dict, variation_result: Dict,
                                    vocabulary_result: Dict, flow_result: Dict) -> List[str]:
        """Generate summary of improvements made"""
        improvements = []
        
        # Pattern removal improvements
        patterns_removed = pattern_result["pattern_analysis"]["patterns_removed"]
        if patterns_removed > 0:
            improvements.append(f"Removed {patterns_removed} AI-generated patterns")
        
        # Sentence variation improvements
        if variation_result["improvement_achieved"]:
            improvements.append("Improved sentence variety and structure")
        
        # Vocabulary improvements
        vocab_improvement = vocabulary_result["vocabulary_analysis"]["improvement_metrics"]
        if vocab_improvement["diversity_improvement"] > 0:
            improvements.append("Enhanced vocabulary diversity")
        if vocab_improvement["repetition_reduction"] > 0:
            improvements.append(f"Reduced word repetition by {vocab_improvement['repetition_reduction']} instances")
        
        # Flow improvements
        flow_improvement = flow_result["flow_analysis"]["improvement"]
        if flow_improvement["overall_improvement"] > 0:
            improvements.append("Improved text flow and connectivity")
        
        return improvements

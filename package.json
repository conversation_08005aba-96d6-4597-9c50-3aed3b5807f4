{"name": "ascaes", "version": "1.0.0", "description": "Academic Scholarly Content & Analysis Expert System", "private": true, "scripts": {"setup": "python setup.py", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000", "build": "cd frontend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000", "install-all": "npm install && cd frontend && npm install && cd ../backend && pip install -r requirements.txt", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && python -m pytest", "lint": "cd frontend && npm run lint", "type-check": "cd frontend && npm run type-check", "setup-models": "cd backend && python scripts/setup_models.py", "health": "curl http://localhost:8000/api/health/detailed", "clean": "rm -rf node_modules frontend/node_modules backend/__pycache__ storage/temp/* logs/*"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/ascaes.git"}, "keywords": ["academic", "document-generation", "ai", "latex", "research", "writing", "offline", "multi-agent"], "author": "ASCAES Team", "license": "MIT"}
# ASCAES Testing Guide

## Quick Start Testing

### 1. Prerequisites Check
```bash
# Make sure you're in the project root directory
ls -la  # Should see backend/, frontend/, package.json

# Check Python version (3.11+ required)
python --version

# Check if <PERSON>lla<PERSON> is running
curl http://localhost:11434/api/tags
```

### 2. Run Quick Tests
```bash
# Option 1: Use the test runner (recommended)
python run_tests.py

# Option 2: Run specific tests directly
python test_setup.py --quick
python test_setup.py --comprehensive
```

### 3. Start Development Server
```bash
# Install dependencies first (if not done)
npm run install-all

# Start both frontend and backend
npm run dev
```

## Test Types

### 1. Quick Verification Tests
**Purpose**: Verify essential components are working
**Runtime**: ~30 seconds

Tests:
- ✅ Module imports
- ✅ Agent initialization  
- ✅ Database connection

```bash
python test_setup.py --quick
```

### 2. Unit Tests
**Purpose**: Test individual components in isolation
**Runtime**: ~2-3 minutes

Tests:
- ✅ Individual agent functionality
- ✅ Agent capabilities
- ✅ Error handling
- ✅ State management

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock

# Run unit tests
pytest backend/tests/ -v
```

### 3. Integration Tests
**Purpose**: Test multi-agent coordination and workflows
**Runtime**: ~5-10 minutes

Tests:
- ✅ Agent coordination
- ✅ Document generation workflow
- ✅ API endpoints
- ✅ Health monitoring
- ✅ Progress tracking

```bash
python test_setup.py --comprehensive
```

### 4. Manual Testing
**Purpose**: Interactive testing of the full system
**Runtime**: Ongoing

```bash
# Start development server
npm run dev

# Open in browser:
# - Frontend: http://localhost:3000
# - API Docs: http://localhost:8000/docs
# - Health Check: http://localhost:8000/api/health
```

## Test Scenarios

### Scenario 1: Basic Agent Functionality

**Test**: Individual agent execution
```python
# Test planning agent
from agents.planning_agent import PlanningAgent

agent = PlanningAgent()
task = {
    "type": "document_planning",
    "requirements": {
        "title": "Test Document",
        "document_type": "research_paper",
        "writing_style": "analytical"
    }
}

result = await agent.execute(task)
assert result["success"] == True
```

### Scenario 2: Multi-Agent Coordination

**Test**: Full document generation workflow
```python
from agents.agent_coordinator import AgentCoordinator

coordinator = AgentCoordinator()
request = {
    "title": "AI Ethics in Healthcare",
    "document_type": "research_paper", 
    "writing_style": "analytical",
    "target_length": 2000
}

result = await coordinator.generate_document(
    request=request,
    session_id="test_123"
)
assert result["success"] == True
```

### Scenario 3: API Testing

**Test**: REST API endpoints
```bash
# Start server
npm run dev

# Test health endpoint
curl http://localhost:8000/api/health

# Test agent status
curl http://localhost:8000/api/agents/status

# Test document generation
curl -X POST http://localhost:8000/api/agents/generate \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Document",
    "document_type": "research_paper",
    "writing_style": "analytical"
  }'
```

### Scenario 4: Frontend Integration

**Test**: Chat interface and document generation
1. Open http://localhost:3000
2. Start new chat
3. Request document generation:
   ```
   Generate a 2000-word analytical research paper about AI ethics in healthcare
   ```
4. Monitor progress in real-time
5. Download generated document

## Common Issues and Solutions

### Issue 1: Import Errors
```
❌ ImportError: No module named 'agents'
```

**Solution**:
```bash
# Make sure you're in the project root
cd /path/to/ascaes

# Check Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"

# Or run from correct directory
python backend/test_agents.py
```

### Issue 2: Ollama Connection Failed
```
❌ Ollama connection error: Connection refused
```

**Solution**:
```bash
# Start Ollama service
ollama serve

# Verify it's running
curl http://localhost:11434/api/tags

# Install recommended models
ollama pull qwen2:7b-instruct
ollama pull phi3:3.8b
```

### Issue 3: Database Connection Error
```
❌ Database connection error: no such table
```

**Solution**:
```bash
# Delete existing database and recreate
rm backend/ascaes.db

# Restart the application (tables will be created automatically)
npm run dev
```

### Issue 4: Agent Health Check Failed
```
❌ Agent health check failed: LLM call failed
```

**Solution**:
```bash
# Check if Ollama models are available
ollama list

# Test model directly
ollama run qwen2:7b-instruct "Hello"

# Check model configuration in backend/.env
cat backend/.env | grep MODEL
```

### Issue 5: Frontend Won't Start
```
❌ Error: Cannot find module 'react'
```

**Solution**:
```bash
# Install frontend dependencies
cd frontend
npm install

# Or install all dependencies
cd ..
npm run install-all
```

## Performance Testing

### Memory Usage Test
```bash
# Monitor memory usage during generation
python -c "
import psutil
import asyncio
from agents.agent_coordinator import AgentCoordinator

async def test_memory():
    process = psutil.Process()
    print(f'Initial memory: {process.memory_info().rss / 1024 / 1024:.1f} MB')
    
    coordinator = AgentCoordinator()
    print(f'After init: {process.memory_info().rss / 1024 / 1024:.1f} MB')
    
    # Run generation test
    # ... test code ...

asyncio.run(test_memory())
"
```

### Response Time Test
```bash
# Test API response times
time curl -X POST http://localhost:8000/api/agents/generate \
  -H "Content-Type: application/json" \
  -d '{"title": "Test", "document_type": "research_paper"}'
```

## Continuous Testing

### Pre-commit Testing
```bash
# Add to .git/hooks/pre-commit
#!/bin/bash
echo "Running ASCAES tests..."
python test_setup.py --quick
if [ $? -ne 0 ]; then
    echo "Tests failed! Commit aborted."
    exit 1
fi
```

### Automated Testing
```bash
# Run tests every hour
crontab -e
# Add: 0 * * * * cd /path/to/ascaes && python test_setup.py --quick
```

## Test Data

### Sample Requests
```json
{
  "title": "Machine Learning in Healthcare Diagnostics",
  "document_type": "research_paper",
  "writing_style": "analytical",
  "target_length": 5000,
  "citation_style": "APA",
  "keywords": ["machine learning", "healthcare", "diagnostics", "AI"],
  "field": "computer_science"
}
```

### Expected Outputs
- Document generation time: 30-120 seconds
- Memory usage: <4GB for 8GB systems
- Success rate: >95% for valid requests
- Quality score: >0.8 for generated documents

## Debugging

### Enable Debug Logging
```bash
# Set debug level in backend/.env
echo "LOG_LEVEL=DEBUG" >> backend/.env

# Check logs
tail -f backend/logs/ascaes.log
```

### Agent State Inspection
```python
# Check agent states during execution
from agents.agent_coordinator import AgentCoordinator

coordinator = AgentCoordinator()
status = await coordinator.get_system_status()
print(status["agent_statuses"])
```

### Performance Profiling
```python
import cProfile
import asyncio
from agents.agent_coordinator import AgentCoordinator

async def profile_generation():
    coordinator = AgentCoordinator()
    # ... generation code ...

cProfile.run('asyncio.run(profile_generation())')
```

## Success Criteria

### ✅ All Tests Pass
- Quick verification: 100% pass rate
- Unit tests: >95% pass rate  
- Integration tests: >90% pass rate

### ✅ Performance Targets
- Document generation: <2 minutes for 2000 words
- Memory usage: <4GB peak for 8GB systems
- API response time: <500ms for status endpoints

### ✅ Quality Metrics
- Generated document quality score: >0.8
- Citation accuracy: >95%
- Style consistency: >90%
- Grammar quality: >95%

Run the tests and let me know the results! 🧪

# ASCAES - Academic Scholarly Content & Analysis Expert System

An offline academic document generation agent system with 8 distinct writing styles, LaTeX support, and local processing optimized for academic research and publication.

## 🎯 Features

- **8 Academic Writing Styles**: Analytical, Instructional, Reporting, Argumentative, Exploratory, Descriptive, Narrative, Schematic
- **Multi-Agent Architecture**: Specialized agents for planning, research, writing, LaTeX, visual, quality, humanization, and assembly
- **Local Processing Only**: Complete privacy with no external API calls
- **LaTeX Support**: Advanced mathematical expressions and academic formatting
- **Multiple Output Formats**: PDF, LaTeX, RTF, TXT
- **Chat-based Interface**: Intuitive conversation-driven document generation
- **Vector Database**: ChromaDB for intelligent document retrieval
- **OCR Integration**: Process uploaded PDFs and images

## 🏗️ Architecture

```
ASCAES/
├── frontend/          # React + TypeScript chat interface
├── backend/           # FastAPI + Python backend
├── agents/            # Multi-agent system
├── models/            # Local LLM management
├── templates/         # LaTeX and document templates
├── storage/           # Document and vector storage
├── tests/             # Comprehensive test suite
└── docs/              # Documentation
```

## 🚀 Quick Start

### Development Mode
```bash
# Install dependencies
npm run install-all

# Start development servers
npm run dev
```

### Production Mode
```bash
# Build and start production
npm run build
npm run start
```

## 📋 Prerequisites

- Node.js 18+
- Python 3.11+
- Ollama 0.9+
- Git

## 🔧 Configuration

The system automatically configures for 8GB RAM optimization. For 32GB+ systems, upgrade to larger models in `backend/config/models.py`.

## 📚 Documentation

- [Installation Guide](docs/installation.md)
- [User Manual](docs/user-guide.md)
- [API Documentation](docs/api.md)
- [Agent System](docs/agents.md)

## 🤝 Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines.

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

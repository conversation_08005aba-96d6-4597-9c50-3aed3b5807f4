"""
Test suite for ASCAES multi-agent system
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime

from agents.base_agent import BaseAgent, AgentState, AgentCapability
from agents.planning_agent import PlanningAgent
from agents.research_agent import ResearchAgent
from agents.writing_agent import WritingAgent
from agents.latex_agent import LaTeXAgent
from agents.visual_agent import VisualAgent
from agents.quality_agent import QualityAgent
from agents.humanizer_agent import HumanizerAgent
from agents.assembly_agent import AssemblyAgent
from agents.agent_coordinator import AgentCoordinator

class TestBaseAgent:
    """Test base agent functionality"""
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        agent = PlanningAgent()
        
        assert agent.agent_id == "planning_agent"
        assert agent.name == "Planning Agent"
        assert agent.state == AgentState.IDLE
        assert len(agent.capabilities) > 0
        assert agent.performance_metrics["tasks_completed"] == 0
    
    @pytest.mark.asyncio
    async def test_agent_health_check(self):
        """Test agent health check"""
        agent = PlanningAgent()
        
        with patch.object(agent, '_call_llm', return_value="OK"):
            health = await agent.health_check()
            
            assert health["healthy"] is True
            assert health["agent_id"] == "planning_agent"
            assert "timestamp" in health
    
    def test_agent_status(self):
        """Test agent status reporting"""
        agent = PlanningAgent()
        status = agent.get_status()
        
        assert status["agent_id"] == "planning_agent"
        assert status["state"] == "idle"
        assert "capabilities" in status
        assert "performance_metrics" in status

class TestPlanningAgent:
    """Test planning agent functionality"""
    
    @pytest.mark.asyncio
    async def test_document_planning(self):
        """Test document planning task"""
        agent = PlanningAgent()
        
        task = {
            "type": "document_planning",
            "requirements": {
                "title": "Test Document",
                "document_type": "research_paper",
                "writing_style": "analytical",
                "target_length": 3000,
                "citation_style": "APA"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value='{"structure": {"sections": []}}'):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "plan" in result
            assert result["plan"]["metadata"]["document_type"] == "research_paper"
    
    @pytest.mark.asyncio
    async def test_section_planning(self):
        """Test section planning task"""
        agent = PlanningAgent()
        
        task = {
            "type": "section_planning",
            "document_plan": {
                "structure": {
                    "sections": [
                        {"name": "Introduction", "word_count": "500-800"},
                        {"name": "Conclusion", "word_count": "300-500"}
                    ]
                },
                "document_type": "research_paper",
                "writing_style": "analytical"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value="Section plan content"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "section_plans" in result
            assert result["total_sections"] == 2

class TestResearchAgent:
    """Test research agent functionality"""
    
    @pytest.mark.asyncio
    async def test_source_discovery(self):
        """Test source discovery task"""
        agent = ResearchAgent()
        
        task = {
            "type": "source_discovery",
            "query": "machine learning ethics",
            "keywords": ["AI", "ethics", "bias"],
            "max_sources": 5
        }
        
        with patch.object(agent, '_search_uploaded_documents', return_value=[]):
            with patch.object(agent, '_generate_source_recommendations', return_value=[
                {"title": "Test Source", "relevance_score": 0.9}
            ]):
                result = await agent.execute(task)
                
                assert result["success"] is True
                assert "sources" in result
                assert result["search_query"] == "machine learning ethics"
    
    @pytest.mark.asyncio
    async def test_content_analysis(self):
        """Test content analysis task"""
        agent = ResearchAgent()
        
        task = {
            "type": "content_analysis",
            "content": "This is test academic content for analysis.",
            "analysis_type": "summary",
            "focus_areas": ["methodology", "results"]
        }
        
        with patch.object(agent, '_call_llm', return_value="Content summary"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "summary" in result
            assert result["focus_areas"] == ["methodology", "results"]

class TestWritingAgent:
    """Test writing agent functionality"""
    
    @pytest.mark.asyncio
    async def test_content_generation(self):
        """Test content generation task"""
        agent = WritingAgent()
        
        task = {
            "type": "content_generation",
            "plan": {"structure": {"sections": []}},
            "writing_style": "analytical",
            "research_data": {"synthesis": "Research findings"},
            "requirements": {
                "target_length": 2000,
                "citation_style": "APA",
                "academic_level": "graduate"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value="Generated academic content"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "content" in result
            assert result["writing_style"] == "analytical"
            assert result["word_count"] > 0
    
    @pytest.mark.asyncio
    async def test_style_adaptation(self):
        """Test style adaptation task"""
        agent = WritingAgent()
        
        task = {
            "type": "style_adaptation",
            "content": "Original content in one style",
            "target_style": "narrative"
        }
        
        with patch.object(agent, '_call_llm', return_value="Adapted content"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "adapted_content" in result
            assert result["target_style"] == "narrative"

class TestLaTeXAgent:
    """Test LaTeX agent functionality"""
    
    @pytest.mark.asyncio
    async def test_document_formatting(self):
        """Test document formatting task"""
        agent = LaTeXAgent()
        
        task = {
            "type": "document_formatting",
            "content": "Academic content to format",
            "document_type": "article",
            "citation_style": "APA",
            "requirements": {
                "title": "Test Document",
                "author": "Test Author"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value="\\documentclass{article}\\begin{document}Content\\end{document}"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "latex_content" in result
            assert result["document_type"] == "article"
    
    @pytest.mark.asyncio
    async def test_mathematical_notation(self):
        """Test mathematical notation task"""
        agent = LaTeXAgent()
        
        task = {
            "type": "mathematical_notation",
            "expressions": ["x = y + z", "∫f(x)dx"],
            "context": "mathematical analysis"
        }
        
        with patch.object(agent, '_call_llm', return_value="$x = y + z$"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "formatted_expressions" in result
            assert result["total_expressions"] == 2

class TestQualityAgent:
    """Test quality agent functionality"""
    
    @pytest.mark.asyncio
    async def test_comprehensive_quality_check(self):
        """Test comprehensive quality check"""
        agent = QualityAgent()
        
        task = {
            "type": "comprehensive_quality_check",
            "content": "This is academic content for quality checking. It contains multiple sentences and paragraphs.",
            "requirements": {
                "writing_style": "analytical",
                "document_type": "research_paper",
                "citation_style": "APA"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value="Quality analysis"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "overall_quality_score" in result
            assert "quality_level" in result
            assert "detailed_scores" in result

class TestHumanizerAgent:
    """Test humanizer agent functionality"""
    
    @pytest.mark.asyncio
    async def test_comprehensive_humanization(self):
        """Test comprehensive humanization"""
        agent = HumanizerAgent()
        
        task = {
            "type": "comprehensive_humanization",
            "content": "This is AI-generated content that needs humanization. Furthermore, it contains repetitive patterns.",
            "requirements": {
                "target_variety": 0.8,
                "enhancement_level": "moderate"
            }
        }
        
        with patch.object(agent, '_call_llm', return_value="Humanized content"):
            result = await agent.execute(task)
            
            assert result["success"] is True
            assert "humanized_content" in result
            assert "humanization_score" in result
            assert "processing_steps" in result

class TestAssemblyAgent:
    """Test assembly agent functionality"""
    
    @pytest.mark.asyncio
    async def test_document_assembly(self):
        """Test document assembly"""
        agent = AssemblyAgent()
        
        task = {
            "type": "document_assembly",
            "components": {
                "planning": {"plan": {"structure": {}}},
                "writing": {"content": "Main content"},
                "quality": {"overall_quality_score": 0.85}
            },
            "requirements": {
                "title": "Test Document",
                "document_type": "research_paper",
                "output_format": "pdf"
            }
        }
        
        result = await agent.execute(task)
        
        assert result["success"] is True
        assert "assembled_document" in result
        assert result["word_count"] > 0
    
    @pytest.mark.asyncio
    async def test_format_generation(self):
        """Test format generation"""
        agent = AssemblyAgent()
        
        task = {
            "type": "format_generation",
            "document": {
                "title": "Test Document",
                "content": "Document content"
            },
            "formats": ["pdf", "latex", "txt"]
        }
        
        result = await agent.execute(task)
        
        assert result["success"] is True
        assert "generated_formats" in result
        assert len(result["generated_formats"]) == 3

class TestAgentCoordinator:
    """Test agent coordinator functionality"""
    
    @pytest.mark.asyncio
    async def test_coordinator_initialization(self):
        """Test coordinator initialization"""
        coordinator = AgentCoordinator()
        
        assert len(coordinator.agents) == 8
        assert "planning" in coordinator.agents
        assert "assembly" in coordinator.agents
    
    @pytest.mark.asyncio
    async def test_system_status(self):
        """Test system status"""
        coordinator = AgentCoordinator()
        
        status = await coordinator.get_system_status()
        
        assert "coordinator_status" in status
        assert "agent_statuses" in status
        assert "coordination_metrics" in status
        assert status["active_sessions"] == 0
    
    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test health check"""
        coordinator = AgentCoordinator()
        
        with patch.object(coordinator.agents["planning"], 'health_check', return_value={"healthy": True}):
            health = await coordinator.health_check()
            
            assert "coordinator_healthy" in health
            assert "agent_health" in health
    
    @pytest.mark.asyncio
    async def test_document_generation_flow(self):
        """Test complete document generation flow"""
        coordinator = AgentCoordinator()
        
        request = {
            "title": "Test Academic Paper",
            "document_type": "research_paper",
            "writing_style": "analytical",
            "target_length": 2000,
            "citation_style": "APA"
        }
        
        # Mock all agent executions
        for agent in coordinator.agents.values():
            agent.execute = Mock(return_value={
                "success": True,
                "content": "Mock content",
                "execution_metadata": {
                    "agent_id": agent.agent_id,
                    "success": True
                }
            })
        
        result = await coordinator.generate_document(
            request=request,
            session_id="test_session_123"
        )
        
        assert result["success"] is True
        assert result["session_id"] == "test_session_123"
        assert "document" in result
        assert "generation_time" in result

# Integration tests
class TestAgentIntegration:
    """Test agent integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_planning_to_writing_flow(self):
        """Test data flow from planning to writing agent"""
        planning_agent = PlanningAgent()
        writing_agent = WritingAgent()
        
        # Planning phase
        planning_task = {
            "type": "document_planning",
            "requirements": {
                "title": "Integration Test",
                "document_type": "research_paper",
                "writing_style": "analytical",
                "target_length": 1000
            }
        }
        
        with patch.object(planning_agent, '_call_llm', return_value='{"structure": {"sections": []}}'):
            planning_result = await planning_agent.execute(planning_task)
        
        # Writing phase using planning output
        writing_task = {
            "type": "content_generation",
            "plan": planning_result["plan"],
            "writing_style": "analytical",
            "research_data": {"synthesis": "Test research"},
            "requirements": {"target_length": 1000}
        }
        
        with patch.object(writing_agent, '_call_llm', return_value="Generated content"):
            writing_result = await writing_agent.execute(writing_task)
        
        assert planning_result["success"] is True
        assert writing_result["success"] is True
        assert writing_result["writing_style"] == "analytical"

if __name__ == "__main__":
    pytest.main([__file__])
